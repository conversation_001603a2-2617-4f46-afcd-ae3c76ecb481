-- OCTI智能评估系统 - 数据库初始化脚本
-- 用于生产环境数据库的初始配置

-- 设置数据库编码和排序规则
ALTER DATABASE octi_db SET timezone TO 'UTC';
ALTER DATABASE octi_db SET log_statement TO 'all';
ALTER DATABASE octi_db SET log_min_duration_statement TO 1000;

-- 创建扩展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_stat_statements";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- 创建监控用户（只读权限）
CREATE USER monitoring_user WITH PASSWORD 'monitoring_password';
GRANT CONNECT ON DATABASE octi_db TO monitoring_user;
GRANT USAGE ON SCHEMA public TO monitoring_user;
GRANT SELECT ON ALL TABLES IN SCHEMA public TO monitoring_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT SELECT ON TABLES TO monitoring_user;

-- 创建备份用户
CREATE USER backup_user WITH PASSWORD 'backup_password';
GRANT CONNECT ON DATABASE octi_db TO backup_user;
GRANT USAGE ON SCHEMA public TO backup_user;
GRANT SELECT ON ALL TABLES IN SCHEMA public TO backup_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT SELECT ON TABLES TO backup_user;

-- 设置连接限制
ALTER USER octi_user CONNECTION LIMIT 50;
ALTER USER monitoring_user CONNECTION LIMIT 5;
ALTER USER backup_user CONNECTION LIMIT 2;

-- 创建性能监控视图
CREATE OR REPLACE VIEW performance_stats AS
SELECT 
    schemaname,
    tablename,
    attname,
    n_distinct,
    correlation
FROM pg_stats
WHERE schemaname = 'public';

-- 创建连接监控视图
CREATE OR REPLACE VIEW connection_stats AS
SELECT 
    datname,
    usename,
    client_addr,
    state,
    query_start,
    state_change,
    query
FROM pg_stat_activity
WHERE datname = 'octi_db';

-- 创建慢查询监控视图
CREATE OR REPLACE VIEW slow_queries AS
SELECT 
    query,
    calls,
    total_time,
    mean_time,
    rows,
    100.0 * shared_blks_hit / nullif(shared_blks_hit + shared_blks_read, 0) AS hit_percent
FROM pg_stat_statements
ORDER BY total_time DESC
LIMIT 20;

-- 设置日志配置
ALTER SYSTEM SET log_destination = 'stderr';
ALTER SYSTEM SET logging_collector = on;
ALTER SYSTEM SET log_directory = 'pg_log';
ALTER SYSTEM SET log_filename = 'postgresql-%Y-%m-%d_%H%M%S.log';
ALTER SYSTEM SET log_rotation_age = '1d';
ALTER SYSTEM SET log_rotation_size = '100MB';
ALTER SYSTEM SET log_min_messages = 'warning';
ALTER SYSTEM SET log_min_error_statement = 'error';
ALTER SYSTEM SET log_min_duration_statement = 1000;
ALTER SYSTEM SET log_checkpoints = on;
ALTER SYSTEM SET log_connections = on;
ALTER SYSTEM SET log_disconnections = on;
ALTER SYSTEM SET log_lock_waits = on;
ALTER SYSTEM SET log_temp_files = 0;

-- 性能优化配置
ALTER SYSTEM SET shared_preload_libraries = 'pg_stat_statements';
ALTER SYSTEM SET max_connections = 100;
ALTER SYSTEM SET shared_buffers = '256MB';
ALTER SYSTEM SET effective_cache_size = '1GB';
ALTER SYSTEM SET maintenance_work_mem = '64MB';
ALTER SYSTEM SET checkpoint_completion_target = 0.9;
ALTER SYSTEM SET wal_buffers = '16MB';
ALTER SYSTEM SET default_statistics_target = 100;
ALTER SYSTEM SET random_page_cost = 1.1;
ALTER SYSTEM SET effective_io_concurrency = 200;

-- 重新加载配置
SELECT pg_reload_conf();

-- 创建健康检查函数
CREATE OR REPLACE FUNCTION health_check()
RETURNS TABLE(
    status TEXT,
    connections INTEGER,
    active_queries INTEGER,
    database_size TEXT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        'healthy'::TEXT as status,
        (SELECT count(*)::INTEGER FROM pg_stat_activity WHERE datname = 'octi_db') as connections,
        (SELECT count(*)::INTEGER FROM pg_stat_activity WHERE datname = 'octi_db' AND state = 'active') as active_queries,
        (SELECT pg_size_pretty(pg_database_size('octi_db'))) as database_size;
END;
$$ LANGUAGE plpgsql;

-- 创建备份函数
CREATE OR REPLACE FUNCTION create_backup_info()
RETURNS TABLE(
    backup_time TIMESTAMP,
    database_size BIGINT,
    table_count INTEGER
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        NOW() as backup_time,
        pg_database_size('octi_db') as database_size,
        (SELECT count(*)::INTEGER FROM information_schema.tables WHERE table_schema = 'public') as table_count;
END;
$$ LANGUAGE plpgsql;

-- 授权监控用户访问监控函数
GRANT EXECUTE ON FUNCTION health_check() TO monitoring_user;
GRANT EXECUTE ON FUNCTION create_backup_info() TO backup_user;

-- 创建索引优化建议函数
CREATE OR REPLACE FUNCTION index_recommendations()
RETURNS TABLE(
    schemaname TEXT,
    tablename TEXT,
    attname TEXT,
    n_distinct REAL,
    correlation REAL,
    recommendation TEXT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        s.schemaname::TEXT,
        s.tablename::TEXT,
        s.attname::TEXT,
        s.n_distinct,
        s.correlation,
        CASE 
            WHEN s.n_distinct > 100 AND s.correlation < 0.1 THEN 'Consider adding index'
            WHEN s.n_distinct < 10 AND s.correlation > 0.9 THEN 'Consider removing index'
            ELSE 'No recommendation'
        END::TEXT as recommendation
    FROM pg_stats s
    WHERE s.schemaname = 'public'
    AND s.n_distinct IS NOT NULL;
END;
$$ LANGUAGE plpgsql;

GRANT EXECUTE ON FUNCTION index_recommendations() TO monitoring_user;

-- 输出初始化完成信息
DO $$
BEGIN
    RAISE NOTICE 'OCTI数据库初始化完成';
    RAISE NOTICE '数据库名称: octi_db';
    RAISE NOTICE '主用户: octi_user';
    RAISE NOTICE '监控用户: monitoring_user';
    RAISE NOTICE '备份用户: backup_user';
    RAISE NOTICE '扩展已安装: uuid-ossp, pg_stat_statements, pg_trgm';
END $$;