# OCTI智能评估系统 - Redis配置文件

# 网络配置
bind 0.0.0.0
port 6379
protected-mode yes
tcp-backlog 511
tcp-keepalive 300

# 通用配置
daemonize no
supervised no
pidfile /var/run/redis_6379.pid
loglevel notice
logfile ""
databases 16

# 安全配置
requirepass ${REDIS_PASSWORD}
# rename-command FLUSHDB ""
# rename-command FLUSHALL ""
# rename-command DEBUG ""

# 内存管理
maxmemory 256mb
maxmemory-policy allkeys-lru
maxmemory-samples 5

# 持久化配置
# RDB快照
save 900 1
save 300 10
save 60 10000
stop-writes-on-bgsave-error yes
rdbcompression yes
rdbchecksum yes
dbfilename dump.rdb
dir /data

# AOF持久化
appendonly yes
appendfilename "appendonly.aof"
appendfsync everysec
no-appendfsync-on-rewrite no
auto-aof-rewrite-percentage 100
auto-aof-rewrite-min-size 64mb
aof-load-truncated yes
aof-use-rdb-preamble yes

# 慢查询日志
slowlog-log-slower-than 10000
slowlog-max-len 128

# 延迟监控
latency-monitor-threshold 100

# 客户端配置
timeout 300
tcp-keepalive 300
maxclients 10000

# 高级配置
hash-max-ziplist-entries 512
hash-max-ziplist-value 64
list-max-ziplist-size -2
list-compress-depth 0
set-max-intset-entries 512
zset-max-ziplist-entries 128
zset-max-ziplist-value 64
hll-sparse-max-bytes 3000
stream-node-max-bytes 4096
stream-node-max-entries 100

# 活跃重新哈希
activerehashing yes

# 客户端输出缓冲区限制
client-output-buffer-limit normal 0 0 0
client-output-buffer-limit replica 256mb 64mb 60
client-output-buffer-limit pubsub 32mb 8mb 60

# 客户端查询缓冲区限制
client-query-buffer-limit 1gb

# 协议最大批量请求大小
proto-max-bulk-len 512mb

# HyperLogLog稀疏表示字节限制
hll-sparse-max-bytes 3000

# Streams宏节点最大字节数和条目数
stream-node-max-bytes 4096
stream-node-max-entries 100

# 活跃过期周期
hz 10

# 动态HZ
dynamic-hz yes

# AOF重写增量fsync
aof-rewrite-incremental-fsync yes

# RDB保存增量fsync
rdb-save-incremental-fsync yes

# LFU和LRU精度
lfu-log-factor 10
lfu-decay-time 1

# 通知配置
notify-keyspace-events ""

# 哈希槽迁移超时
cluster-migration-barrier 1
cluster-require-full-coverage yes