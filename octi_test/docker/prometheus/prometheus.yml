# OCTI智能评估系统 - Prometheus监控配置

global:
  scrape_interval: 15s
  evaluation_interval: 15s
  external_labels:
    monitor: 'octi-monitor'

# 告警规则文件
rule_files:
  - "alert_rules.yml"

# 告警管理器配置
alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

# 数据采集配置
scrape_configs:
  # Prometheus自身监控
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']
    scrape_interval: 30s
    metrics_path: /metrics

  # OCTI应用监控
  - job_name: 'octi-app'
    static_configs:
      - targets: ['app:3000']
    scrape_interval: 15s
    metrics_path: /api/metrics
    scrape_timeout: 10s
    honor_labels: true
    params:
      format: ['prometheus']

  # Node Exporter (系统指标)
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']
    scrape_interval: 30s

  # PostgreSQL监控
  - job_name: 'postgres-exporter'
    static_configs:
      - targets: ['postgres-exporter:9187']
    scrape_interval: 30s

  # Redis监控
  - job_name: 'redis-exporter'
    static_configs:
      - targets: ['redis-exporter:9121']
    scrape_interval: 30s

  # Nginx监控
  - job_name: 'nginx-exporter'
    static_configs:
      - targets: ['nginx-exporter:9113']
    scrape_interval: 30s

  # 应用健康检查
  - job_name: 'octi-health'
    static_configs:
      - targets: ['app:3000']
    scrape_interval: 10s
    metrics_path: /api/health
    scrape_timeout: 5s

# 远程写入配置 (可选，用于长期存储)
# remote_write:
#   - url: "https://your-remote-storage/api/v1/write"
#     basic_auth:
#       username: "your-username"
#       password: "your-password"