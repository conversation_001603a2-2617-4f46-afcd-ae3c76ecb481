# OCTI智能评估系统 - Prometheus告警规则
# 定义系统监控和告警条件

groups:
  # 应用服务告警
  - name: octi-app-alerts
    rules:
      # 应用服务不可用
      - alert: OCTIAppDown
        expr: up{job="octi-app"} == 0
        for: 1m
        labels:
          severity: critical
          service: octi-app
        annotations:
          summary: "OCTI应用服务不可用"
          description: "OCTI应用服务已停止响应超过1分钟"

      # 应用响应时间过长
      - alert: OCTIHighResponseTime
        expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket{job="octi-app"}[5m])) > 2
        for: 5m
        labels:
          severity: warning
          service: octi-app
        annotations:
          summary: "OCTI应用响应时间过长"
          description: "95%的请求响应时间超过2秒，当前值: {{ $value }}秒"

      # 错误率过高
      - alert: OCTIHighErrorRate
        expr: rate(http_requests_total{job="octi-app",status=~"5.."}[5m]) / rate(http_requests_total{job="octi-app"}[5m]) > 0.05
        for: 3m
        labels:
          severity: critical
          service: octi-app
        annotations:
          summary: "OCTI应用错误率过高"
          description: "应用5xx错误率超过5%，当前值: {{ $value | humanizePercentage }}"

      # API请求量异常
      - alert: OCTILowRequestRate
        expr: rate(http_requests_total{job="octi-app"}[10m]) < 0.1
        for: 10m
        labels:
          severity: warning
          service: octi-app
        annotations:
          summary: "OCTI API请求量异常低"
          description: "API请求量低于正常水平，可能存在问题"

  # 数据库告警
  - name: octi-database-alerts
    rules:
      # 数据库连接不可用
      - alert: PostgreSQLDown
        expr: up{job="postgres-exporter"} == 0
        for: 1m
        labels:
          severity: critical
          service: postgresql
        annotations:
          summary: "PostgreSQL数据库不可用"
          description: "PostgreSQL数据库连接失败超过1分钟"

      # 数据库连接数过高
      - alert: PostgreSQLTooManyConnections
        expr: pg_stat_database_numbackends / pg_settings_max_connections > 0.8
        for: 5m
        labels:
          severity: warning
          service: postgresql
        annotations:
          summary: "PostgreSQL连接数过高"
          description: "数据库连接数超过80%，当前: {{ $value | humanizePercentage }}"

      # 数据库查询时间过长
      - alert: PostgreSQLSlowQueries
        expr: pg_stat_activity_max_tx_duration > 300
        for: 2m
        labels:
          severity: warning
          service: postgresql
        annotations:
          summary: "PostgreSQL存在慢查询"
          description: "存在执行时间超过5分钟的查询"

      # 数据库磁盘空间不足
      - alert: PostgreSQLDiskSpaceLow
        expr: (pg_database_size_bytes / (1024^3)) > 8
        for: 5m
        labels:
          severity: warning
          service: postgresql
        annotations:
          summary: "PostgreSQL磁盘空间不足"
          description: "数据库大小超过8GB，当前: {{ $value }}GB"

  # Redis告警
  - name: octi-redis-alerts
    rules:
      # Redis服务不可用
      - alert: RedisDown
        expr: up{job="redis-exporter"} == 0
        for: 1m
        labels:
          severity: critical
          service: redis
        annotations:
          summary: "Redis服务不可用"
          description: "Redis服务连接失败超过1分钟"

      # Redis内存使用率过高
      - alert: RedisHighMemoryUsage
        expr: redis_memory_used_bytes / redis_memory_max_bytes > 0.9
        for: 5m
        labels:
          severity: warning
          service: redis
        annotations:
          summary: "Redis内存使用率过高"
          description: "Redis内存使用率超过90%，当前: {{ $value | humanizePercentage }}"

      # Redis连接数过高
      - alert: RedisTooManyConnections
        expr: redis_connected_clients > 100
        for: 5m
        labels:
          severity: warning
          service: redis
        annotations:
          summary: "Redis连接数过高"
          description: "Redis连接数超过100，当前: {{ $value }}"

  # 系统资源告警
  - name: octi-system-alerts
    rules:
      # CPU使用率过高
      - alert: HighCPUUsage
        expr: 100 - (avg by(instance) (irate(node_cpu_seconds_total{mode="idle"}[5m])) * 100) > 80
        for: 5m
        labels:
          severity: warning
          service: system
        annotations:
          summary: "系统CPU使用率过高"
          description: "CPU使用率超过80%，当前: {{ $value }}%"

      # 内存使用率过高
      - alert: HighMemoryUsage
        expr: (1 - (node_memory_MemAvailable_bytes / node_memory_MemTotal_bytes)) * 100 > 85
        for: 5m
        labels:
          severity: warning
          service: system
        annotations:
          summary: "系统内存使用率过高"
          description: "内存使用率超过85%，当前: {{ $value }}%"

      # 磁盘空间不足
      - alert: DiskSpaceLow
        expr: (1 - (node_filesystem_avail_bytes{fstype!="tmpfs"} / node_filesystem_size_bytes{fstype!="tmpfs"})) * 100 > 85
        for: 5m
        labels:
          severity: warning
          service: system
        annotations:
          summary: "磁盘空间不足"
          description: "磁盘使用率超过85%，挂载点: {{ $labels.mountpoint }}，当前: {{ $value }}%"

      # 磁盘IO等待时间过长
      - alert: HighDiskIOWait
        expr: irate(node_cpu_seconds_total{mode="iowait"}[5m]) * 100 > 20
        for: 5m
        labels:
          severity: warning
          service: system
        annotations:
          summary: "磁盘IO等待时间过长"
          description: "磁盘IO等待时间超过20%，当前: {{ $value }}%"

  # 网络告警
  - name: octi-network-alerts
    rules:
      # 网络连接数过高
      - alert: TooManyNetworkConnections
        expr: node_netstat_Tcp_CurrEstab > 1000
        for: 5m
        labels:
          severity: warning
          service: network
        annotations:
          summary: "网络连接数过高"
          description: "TCP连接数超过1000，当前: {{ $value }}"

      # 网络错误率过高
      - alert: HighNetworkErrorRate
        expr: rate(node_network_receive_errs_total[5m]) + rate(node_network_transmit_errs_total[5m]) > 10
        for: 5m
        labels:
          severity: warning
          service: network
        annotations:
          summary: "网络错误率过高"
          description: "网络接口错误率过高，接口: {{ $labels.device }}"

  # 业务指标告警
  - name: octi-business-alerts
    rules:
      # 评估完成率过低
      - alert: LowAssessmentCompletionRate
        expr: rate(octi_assessments_completed_total[1h]) / rate(octi_assessments_started_total[1h]) < 0.7
        for: 30m
        labels:
          severity: warning
          service: business
        annotations:
          summary: "评估完成率过低"
          description: "评估完成率低于70%，当前: {{ $value | humanizePercentage }}"

      # LLM API调用失败率过高
      - alert: HighLLMAPIFailureRate
        expr: rate(octi_llm_api_errors_total[5m]) / rate(octi_llm_api_requests_total[5m]) > 0.1
        for: 5m
        labels:
          severity: critical
          service: llm-api
        annotations:
          summary: "LLM API调用失败率过高"
          description: "LLM API失败率超过10%，当前: {{ $value | humanizePercentage }}"

      # 报告生成时间过长
      - alert: SlowReportGeneration
        expr: histogram_quantile(0.95, rate(octi_report_generation_duration_seconds_bucket[5m])) > 60
        for: 5m
        labels:
          severity: warning
          service: business
        annotations:
          summary: "报告生成时间过长"
          description: "95%的报告生成时间超过60秒，当前: {{ $value }}秒"