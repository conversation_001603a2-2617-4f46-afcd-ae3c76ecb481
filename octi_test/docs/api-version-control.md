# API文档版本控制机制

## 版本控制策略

### 1. 文档版本号规范
- **格式**: `v{major}.{minor}.{patch}`
- **示例**: `v1.2.3`
- **变更规则**:
  - `major`: 破坏性API变更
  - `minor`: 新增API或非破坏性功能变更
  - `patch`: 文档修正、示例更新

### 2. 文档同步检查清单

#### 新增API时
- [ ] 更新 `API_documentation.md`
- [ ] 添加请求/响应示例
- [ ] 更新API路径映射表
- [ ] 添加错误码定义
- [ ] 更新实现状态总览

#### 修改API时
- [ ] 检查向后兼容性
- [ ] 更新相关文档章节
- [ ] 验证示例代码准确性
- [ ] 更新变更日志

#### 删除API时
- [ ] 标记为废弃 (Deprecated)
- [ ] 提供迁移指南
- [ ] 设置废弃时间表

### 3. 自动化检查脚本

```bash
#!/bin/bash
# scripts/check-api-docs.sh

echo "🔍 检查API文档一致性..."

# 检查所有API路由文件
find src/app/api -name "route.ts" | while read file; do
  route_path=$(echo $file | sed 's|src/app/api||' | sed 's|/route.ts||')
  echo "检查路由: $route_path"
  
  # 检查文档中是否存在对应描述
  if ! grep -q "$route_path" docs/API_documentation.md; then
    echo "⚠️  警告: 路由 $route_path 在文档中未找到"
  fi
done

echo "✅ API文档检查完成"
```

### 4. 文档更新工作流

```yaml
# .github/workflows/docs-sync.yml
name: API文档同步检查

on:
  pull_request:
    paths:
      - 'src/app/api/**'
      - 'docs/**'

jobs:
  check-docs:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: 检查API文档一致性
        run: |
          chmod +x scripts/check-api-docs.sh
          ./scripts/check-api-docs.sh
          
      - name: 验证响应格式
        run: |
          # 检查所有API是否使用统一响应格式
          grep -r "NextResponse.json" src/app/api/ | grep -v "successResponse\|errorResponse" || true
```

### 5. 文档维护责任

| 角色 | 责任 |
|------|------|
| **后端开发** | API实现后立即更新文档 |
| **前端开发** | 验证API文档准确性 |
| **QA工程师** | 测试API与文档一致性 |
| **技术写作** | 文档格式和语言规范 |

### 6. 版本发布检查清单

发布前必须确认：
- [ ] 所有新增API已文档化
- [ ] 响应格式符合统一规范
- [ ] 错误码定义完整
- [ ] 示例代码可执行
- [ ] 实现状态准确标记
- [ ] 变更日志已更新