# OCTI智能评估系统 - 数据库Schema设计

## 1. 设计概述
本数据库设计旨在支持OCTI智能评估系统的核心业务流程，包括用户管理、配置化评估、多版本报告以及**专业版的多源数据融合功能**。

- **技术选型**: PostgreSQL (通过Prisma ORM进行管理)
- **核心原则**: 关系清晰、易于扩展、关键数据加密。

## 2. ER图 (实体关系图)

```mermaid
erDiagram
    User {
        string id PK
        string email UK
        string name
        string passwordHash
        Role role
        datetime createdAt
        datetime updatedAt
    }

    Organization {
        string id PK
        string name
        string industry
        string size
        string userId FK
        datetime createdAt
        datetime updatedAt
    }

    Assessment {
        string id PK
        string organizationId FK
        string version "标准版/专业版"
        AssessmentStatus status
        json questionnaireData
        json answersData
        json reportData
        datetime createdAt
        datetime completedAt
    }

    ExternalDataSource {
        string id PK
        string assessmentId FK
        DataSourceType type "FILE/URL"
        string source "文件路径/URL"
        ProcessingStatus status "PENDING/PROCESSING/COMPLETED/FAILED"
        datetime createdAt
    }

    ProcessedData {
        string id PK
        string dataSourceId FK
        text extractedContent
        json structuredData
        datetime processedAt
    }

    ApiKey {
        string id PK
        string provider UK
        string keyEncrypted
        string keyIv
        string keyTag
        datetime createdAt
        datetime updatedAt
    }

    SecurityEvent {
        string id PK
        SecurityEventType type
        string userId
        string ip
        string userAgent
        json details
        datetime timestamp
    }

    User ||--o{ Organization : "拥有"
    Organization ||--o{ Assessment : "进行"
    Assessment ||--o{ ExternalDataSource : "包含"
    ExternalDataSource ||--o{ ProcessedData : "产出"
```

## 3. Prisma Schema (`schema.prisma`)

```prisma
// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// --- 用户与组织模型 ---

model User {
  id             String         @id @default(uuid())
  email          String         @unique
  name           String?
  passwordHash   String
  role           Role           @default(USER)
  createdAt      DateTime       @default(now())
  updatedAt      DateTime       @updatedAt
  organizations  Organization[]
  securityEvents SecurityEvent[]
}

model Organization {
  id           String       @id @default(uuid())
  name         String
  industry     String?
  size         String?
  createdAt    DateTime     @default(now())
  updatedAt    DateTime     @updatedAt
  user         User         @relation(fields: [userId], references: [id])
  userId       String
  assessments  Assessment[]
}

enum Role {
  USER
  ADMIN
}

// --- 评估核心模型 ---

model Assessment {
  id                 String                 @id @default(uuid())
  version            String // "standard" or "professional"
  status             AssessmentStatus       @default(PENDING)
  questionnaireData  Json? // LLM生成的问卷结构
  answersData        Json? // 用户提交的答案
  reportData         Json? // LLM生成的分析报告
  createdAt          DateTime               @default(now())
  completedAt        DateTime?
  organization       Organization           @relation(fields: [organizationId], references: [id])
  organizationId     String
  externalDataSources ExternalDataSource[]
}

enum AssessmentStatus {
  PENDING
  IN_PROGRESS
  COMPLETED
  FAILED
}

// --- 【新增】外部数据融合模型 ---

model ExternalDataSource {
  id             String           @id @default(uuid())
  type           DataSourceType // "FILE" or "URL"
  source         String // 文件在COS中的路径或网页URL
  status         ProcessingStatus @default(PENDING)
  errorMessage   String? // 处理失败时的错误信息
  createdAt      DateTime         @default(now())
  assessment     Assessment       @relation(fields: [assessmentId], references: [id])
  assessmentId   String
  processedData  ProcessedData?
}

enum DataSourceType {
  FILE
  URL
}

enum ProcessingStatus {
  PENDING
  PROCESSING
  COMPLETED
  FAILED
}

model ProcessedData {
  id               String             @id @default(uuid())
  extractedContent String? // 从文件或网页中提取的原始文本
  structuredData   Json? // LLM初步处理后的结构化数据
  processedAt      DateTime           @default(now())
  dataSource       ExternalDataSource @relation(fields: [dataSourceId], references: [id])
  dataSourceId     String             @unique
}

// --- 系统与安全模型 ---

model ApiKey {
  id           String   @id @default(uuid())
  provider     String   @unique // e.g., "minimax", "deepseek"
  keyEncrypted String
  keyIv        String
  keyTag       String
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt
}

model SecurityEvent {
  id        String            @id @default(uuid())
  type      SecurityEventType
  details   Json?
  ip        String?
  userAgent String?
  timestamp DateTime          @default(now())
  user      User?             @relation(fields: [userId], references: [id])
  userId    String?
}

enum SecurityEventType {
  LOGIN_SUCCESS
  LOGIN_FAILURE
  PASSWORD_CHANGE
  ACCOUNT_LOCKOUT
  PERMISSION_CHANGE
  CONFIG_CHANGE
  API_KEY_USAGE
  SUSPICIOUS_ACTIVITY
}
```

## 4. 模型详解

### `User` & `Organization`
- 标准的用户和组织模型，用于多租户管理。

### `Assessment`
- 核心评估模型，存储一次评估的全过程数据。
- `version`字段区分标准版与专业版。
- `questionnaireData`, `answersData`, `reportData` 以JSON格式存储，以适应LLM输出的灵活性。

### `ExternalDataSource` (新增)
- **目的**: 追踪为某次**专业版**评估添加的每一个外部数据源。
- `type`: 区分是用户上传的`FILE`还是提供的`URL`。
- `source`: 记录文件路径或URL地址。
- `status`: 追踪该数据源的异步处理状态。

### `ProcessedData` (新增)
- **目的**: 存储从外部数据源中异步提取和处理后的数据。
- `extractedContent`: 存储从PDF、DOCX或网页中解析出的纯文本。
- `structuredData`: （可选）可以用于存储LLM对`extractedContent`进行初步信息提取后的结果，方便后续的融合分析。

### `ApiKey`, `SecurityEvent`
- 用于系统安全和审计，存储加密的API密钥和安全相关事件。