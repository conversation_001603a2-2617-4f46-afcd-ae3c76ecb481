# OCTI代码审查检查清单

## 基础代码规范
- [ ] 文件命名：kebab-case格式
- [ ] 组件命名：PascalCase格式  
- [ ] 变量命名：camelCase格式
- [ ] 常量命名：UPPER_SNAKE_CASE格式
- [ ] 严格TypeScript，避免any类型
- [ ] JSDoc注释完整性

## 架构合规性
- [ ] LLM API调用仅在后端进行
- [ ] 使用Zod验证所有输入
- [ ] 敏感数据加密存储
- [ ] 组件文件不超过200行
- [ ] 函数不超过50行

## 功能完整性
- [ ] 配置热更新机制
- [ ] 错误处理和降级策略
- [ ] 缓存机制实现
- [ ] API安全防护

# 需要审查的文件
src/services/config/
├── config-loader.ts
├── config-validator.ts  
├── config-cache.ts
└── version-control.ts

# 需要审查的文件
src/services/agents/
├── question-designer/
│   ├── index.ts
│   ├── prompt-builder.ts
│   └── response-parser.ts
└── organization-tutor/
    ├── standard-analyzer.ts
    └── professional-analyzer.ts

# 需要审查的文件
src/components/
├── questionnaire/
├── report/
└── config-management/

# 常见代码问题预判

## 类型安全问题
- 过度使用 `any` 类型
- 缺少接口定义
- 类型断言使用不当

## 错误处理问题  
- 缺少try-catch包装
- 错误信息不明确
- 没有降级机制

## 性能问题
- 缺少缓存机制
- 重复的API调用
- 内存泄漏风险

## 安全问题
- API密钥硬编码
- 输入验证不足
- XSS/注入攻击防护缺失

请按以下方式分享代码文件，我将逐一进行详细审查：

配置系统相关文件：
@src/services/config/目录下的所有文件
智能体服务文件：
@src/services/agents/目录下的核心文件
前端组件文件：
@src/components/目录下的主要组件
API路由文件：
@src/pages/api/或@src/app/api/目录
