# OCTI智能评估系统 - 安全考虑

## 1. 安全概述

OCTI智能评估系统处理组织敏感数据，因此安全性是系统设计和实现的核心考虑因素。本文档概述了系统的安全架构、潜在风险和缓解措施。

## 2. 数据安全

### 2.1 数据分类

系统中的数据按敏感度分为以下几类：

| 数据类型 | 敏感度 | 示例 |
|---------|-------|------|
| 公开数据 | 低 | 组织类型、行业信息 |
| 内部数据 | 中 | 问卷问题、评估框架 |
| 敏感数据 | 高 | 用户账号、组织评估结果 |
| 机密数据 | 极高 | 认证凭证、API密钥 |

### 2.2 数据加密

#### 2.2.1 传输中加密

- 所有API通信使用TLS 1.3加密
- WebSocket连接使用WSS协议
- 内部服务间通信使用mTLS

#### 2.2.2 静态数据加密

```typescript
// src/services/encryption/index.ts
import crypto from 'crypto';

const ENCRYPTION_KEY = process.env.ENCRYPTION_KEY;
const ALGORITHM = 'aes-256-gcm';

export function encrypt(text: string): { encryptedData: string, iv: string, tag: string } {
  const iv = crypto.randomBytes(16);
  const cipher = crypto.createCipheriv(ALGORITHM, Buffer.from(ENCRYPTION_KEY, 'hex'), iv);
  
  let encrypted = cipher.update(text, 'utf8', 'hex');
  encrypted += cipher.final('hex');
  
  return {
    encryptedData: encrypted,
    iv: iv.toString('hex'),
    tag: cipher.getAuthTag().toString('hex')
  };
}

export function decrypt(encryptedData: string, iv: string, tag: string): string {
  const decipher = crypto.createDecipheriv(
    ALGORITHM, 
    Buffer.from(ENCRYPTION_KEY, 'hex'), 
    Buffer.from(iv, 'hex')
  );
  
  decipher.setAuthTag(Buffer.from(tag, 'hex'));
  
  let decrypted = decipher.update(encryptedData, 'hex', 'utf8');
  decrypted += decipher.final('utf8');
  
  return decrypted;
}
```

#### 2.2.3 数据库加密

敏感字段在数据库层面加密存储：

```typescript
// prisma/schema.prisma
model OrganizationAssessment {
  id          String   @id @default(uuid())
  // ...其他字段
  
  // 加密字段
  resultsEncrypted String?
  resultsIv        String?
  resultsTag       String?
  
  // ...其他字段和关系
}
```

```typescript
// src/services/assessment/storage.ts
import { prisma } from '@/prisma/client';
import { encrypt, decrypt } from '@/services/encryption';

export async function saveAssessmentResults(assessmentId: string, results: any): Promise<void> {
  const resultsJson = JSON.stringify(results);
  const { encryptedData, iv, tag } = encrypt(resultsJson);
  
  await prisma.organizationAssessment.update({
    where: { id: assessmentId },
    data: {
      resultsEncrypted: encryptedData,
      resultsIv: iv,
      resultsTag: tag
    }
  });
}

export async function getAssessmentResults(assessmentId: string): Promise<any> {
  const assessment = await prisma.organizationAssessment.findUnique({
    where: { id: assessmentId },
    select: {
      resultsEncrypted: true,
      resultsIv: true,
      resultsTag: true
    }
  });
  
  if (!assessment?.resultsEncrypted) {
    return null;
  }
  
  const decrypted = decrypt(
    assessment.resultsEncrypted,
    assessment.resultsIv,
    assessment.resultsTag
  );
  
  return JSON.parse(decrypted);
}
```

### 2.3 数据备份和恢复

- 自动每日备份
- 备份加密存储
- 定期恢复测试
- 90天备份保留策略

## 3. 身份认证与授权

### 3.1 用户认证

系统使用NextAuth.js实现多种认证方式：

```typescript
// src/pages/api/auth/[...nextauth].ts
import NextAuth from 'next-auth';
import CredentialsProvider from 'next-auth/providers/credentials';
import { PrismaAdapter } from '@next-auth/prisma-adapter';
import { prisma } from '@/prisma/client';
import { verifyPassword } from '@/services/auth/password';

export default NextAuth({
  adapter: PrismaAdapter(prisma),
  providers: [
    CredentialsProvider({
      name: 'Credentials',
      credentials: {
        email: { label: "邮箱", type: "email" },
        password: { label: "密码", type: "password" }
      },
      async authorize(credentials) {
        const user = await prisma.user.findUnique({
          where: { email: credentials.email }
        });
        
        if (!user) {
          return null;
        }
        
        const isValid = await verifyPassword(
          credentials.password,
          user.passwordHash
        );
        
        if (!isValid) {
          return null;
        }
        
        return {
          id: user.id,
          email: user.email,
          name: user.name,
          role: user.role
        };
      }
    })
  ],
  session: {
    strategy: 'jwt',
    maxAge: 30 * 24 * 60 * 60, // 30天
  },
  callbacks: {
    async jwt({ token, user }) {
      if (user) {
        token.id = user.id;
        token.role = user.role;
      }
      return token;
    },
    async session({ session, token }) {
      session.user.id = token.id;
      session.user.role = token.role;
      return session;
    }
  },
  pages: {
    signIn: '/auth/login',
    error: '/auth/error',
  },
  secret: process.env.NEXTAUTH_SECRET
});
```

### 3.2 密码安全

密码使用Argon2算法哈希存储：

```typescript
// src/services/auth/password.ts
import argon2 from 'argon2';

export async function hashPassword(password: string): Promise<string> {
  return await argon2.hash(password, {
    type: argon2.argon2id,
    memoryCost: 2**16,
    timeCost: 3,
    parallelism: 1
  });
}

export async function verifyPassword(
  password: string,
  hashedPassword: string
): Promise<boolean> {
  return await argon2.verify(hashedPassword, password);
}
```

### 3.3 基于角色的访问控制

```typescript
// src/middleware.ts
import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import { getToken } from 'next-auth/jwt';

export async function middleware(request: NextRequest) {
  const token = await getToken({ req: request });
  
  // 未认证用户重定向到登录页
  if (!token) {
    return NextResponse.redirect(new URL('/auth/login', request.url));
  }
  
  // 基于角色的访问控制
  if (request.nextUrl.pathname.startsWith('/admin') && token.role !== 'ADMIN') {
    return NextResponse.redirect(new URL('/dashboard', request.url));
  }
  
  return NextResponse.next();
}

export const config = {
  matcher: ['/dashboard/:path*', '/admin/:path*', '/assessment/:path*']
};
```

### 3.4 API安全

API端点使用多层保护：

```typescript
// src/pages/api/admin/config/update.ts
import type { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/pages/api/auth/[...nextauth]';
import { rateLimit } from '@/services/security/rate-limit';
import { validateConfigSchema } from '@/services/config/validator';

// 速率限制中间件
const limiter = rateLimit({
  interval: 60 * 1000, // 1分钟
  uniqueTokenPerInterval: 100,
  max: 10 // 每分钟最多10个请求
});

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  // 1. 方法验证
  if (req.method !== 'POST') {
    return res.status(405).json({ error: '方法不允许' });
  }
  
  try {
    // 2. 速率限制
    await limiter.check(res, 10, 'update_config');
    
    // 3. 会话验证
    const session = await getServerSession(req, res, authOptions);
    if (!session) {
      return res.status(401).json({ error: '未授权' });
    }
    
    // 4. 角色验证
    if (session.user.role !== 'ADMIN') {
      return res.status(403).json({ error: '权限不足' });
    }
    
    // 5. 输入验证
    const { configType, configData } = req.body;
    if (!configType || !configData) {
      return res.status(400).json({ error: '无效的请求数据' });
    }
    
    // 6. 架构验证
    const validationResult = validateConfigSchema(configType, configData);
    if (!validationResult.valid) {
      return res.status(400).json({ 
        error: '配置数据无效',
        details: validationResult.errors
      });
    }
    
    // 处理配置更新...
    
    return res.status(200).json({ success: true });
  } catch (error) {
    if (error.code === 'RATE_LIMIT_EXCEEDED') {
      return res.status(429).json({ error: '请求过于频繁，请稍后再试' });
    }
    
    console.error('配置更新错误:', error);
    return res.status(500).json({ error: '服务器内部错误' });
  }
}
```

## 4. 前端安全

### 4.1 CSP配置

```typescript
// next.config.js
const ContentSecurityPolicy = `
  default-src 'self';
  script-src 'self' 'unsafe-eval' 'unsafe-inline';
  style-src 'self' 'unsafe-inline';
  img-src 'self' data: blob:;
  font-src 'self';
  connect-src 'self' https://api.minimax.chat https://api.deepseek.com;
  frame-src 'self';
  object-src 'none';
`;

module.exports = {
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'Content-Security-Policy',
            value: ContentSecurityPolicy.replace(/\s{2,}/g, ' ').trim()
          },
          {
            key: 'X-XSS-Protection',
            value: '1; mode=block'
          },
          {
            key: 'X-Frame-Options',
            value: 'SAMEORIGIN'
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff'
          },
          {
            key: 'Referrer-Policy',
            value: 'strict-origin-when-cross-origin'
          },
          {
            key: 'Permissions-Policy',
            value: 'camera=(), microphone=(), geolocation=()'
          }
        ]
      }
    ];
  }
};
```

### 4.2 CSRF保护

```typescript
// src/pages/api/questionnaire/submit.ts
import type { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/pages/api/auth/[...nextauth]';
import { validateCsrfToken } from '@/services/security/csrf';

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: '方法不允许' });
  }
  
  try {
    // 会话验证
    const session = await getServerSession(req, res, authOptions);
    if (!session) {
      return res.status(401).json({ error: '未授权' });
    }
    
    // CSRF令牌验证
    const csrfToken = req.headers['x-csrf-token'] as string;
    if (!validateCsrfToken(csrfToken, session.user.id)) {
      return res.status(403).json({ error: 'CSRF验证失败' });
    }
    
    // 处理问卷提交...
    
    return res.status(200).json({ success: true });
  } catch (error) {
    console.error('问卷提交错误:', error);
    return res.status(500).json({ error: '服务器内部错误' });
  }
}
```

### 4.3 XSS防护

使用React的内置XSS防护和DOMPurify库：

```typescript
// src/components/report/ReportContent.tsx
import React from 'react';
import DOMPurify from 'isomorphic-dompurify';
import { marked } from 'marked';

interface ReportContentProps {
  content: string;
}

export function ReportContent({ content }: ReportContentProps) {
  // 将Markdown转换为HTML，并净化
  const sanitizedHtml = DOMPurify.sanitize(marked(content), {
    USE_PROFILES: { html: true },
    ALLOWED_TAGS: [
      'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'p', 'ul', 'ol', 'li',
      'strong', 'em', 'blockquote', 'code', 'pre', 'br', 'a'
    ],
    ALLOWED_ATTR: ['href', 'target', 'rel']
  });
  
  return (
    <div className="report-content">
      <div dangerouslySetInnerHTML={{ __html: sanitizedHtml }} />
    </div>
  );
}
```

## 5. LLM安全

### 5.1 提示词注入防护

```typescript
// src/services/llm/security/prompt-sanitizer.ts
import { sanitizeInput } from '@/services/security/input-sanitizer';

export function sanitizePrompt(prompt: string): string {
  // 基本清理
  let sanitized = sanitizeInput(prompt);
  
  // 移除可能的提示词注入模式
  sanitized = sanitized
    // 移除尝试覆盖系统提示词的模式
    .replace(/system:\s*.*?(?=\n|$)/gi, '')
    // 移除尝试结束对话的模式
    .replace(/end conversation|ignore previous|ignore instructions/gi, '')
    // 移除尝试获取系统提示词的模式
    .replace(/show me your prompts|what are your instructions/gi, '');
  
  return sanitized;
}

export function validatePromptSafety(prompt: string): boolean {
  // 检查是否包含敏感指令
  const sensitivePatterns = [
    /generate (harmful|illegal|unethical)/i,
    /bypass (security|filters|restrictions)/i,
    /ignore (guidelines|rules|instructions)/i
  ];
  
  return !sensitivePatterns.some(pattern => pattern.test(prompt));
}
```

### 5.2 LLM输出验证

```typescript
// src/services/llm/security/output-validator.ts
import { z } from 'zod';

// 问卷问题的架构验证
const QuestionOptionSchema = z.object({
  id: z.string(),
  text: z.string().min(1),
  value: z.number().or(z.string()),
  dimension: z.string().optional()
});

const QuestionSchema = z.object({
  id: z.string(),
  text: z.string().min(1),
  type: z.string(),
  options: z.array(QuestionOptionSchema).min(2),
  dimension: z.string().optional()
});

const QuestionnaireSchema = z.array(QuestionSchema);

export function validateQuestionnaireOutput(output: any): {
  valid: boolean;
  data?: any;
  errors?: any;
} {
  try {
    const result = QuestionnaireSchema.safeParse(output);
    
    if (result.success) {
      return {
        valid: true,
        data: result.data
      };
    } else {
      return {
        valid: false,
        errors: result.error.format()
      };
    }
  } catch (error) {
    return {
      valid: false,
      errors: error.message
    };
  }
}

// 报告输出的架构验证
const ReportSectionSchema = z.object({
  title: z.string(),
  content: z.string().min(1)
});

const ReportSchema = z.object({
  meta: z.object({
    version: z.string(),
    generatedAt: z.string(),
    models: z.array(z.string())
  }),
  sections: z.array(ReportSectionSchema)
});

export function validateReportOutput(output: any): {
  valid: boolean;
  data?: any;
  errors?: any;
} {
  try {
    const result = ReportSchema.safeParse(output);
    
    if (result.success) {
      return {
        valid: true,
        data: result.data
      };
    } else {
      return {
        valid: false,
        errors: result.error.format()
      };
    }
  } catch (error) {
    return {
      valid: false,
      errors: error.message
    };
  }
}
```

### 5.3 API密钥安全

```typescript
// src/services/llm/api-key-manager.ts
import { prisma } from '@/prisma/client';
import { encrypt, decrypt } from '@/services/encryption';

export async function getApiKey(provider: string): Promise<string | null> {
  const apiKeyRecord = await prisma.apiKey.findUnique({
    where: { provider }
  });
  
  if (!apiKeyRecord) {
    return null;
  }
  
  // 解密API密钥
  return decrypt(
    apiKeyRecord.keyEncrypted,
    apiKeyRecord.keyIv,
    apiKeyRecord.keyTag
  );
}

export async function rotateApiKey(
  provider: string,
  newKey: string
): Promise<void> {
  // 加密新的API密钥
  const { encryptedData, iv, tag } = encrypt(newKey);
  
  // 更新或创建API密钥记录
  await prisma.apiKey.upsert({
    where: { provider },
    update: {
      keyEncrypted: encryptedData,
      keyIv: iv,
      keyTag: tag,
      updatedAt: new Date()
    },
    create: {
      provider,
      keyEncrypted: encryptedData,
      keyIv: iv,
      keyTag: tag
    }
  });
  
  // 记录密钥轮换事件
  await prisma.securityEvent.create({
    data: {
      type: 'API_KEY_ROTATION',
      details: JSON.stringify({ provider }),
      timestamp: new Date()
    }
  });
}
```

## 6. 安全监控与审计

### 6.1 安全日志

```typescript
// src/services/security/logger.ts
import { prisma } from '@/prisma/client';
import winston from 'winston';

// 创建Winston日志记录器
const logger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.json()
  ),
  defaultMeta: { service: 'octi-security' },
  transports: [
    new winston.transports.File({ filename: 'logs/security-error.log', level: 'error' }),
    new winston.transports.File({ filename: 'logs/security.log' })
  ]
});

// 在非生产环境下，也将日志输出到控制台
if (process.env.NODE_ENV !== 'production') {
  logger.add(new winston.transports.Console({
    format: winston.format.simple()
  }));
}

export enum SecurityEventType {
  LOGIN_SUCCESS = 'LOGIN_SUCCESS',
  LOGIN_FAILURE = 'LOGIN_FAILURE',
  PASSWORD_CHANGE = 'PASSWORD_CHANGE',
  ACCOUNT_LOCKOUT = 'ACCOUNT_LOCKOUT',
  PERMISSION_CHANGE = 'PERMISSION_CHANGE',
  CONFIG_CHANGE = 'CONFIG_CHANGE',
  API_KEY_USAGE = 'API_KEY_USAGE',
  SUSPICIOUS_ACTIVITY = 'SUSPICIOUS_ACTIVITY'
}

export interface SecurityEventData {
  userId?: string;
  ip?: string;
  userAgent?: string;
  details?: any;
}

export async function logSecurityEvent(
  type: SecurityEventType,
  data: SecurityEventData
): Promise<void> {
  // 记录到数据库
  await prisma.securityEvent.create({
    data: {
      type,
      userId: data.userId,
      ip: data.ip,
      userAgent: data.userAgent,
      details: data.details ? JSON.stringify(data.details) : null,
      timestamp: new Date()
    }
  });
  
  // 记录到日志文件
  logger.info(`安全事件: ${type}`, {
    ...data,
    timestamp: new Date().toISOString()
  });
}
```

### 6.2 入侵检测

```typescript
// src/services/security/intrusion-detection.ts
import { prisma } from '@/prisma/client';
import { logSecurityEvent, SecurityEventType } from './logger';

// 检测登录失败尝试
export async function detectLoginAttempts(
  userId: string,
  ip: string,
  success: boolean
): Promise<boolean> {
  // 记录登录尝试
  await logSecurityEvent(
    success ? SecurityEventType.LOGIN_SUCCESS : SecurityEventType.LOGIN_FAILURE,
    { userId, ip }
  );
  
  if (!success) {
    // 检查最近30分钟内的失败尝试
    const recentFailures = await prisma.securityEvent.count({
      where: {
        type: SecurityEventType.LOGIN_FAILURE,
        userId,
        timestamp: {
          gte: new Date(Date.now() - 30 * 60 * 1000)
        }
      }
    });
    
    // 如果失败尝试超过5次，锁定账户
    if (recentFailures >= 5) {
      await prisma.user.update({
        where: { id: userId },
        data: { locked: true, lockedAt: new Date() }
      });
      
      await logSecurityEvent(SecurityEventType.ACCOUNT_LOCKOUT, {
        userId,
        ip,
        details: { reason: '多次登录失败' }
      });
      
      return true; // 账户已锁定
    }
  } else {
    // 登录成功，重置失败计数
    await prisma.user.update({
      where: { id: userId },
      data: { loginFailCount: 0 }
    });
  }
  
  return false; // 账户未锁定
}

// 检测可疑活动
export async function detectSuspiciousActivity(
  userId: string,
  ip: string,
  action: string
): Promise<boolean> {
  // 获取用户的常用IP
  const userIps = await prisma.securityEvent.findMany({
    where: {
      userId,
      type: SecurityEventType.LOGIN_SUCCESS,
      timestamp: {
        gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) // 30天内
      }
    },
    select: { ip: true },
    distinct: ['ip']
  });
  
  const knownIps = userIps.map(event => event.ip);
  
  // 如果是新IP且执行敏感操作，标记为可疑
  if (!knownIps.includes(ip) && isSensitiveAction(action)) {
    await logSecurityEvent(SecurityEventType.SUSPICIOUS_ACTIVITY, {
      userId,
      ip,
      details: { action, reason: '新IP执行敏感操作' }
    });
    
    return true; // 可疑活动
  }
  
  return false; // 正常活动
}

function isSensitiveAction(action: string): boolean {
  const sensitiveActions = [
    'update_config',
    'delete_data',
    'change_permissions',
    'export_all_data',
    'api_key_rotation'
  ];
  
  return sensitiveActions.includes(action);
}
```

### 6.3 安全审计

```typescript
// src/services/security/auditor.ts
import { prisma } from '@/prisma/client';

export async function generateSecurityAuditReport(
  startDate: Date,
  endDate: Date
): Promise<any> {
  // 获取时间范围内的所有安全事件
  const events = await prisma.securityEvent.findMany({
    where: {
      timestamp: {
        gte: startDate,
        lte: endDate
      }
    },
    orderBy: {
      timestamp: 'asc'
    }
  });
  
  // 按类型分组
  const eventsByType = events.reduce((acc, event) => {
    acc[event.type] = acc[event.type] || [];
    acc[event.type].push(event);
    return acc;
  }, {});
  
  // 计算登录失败率
  const loginAttempts = [
    ...(eventsByType.LOGIN_SUCCESS || []),
    ...(eventsByType.LOGIN_FAILURE || [])
  ];
  
  const failureRate = loginAttempts.length > 0
    ? (eventsByType.LOGIN_FAILURE || []).length / loginAttempts.length
    : 0;
  
  // 识别最活跃的用户和IP
  const userActivity = {};
  const ipActivity = {};
  
  events.forEach(event => {
    if (event.userId) {
      userActivity[event.userId] = (userActivity[event.userId] || 0) + 1;
    }
    
    if (event.ip) {
      ipActivity[event.ip] = (ipActivity[event.ip] || 0) + 1;
    }
  });
  
  const mostActiveUsers = Object.entries(userActivity)
    .sort((a, b) => b[1] - a[1])
    .slice(0, 10)
    .map(([userId, count]) => ({ userId, count }));
  
  const mostActiveIps = Object.entries(ipActivity)
    .sort((a, b) => b[1] - a[1])
    .slice(0, 10)
    .map(([ip, count]) => ({ ip, count }));
  
  // 生成报告
  return {
    period: {
      start: startDate,
      end: endDate
    },
    summary: {
      totalEvents: events.length,
      eventsByType: Object.entries(eventsByType).map(([type, events]) => ({
        type,
        count: events.length
      }))
    },
    loginActivity: {
      totalAttempts: loginAttempts.length,
      successCount: (eventsByType.LOGIN_SUCCESS || []).length,
      failureCount: (eventsByType.LOGIN_FAILURE || []).length,
      failureRate: failureRate.toFixed(2)
    },
    accountSecurity: {
      lockouts: (eventsByType.ACCOUNT_LOCKOUT || []).length,
      passwordChanges: (eventsByType.PASSWORD_CHANGE || []).length,
      permissionChanges: (eventsByType.PERMISSION_CHANGE || []).length
    },
    suspiciousActivity: {
      count: (eventsByType.SUSPICIOUS_ACTIVITY || []).length,
      details: (eventsByType.SUSPICIOUS_ACTIVITY || []).map(event => ({
        timestamp: event.timestamp,
        userId: event.userId,
        ip: event.ip,
        details: JSON.parse(event.details || '{}')
      }))
    },
    topActivity: {
      users: mostActiveUsers,
      ips: mostActiveIps
    }
  };
}
```

## 7. 安全合规

### 7.1 数据保护合规

系统设计符合以下数据保护法规：

- 中国《网络安全法》
- 中国《数据安全法》
- 中国《个人信息保护法》
- GDPR (适用于处理欧盟用户数据)

### 7.2 数据处理协议

```typescript
// src/pages/api/data-processing-agreement.ts
import type { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/pages/api/auth/[...nextauth]';
import { prisma } from '@/prisma/client';

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  // 获取用户会话
  const session = await getServerSession(req, res, authOptions);
  if (!session) {
    return res.status(401).json({ error: '未授权' });
  }
  
  if (req.method === 'GET') {
    // 获取当前DPA状态
    const agreement = await prisma.dataProcessingAgreement.findUnique({
      where: { userId: session.user.id }
    });
    
    return res.status(200).json({
      agreed: !!agreement,
      timestamp: agreement?.agreedAt || null,
      version: agreement?.version || null
    });
  }
  
  if (req.method === 'POST') {
    // 接受DPA
    const { version } = req.body;
    
    if (!version) {
      return res.status(400).json({ error: '缺少版本信息' });
    }
    
    // 更新或创建DPA记录
    await prisma.dataProcessingAgreement.upsert({
      where: { userId: session.user.id },
      update: {
        version,
        agreedAt: new Date()
      },
      create: {
        userId: session.user.id,
        version,
        agreedAt: new Date()
      }
    });
    
    return res.status(200).json({ success: true });
  }
  
  return res.status(405).json({ error: '方法不允许' });
}
```

### 7.3 数据留存政策

```typescript
// src/services/data-retention/policy.ts
import { prisma } from '@/prisma/client';

// 数据留存期限（天）
const RETENTION_PERIODS = {
  ASSESSMENT_DATA: 365 * 2, // 2年
  USER_ACTIVITY: 180, // 6个月
  SECURITY_LOGS: 365, // 1年
  ANONYMOUS_ANALYTICS: 730, // 2年
};

// 执行数据清理
export async function cleanupExpiredData(): Promise<{
  assessmentsDeleted: number;
  activitiesDeleted: number;
  logsDeleted: number;
}> {
  // 清理过期的评估数据
  const assessmentCutoff = new Date(
    Date.now() - RETENTION_PERIODS.ASSESSMENT_DATA * 24 * 60 * 60 * 1000
  );
  
  const { count: assessmentsDeleted } = await prisma.organizationAssessment.deleteMany({
    where: {
      createdAt: {
        lt: assessmentCutoff
      },
      retentionOverride: false
    }
  });
  
  // 清理用户活动数据
  const activityCutoff = new Date(
    Date.now() - RETENTION_PERIODS.USER_ACTIVITY * 24 * 60 * 60 * 1000
  );
  
  const { count: activitiesDeleted } = await prisma.userActivity.deleteMany({
    where: {
      timestamp: {
        lt: activityCutoff
      }
    }
  });
  
  // 清理安全日志
  const logsCutoff = new Date(
    Date.now() - RETENTION_PERIODS.SECURITY_LOGS * 24 * 60 * 60 * 1000
  );
  
  const { count: logsDeleted } = await prisma.securityEvent.deleteMany({
    where: {
      timestamp: {
        lt: logsCutoff
      },
      // 保留重要的安全事件
      type: {
        notIn: ['ACCOUNT_LOCKOUT', 'SUSPICIOUS_ACTIVITY']
      }
    }
  });
  
  return {
    assessmentsDeleted,
    activitiesDeleted,
    logsDeleted
  };
}

// 匿名化数据
export async function anonymizeUserData(userId: string): Promise<void> {
  // 获取用户信息
  const user = await prisma.user.findUnique({
    where: { id: userId }
  });
  
  if (!user) {
    throw new Error('用户不存在');
  }
  
  // 创建匿名化记录
  await prisma.anonymizedUser.create({
    data: {
      originalId: userId,
      anonymizedAt: new Date(),
      metadata: JSON.stringify({
        registrationDate: user.createdAt,
        lastActive: user.updatedAt
      })
    }
  });
  
  // 匿名化用户数据
  await prisma.user.update({
    where: { id: userId },
    data: {
      email: `anonymized_${Date.now()}@example.com`,
      name: `已匿名化用户`,
      passwordHash: null,
      phone: null,
      profileData: null,
      isAnonymized: true
    }
  });
  
  // 匿名化关联的评估数据
  await prisma.organizationAssessment.updateMany({
    where: { userId },
    data: {
      organizationName: '已匿名化组织',
      organizationDetails: null
    }
  });
}
```

## 8. 安全最佳实践

### 8.1 安全开发生命周期

OCTI系统开发遵循以下安全开发生命周期：

1. **需求阶段**：进行威胁建模和风险评估
2. **设计阶段**：应用安全设计原则和模式
3. **开发阶段**：遵循安全编码规范
4. **测试阶段**：进行安全测试和代码审查
5. **部署阶段**：安全配置和加固
6. **维护阶段**：持续监控和更新

### 8.2 安全配置清单

部署前安全配置清单：

- [ ] 所有默认密码已更改
- [ ] 生产环境中禁用调试模式
- [ ] 启用HTTPS并配置TLS 1.3
- [ ] 配置安全响应头
- [ ] 实施适当的CORS策略
- [ ] 配置数据库最小权限
- [ ] 启用审计日志
- [ ] 配置自动备份
- [ ] 设置入侵检测规则
- [ ] 配置防火墙规则
- [ ] 实施速率限制
- [ ] 配置错误处理（不泄露敏感信息）
- [ ] 设置安全监控和告警

### 8.3 安全响应计划

安全事件响应流程：

1. **准备**：建立安全响应团队和流程
2. **检测**：监控系统和日志以识别潜在事件
3. **分析**：评估事件的范围和影响
4. **遏制**：限制事件的影响范围
5. **根除**：移除威胁源
6. **恢复**：恢复系统正常运行
7. **总结**：记录经验教训并改进流程

```typescript
// src/services/security/incident-response.ts
import { prisma } from '@/prisma/client';
import { sendEmail } from '@/services/notifications/email';

export enum IncidentSeverity {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  CRITICAL = 'CRITICAL'
}

export enum IncidentStatus {
  DETECTED = 'DETECTED',
  ANALYZING = 'ANALYZING',
  CONTAINING = 'CONTAINING',
  ERADICATED = 'ERADICATED',
  RECOVERING = 'RECOVERING',
  RESOLVED = 'RESOLVED'
}

export interface IncidentDetails {
  title: string;
  description: string;
  severity: IncidentSeverity;
  affectedSystems: string[];
  detectionSource: string;
}

export async function reportSecurityIncident(
  details: IncidentDetails
): Promise<string> {
  // 创建安全事件记录
  const incident = await prisma.securityIncident.create({
    data: {
      title: details.title,
      description: details.description,
      severity: details.severity,
      status: IncidentStatus.DETECTED,
      affectedSystems: details.affectedSystems.join(','),
      detectionSource: details.detectionSource,
      reportedAt: new Date()
    }
  });
  
  // 根据严重性确定通知对象
  const notifyEmails = details.severity === IncidentSeverity.CRITICAL || 
                      details.severity === IncidentSeverity.HIGH
    ? ['<EMAIL>', '<EMAIL>']
    : ['<EMAIL>'];
  
  // 发送通知
  await sendEmail({
    to: notifyEmails,
    subject: `安全事件警报: ${details.title} [${details.severity}]`,
    body: `
      <h2>安全事件详情</h2>
      <p><strong>ID:</strong> ${incident.id}</p>
      <p><strong>标题:</strong> ${details.title}</p>
      <p><strong>严重性:</strong> ${details.severity}</p>
      <p><strong>状态:</strong> ${IncidentStatus.DETECTED}</p>
      <p><strong>检测时间:</strong> ${incident.reportedAt.toISOString()}</p>
      <p><strong>描述:</strong> ${details.description}</p>
      <p><strong>受影响系统:</strong> ${details.affectedSystems.join(', ')}</p>
      <p><strong>检测来源:</strong> ${details.detectionSource}</p>
      <p>请立即访问安全事件响应平台处理此事件。</p>
    `
  });
  
  return incident.id;
}

export async function updateIncidentStatus(
  incidentId: string,
  status: IncidentStatus,
  notes: string
): Promise<void> {
  // 更新事件状态
  await prisma.securityIncident.update({
    where: { id: incidentId },
    data: {
      status,
      updatedAt: new Date()
    }
  });
  
  // 记录状态变更
  await prisma.securityIncidentUpdate.create({
    data: {
      incidentId,
      status,
      notes,
      timestamp: new Date()
    }
  });
  
  // 如果事件已解决，发送总结通知
  if (status === IncidentStatus.RESOLVED) {
    const incident = await prisma.securityIncident.findUnique({
      where: { id: incidentId },
      include: {
        updates: {
          orderBy: { timestamp: 'asc' }
        }
      }
    });
    
    const timeline = incident.updates.map(update => `
      <li>
        <strong>${update.timestamp.toISOString()}</strong>: 
        状态变更为 ${update.status}
        ${update.notes ? `<br>备注: ${update.notes}` : ''}
      </li>
    `).join('');
    
    await sendEmail({
      to: ['<EMAIL>'],
      subject: `安全事件已解决: ${incident.title} [${incident.severity}]`,
      body: `
        <h2>安全事件已解决</h2>
        <p><strong>ID:</strong> ${incident.id}</p>
        <p><strong>标题:</strong> ${incident.title}</p>
        <p><strong>严重性:</strong> ${incident.severity}</p>
        <p><strong>检测时间:</strong> ${incident.reportedAt.toISOString()}</p>
        <p><strong>解决时间:</strong> ${new Date().toISOString()}</p>
        
        <h3>事件时间线</h3>
        <ul>
          ${timeline}
        </ul>
        
        <p>请在30天内完成事件总结报告。</p>
      `
    });
  }
}
```

## 9. 第三方安全

### 9.1 LLM供应商安全评估

| 供应商 | 安全合规 | 数据处理位置 | 数据留存政策 | API安全 | 风险评级 |
|-------|---------|------------|------------|--------|--------|
| MiniMax | ISO 27001, SOC 2 | 中国大陆 | 30天 | TLS 1.3, API密钥 | 低 |
| DeepSeek | ISO 27001 | 中国大陆 | 14天 | TLS 1.3, API密钥 | 低 |

### 9.2 依赖项安全管理

```json
// package.json中的安全相关脚本
{
  "scripts": {
    "security:audit": "npm audit --audit-level=high",
    "security:deps": "npx depcheck",
    "security:outdated": "npm outdated",
    "security:check": "npm run security:audit && npm run security:deps && npm run security:outdated"
  },
  "devDependencies": {
    "depcheck": "^1.4.3"
  }
}
```

### 9.3 供应链安全

```yaml
# .github/workflows/dependency-review.yml
name: 依赖项安全审查

on:
  pull_request:
    branches: [ main ]
    paths:
      - 'package.json'
      - 'package-lock.json'
      - 'yarn.lock'

jobs:
  dependency-review:
    runs-on: ubuntu-latest
    steps:
      - name: 检出代码
        uses: actions/checkout@v3
      
      - name: 依赖项审查
        uses: actions/dependency-review-action@v2
        with:
          fail-on-severity: high
          deny-licenses: GPL-1.0-only, LGPL-2.0-only
  
  snyk-security:
    runs-on: ubuntu-latest
    steps:
      - name: 检出代码
        uses: actions/checkout@v3
      
      - name: Snyk安全扫描
        uses: snyk/actions/node@master
        env:
          SNYK_TOKEN: ${{ secrets.SNYK_TOKEN }}
        with:
          args: --severity-threshold=high
```

## 10. 安全测试

### 10.1 自动化安全测试

```yaml
# .github/workflows/security-scan.yml
name: 安全扫描

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]
  schedule:
    - cron: '0 2 * * 1' # 每周一凌晨2点

jobs:
  security-scan:
    runs-on: ubuntu-latest
    steps:
      - name: 检出代码
        uses: actions/checkout@v3
      
      - name: 安装依赖
        run: npm ci
      
      - name: ESLint安全规则检查
        run: npx eslint . --config .eslintrc.security.js
      
      - name: 依赖项审计
        run: npm audit --audit-level=moderate
      
      - name: SAST扫描
        uses: github/codeql-action/analyze@v2
        with:
          languages: javascript, typescript
      
      - name: SonarCloud扫描
        uses: SonarSource/sonarcloud-github-action@master
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
```

### 10.2 渗透测试清单

每季度进行渗透测试，重点关注：

1. 认证和会话管理
2. 授权和访问控制
3. 输入验证和输出编码
4. 加密实现
5. 错误处理和日志记录
6. 配置管理
7. 业务逻辑缺陷
8. 客户端安全
9. API安全
10. LLM特定风险（提示词注入、数据泄露）

## 11. 安全培训

### 11.1 开发团队安全培训

每季度进行安全培训，内容包括：

- 安全编码实践
- 常见漏洞及防护
- LLM安全风险
- 数据保护要求
- 安全事件响应
- 新兴安全威胁

### 11.2 用户安全指南

为系统管理员和最终用户提供安全指南：

- 密码管理最佳实践
- 多因素认证设置
- 数据处理安全
- 可疑活动识别
- 安全事件报告流程
- 隐私设置管理
