    - **测试用例4**: 模拟LLM服务调用失败，验证系统是否能回退到标准版的单一模型分析并成功生成报告。
    - **测试用例5**: 验证文件上传和处理功能的基础错误处理机制。

### 4.1.3 问题深度验证测试 ⭐**新增**
- **目标**: 验证标准版和专业版问卷的问题深度差异化是否符合配置要求
- **测试用例**:
  - **测试用例6**: 生成标准版问卷，验证问题文本中包含"基本"、"通常"、"一般"等浅层关键词的比例≥30%
  - **测试用例7**: 生成专业版问卷，验证问题文本中包含"深入"、"复杂"、"多维度"等深层关键词的比例≥30%
  - **测试用例8**: 对比标准版和专业版同一维度的问题，验证专业版问题的情境复杂度明显高于标准版
  - **测试用例9**: 验证专业版问题的认知负荷（问题长度、选项复杂度）明显高于标准版
  - **测试用例10**: 使用相同的组织信息分别生成标准版和专业版问卷，验证两个版本问题风格的差异化

## 4.2 基础性能测试
- **工具**: 简单的Node.js测试脚本
- **场景**:
  - **基础并发**: 模拟5-10个用户同时使用系统的基本响应时间。
  - **文件处理**: 测试处理一个10MB以内PDF文件的处理时间和稳定性。
  - **API响应**: 验证主要API端点的响应时间在合理范围内（<3秒）。
