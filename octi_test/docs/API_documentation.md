# OCTI智能评估系统 - API文档 (修订版)

## 1. 概述
本API旨在为OCTI智能评估系统的前端应用提供全面的后端服务支持。所有API都遵循RESTful设计原则，并使用JSON格式进行数据交换。

- **根URL**: `/api`
- **认证**: 所有需要认证的端点都必须在请求头中包含有效的JWT `Authorization: Bearer <token>`。
- **版本**: v1.0
- **最后更新**: 2025年1月

---

## 2. 认证API (`/api/auth`)

### **POST** `/api/auth/register`
- **功能**: 用户注册
- **状态**: 🔴 待实现 → ✅ 需要实现
- **认证**: 不需要
- **请求体**:
  ```json
  {
    "email": "<EMAIL>",
    "password": "password123",
    "name": "用户名",
    "organizationName": "公司名称"
  }
  ```
- **响应 (201)**:
  ```json
  {
    "success": true,
    "data": {
      "user": {
        "id": "user-123",
        "email": "<EMAIL>",
        "name": "用户名",
        "role": "USER"
      },
      "message": "用户注册成功"
    },
    "meta": {
      "timestamp": "2025-01-XX",
      "requestId": "req-123"
    }
  }
  ```

### **POST** `/api/auth/login`
- **功能**: 用户登录  
- **状态**: 🔴 待实现 → ✅ 需要实现
- **认证**: 不需要
- **请求体**:
  ```json
  {
    "email": "<EMAIL>",
    "password": "password123"
  }
  ```
- **响应 (200)**:
  ```json
  {
    "success": true,
    "data": {
      "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
      "user": {
        "id": "user-123",
        "email": "<EMAIL>",
        "name": "用户名",
        "role": "USER"
      }
    },
    "meta": {
      "timestamp": "2025-01-XX",
      "requestId": "req-123"
    }
  }
  ```

### **POST** `/api/auth/logout`
- **功能**: 用户登出
- **状态**: 🔴 待实现
- **认证**: 需要
- **响应 (200)**:
  ```json
  {
    "success": true,
    "data": {
      "message": "登出成功"
    },
    "meta": {
      "timestamp": "2025-01-XX",
      "requestId": "req-123"
    }
  }
  ```

### **GET** `/api/auth/me`
- **功能**: 获取当前用户信息
- **状态**: 🔴 待实现
- **认证**: 需要
- **响应 (200)**:
  ```json
  {
    "success": true,
    "data": {
      "user": {
        "id": "user-123",
        "email": "<EMAIL>",
        "name": "用户名",
        "role": "USER",
        "createdAt": "2025-01-XX"
      }
    },
    "meta": {
      "timestamp": "2025-01-XX",
      "requestId": "req-123"
    }
  }
  ```

---

## 3. 评估管理API (`/api/assessments`)

### **POST** `/api/assessments`
- **功能**: 创建新的评估
- **状态**: ✅ 已实现
- **认证**: 需要
- **请求体**:
  ```json
  {
    "title": "组织能力评估",
    "description": "评估描述",
    "type": "team_collaboration",
    "dimensions": ["leadership", "innovation"],
    "requirements": {
      "version": "standard"
    }
  }
  ```
- **响应 (201)**:
  ```json
  {
    "success": true,
    "data": {
      "assessmentId": "assessment-id-456",
      "questionnaire": { ... }
    },
    "meta": {
      "timestamp": "2025-01-XX",
      "requestId": "req-123"
    }
  }
  ```

### **GET** `/api/assessments`
- **功能**: 获取评估列表
- **状态**: ✅ 已实现
- **认证**: 需要
- **查询参数**:
  - `page`: 页码 (默认: 1)
  - `limit`: 每页数量 (默认: 10)
  - `type`: 评估类型过滤
- **响应 (200)**:
  ```json
  {
    "success": true,
    "data": {
      "assessments": [...],
      "pagination": {
        "page": 1,
        "limit": 10,
        "total": 50,
        "totalPages": 5
      }
    }
  }
  ```

---

## 4. 评估执行API (`/api/assessment`)

### **POST** `/api/assessment/evaluate`
- **功能**: 评估问卷回答并生成报告
- **状态**: ✅ 已实现
- **认证**: 需要
- **请求体**:
  ```json
  {
    "assessmentId": "assessment-id-456",
    "responses": [
      {
        "questionId": "q1",
        "answer": "选项A"
      }
    ],
    "version": "standard",
    "options": {
      "includeRecommendations": true,
      "includeBenchmarking": false,
      "dataFusion": {
        "uploadedFiles": ["file-id-1"],
        "webData": ["url-data-1"]
      }
    }
  }
  ```
- **响应 (201)**:
  ```json
  {
    "success": true,
    "data": {
      "assessmentId": "assessment-id-456",
      "report": { ... },
      "generatedAt": "2025-01-XX"
    }
  }
  ```

### **GET** `/api/assessment/evaluate?assessmentId=xxx`
- **功能**: 获取评估报告
- **状态**: ✅ 已实现
- **认证**: 需要
- **响应 (200)**:
  ```json
  {
    "success": true,
    "data": {
      "status": "completed",
      "report": { ... },
      "reportUrl": "https://storage.example.com/reports/xxx.pdf"
    }
  }
  ```

### **OPTIONS** `/api/assessment/evaluate`
- **功能**: 获取评估统计信息
- **状态**: ✅ 已实现
- **响应 (200)**:
  ```json
  {
    "success": true,
    "data": {
      "statistics": {
        "totalAssessments": 100,
        "cacheHitRate": 0.85,
        "lastUpdated": "2025-01-XX"
      },
      "capabilities": {
        "supportedVersions": ["standard", "professional"],
        "maxResponses": 60,
        "supportedDataFusion": true
      }
    }
  }
  ```

---

## 5. 测试API (`/api/test`)

### **POST** `/api/test/llm`
- **功能**: 测试LLM连接状态
- **状态**: ✅ 已实现
- **认证**: 不需要 (仅开发环境)
- **响应 (200)**:
  ```json
  {
    "success": true,
    "message": "API密钥环境变量检查完成",
    "environment": {
      "minimax_configured": true,
      "deepseek_configured": true
    },
    "timestamp": "2025-01-XX"
  }
  ```

---

## 6. 【待实现】外部数据源API

### **POST** `/api/assessment/{assessmentId}/dataSource/upload`
- **功能**: 为专业版评估上传外部文件
- **状态**: 🔴 待实现
- **认证**: 需要

### **POST** `/api/assessment/{assessmentId}/dataSource/scrape`
- **功能**: 为专业版评估提交URL进行网络数据采集
- **状态**: 🔴 待实现
- **认证**: 需要

---

## 7. 【待实现】配置管理API

### **GET** `/api/admin/config/{configType}`
- **功能**: 获取指定类型的配置文件内容
- **状态**: 🔴 待实现
- **认证**: 需要 (仅限ADMIN角色)

### **POST** `/api/admin/config/{configType}`
- **功能**: 更新指定类型的配置文件
- **状态**: 🔴 待实现
- **认证**: 需要 (仅限ADMIN角色)

---

## 8. 统一错误响应格式

所有API错误响应都遵循以下格式：

```json
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "错误描述",
    "details": { ... }
  },
  "meta": {
    "timestamp": "2025-01-XX",
    "requestId": "req-123"
  }
}
```

### 常见错误码
- `VALIDATION_ERROR`: 输入验证失败 (400)
- `UNAUTHORIZED`: 未授权访问 (401)
- `FORBIDDEN`: 权限不足 (403)
- `NOT_FOUND`: 资源不存在 (404)
- `INTERNAL_ERROR`: 服务器内部错误 (500)

---

## 9. 实现状态总览 (更新版)

| API端点 | 状态 | 优先级 | 文档状态 | 备注 |
|---------|------|--------|----------|------|
| `/api/assessments` | ✅ 已实现 | 高 | ✅ 已文档化 | 核心功能 |
| `/api/assessment/evaluate` | ✅ 已实现 | 高 | ✅ 已文档化 | 核心功能 |
| `/api/agents` | ✅ 已实现 | 高 | ✅ 新增文档 | 智能体管理 |
| `/api/config` | ✅ 已实现 | 中 | ✅ 新增文档 | 配置管理 |
| `/api/system` | ✅ 已实现 | 中 | ✅ 新增文档 | 系统监控 |
| `/api/reports/{id}` | ✅ 已实现 | 中 | ✅ 新增文档 | 报告管理 |
| `/api/test/llm` | ✅ 已实现 | 低 | ✅ 已文档化 | 开发工具 |
| `/api/auth/*` | 🔴 待实现 | 高 | ✅ 已文档化 | 用户认证 |
| `/api/assessment/*/dataSource/*` | 🔴 待实现 | 中 | ✅ 已文档化 | 专业版功能 |

### 统计信息
- **已实现**: 7/9 (78%)
- **已文档化**: 9/9 (100%)
- **响应格式统一**: 8/9 (89%)
- **认证集成**: 2/9 (22%)

### 下一步行动
1. **立即实现**: 认证API (`/api/auth/*`)
2. **中期实现**: 数据源API (`/api/assessment/*/dataSource/*`)
3. **持续优化**: 响应格式完全统一
4. **安全加固**: 所有API集成认证中间件

---

## 10. 开发建议

### 立即需要实现的API
1. **用户认证API** (`/api/auth/*`) - 系统基础功能
2. **数据源API** (`/api/assessment/*/dataSource/*`) - 专业版核心功能
3. **配置管理API** (`/api/admin/config/*`) - 运营管理功能

### API设计改进建议
1. **版本控制**: 在URL中添加版本号 `/api/v1/`
2. **分页标准化**: 统一分页参数和响应格式
3. **缓存策略**: 为GET请求添加适当的缓存头
4. **限流机制**: 实现API调用频率限制

---

## 11. 智能体管理API (`/api/agents`)

### **GET** `/api/agents`
- **功能**: 获取智能体状态或列表
- **状态**: ✅ 已实现
- **认证**: 需要
- **查询参数**:
  - `agent`: 智能体名称 (可选)
  - `action`: 操作类型 (`status` | `list`, 默认: `list`)
- **响应 (200)**:
  ```json
  {
    "success": true,
    "data": {
      "agents": [
        {
          "name": "question-designer",
          "status": "active",
          "version": "1.0.0"
        },
        {
          "name": "organization-tutor", 
          "status": "active",
          "version": "1.0.0"
        }
      ]
    },
    "meta": {
      "timestamp": "2025-01-XX",
      "requestId": "req-123"
    }
  }
  ```

### **POST** `/api/agents`
- **功能**: 执行智能体任务
- **状态**: ✅ 已实现
- **认证**: 需要
- **请求体**:
  ```json
  {
    "agentName": "question-designer",
    "input": {
      "type": "team_collaboration",
      "dimensions": ["leadership", "innovation"],
      "requirements": {
        "version": "standard"
      }
    }
  }
  ```
- **响应 (200)**:
  ```json
  {
    "success": true,
    "data": {
      "result": { ... },
      "executionTime": 1500
    },
    "meta": {
      "timestamp": "2025-01-XX",
      "requestId": "req-123"
    }
  }
  ```

---

## 12. 系统管理API (`/api/system`)

### **GET** `/api/system`
- **功能**: 获取系统状态
- **状态**: ✅ 已实现
- **认证**: 需要 (仅限ADMIN角色)
- **查询参数**:
  - `action`: 操作类型 (`health` | `status` | `metrics`, 默认: `status`)
- **响应 (200)**:
  ```json
  {
    "success": true,
    "data": {
      "status": "healthy",
      "uptime": 86400,
      "version": "1.0.0",
      "environment": "production"
    },
    "meta": {
      "timestamp": "2025-01-XX",
      "requestId": "req-123"
    }
  }
  ```

---

## 13. 报告管理API (`/api/reports`)

### **GET** `/api/reports/{id}`
- **功能**: 获取指定报告
- **状态**: ✅ 已实现
- **认证**: 需要
- **响应 (200)**:
  ```json
  {
    "success": true,
    "data": {
      "id": "report-123",
      "assessmentId": "assessment-456",
      "organizationName": "示例公司",
      "sections": [...],
      "createdAt": "2025-01-XX",
      "updatedAt": "2025-01-XX"
    },
    "meta": {
      "timestamp": "2025-01-XX",
      "requestId": "req-123"
    }
  }
  ```

### **PUT** `/api/reports/{id}`
- **功能**: 更新报告
- **状态**: ✅ 已实现
- **认证**: 需要
- **请求体**:
  ```json
  {
    "organizationName": "更新后的公司名",
    "sections": [...]
  }
  ```

### **DELETE** `/api/reports/{id}`
- **功能**: 删除报告
- **状态**: ✅ 已实现
- **认证**: 需要
- **响应 (200)**:
  ```json
  {
    "success": true,
    "message": "报告已删除",
    "meta": {
      "timestamp": "2025-01-XX",
      "requestId": "req-123"
    }
  }
  ```

---

## 14. 配置管理API (`/api/config`)

### **GET** `/api/config`
- **功能**: 获取配置
- **状态**: ✅ 已实现
- **认证**: 需要 (仅限ADMIN角色)
- **查询参数**:
  - `key`: 配置键名 (可选)
  - `batch`: 批量获取配置键 (逗号分隔)
- **响应 (200)**:
  ```json
  {
    "success": true,
    "data": {
      "config": { ... }
    },
    "meta": {
      "timestamp": "2025-01-XX",
      "requestId": "req-123"
    }
  }
  ```

### **POST** `/api/config`
- **功能**: 设置配置
- **状态**: ✅ 已实现
- **认证**: 需要 (仅限ADMIN角色)
- **请求体**:
  ```json
  {
    "action": "set",
    "key": "question_design_prompt",
    "value": { ... }
  }
  ```

### **PUT** `/api/config`
- **功能**: 重新加载配置
- **状态**: ✅ 已实现
- **认证**: 需要 (仅限ADMIN角色)
- **响应 (200)**:
  ```json
  {
    "success": true,
    "message": "配置已重新加载",
    "meta": {
      "timestamp": "2025-01-XX",
      "requestId": "req-123"
    }
  }
  ```


