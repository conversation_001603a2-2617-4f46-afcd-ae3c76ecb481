# OCTI智能评估系统 - LLM模型实现文档

## 1. LLM集成架构

系统的LLM集成采用**多模型、可扩展**的架构，通过统一的客户端和提示词构建器与不同的LLM服务进行交互。

```mermaid
graph TD
    subgraph "智能体"
        A1[问卷设计师]
        A2[组织评估导师]
    end

    subgraph "提示词工程"
        B1[问卷提示词构建器]
        B2[分析提示词构建器]
    end

    subgraph "LLM API客户端 (可扩展)"
        C[LLMApiClient]
    end

    subgraph "外部LLM服务"
        D1[MiniMax API]
        D2[DeepSeek API]
    end

    A1 --> B1;
    A2 --> B2;
    B1 & B2 --> C;
    C -- "根据模型选择调用" --> D1;
    C -- "根据模型选择调用" --> D2;
```

## 2. 核心组件实现

### 2.1 LLM API客户端 (`LLMApiClient`)

**【已修复】** 此客户端经过重构，以支持调用多个不同的LLM API。它不再是硬编码的，而是通过策略模式动态选择与特定模型匹配的处理器。

```typescript
// src/services/llm/client/LLMApiClient.ts

import { getApiKey } from '@/services/llm/api-key-manager';
import { MiniMaxApiHandler } from './handlers/minimax-handler';
import { DeepSeekApiHandler } from './handlers/deepseek-handler';

// API处理器接口
interface ApiHandler {
  call(prompt: any, apiKey: string): Promise<any>;
}

export class LLMApiClient {
  private handlers: Map<string, ApiHandler>;

  constructor() {
    this.handlers = new Map();
    this.handlers.set('minimax', new MiniMaxApiHandler());
    this.handlers.set('deepseek', new DeepSeekApiHandler());
  }

  /**
   * 根据模型名称调用相应的LLM API
   * @param modelName - 'minimax' or 'deepseek'
   * @param prompt - 为目标API格式化好的提示词对象
   * @returns LLM的响应
   */
  async call(modelName: string, prompt: any): Promise<any> {
    const handler = this.handlers.get(modelName);
    if (!handler) {
      throw new Error(`不支持的模型: ${modelName}`);
    }

    const apiKey = await getApiKey(modelName);
    if (!apiKey) {
      throw new Error(`缺少 ${modelName} 的API密钥`);
    }

    return handler.call(prompt, apiKey);
  }
}

// 示例处理器：
// src/services/llm/client/handlers/deepseek-handler.ts
export class DeepSeekApiHandler implements ApiHandler {
  async call(prompt: any, apiKey: string): Promise<any> {
    const response = await fetch('https://api.deepseek.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`,
      },
      body: JSON.stringify(prompt),
    });
    if (!response.ok) {
      throw new Error(`DeepSeek API error: ${response.statusText}`);
    }
    return response.json();
  }
}
```

### 2.2 提示词构建器 (`PromptBuilder`)

提示词构建器负责根据配置文件和用户输入，动态生成符合特定LLM API格式要求的提示词。

```typescript
// src/services/llm/prompt/AnalysisPromptBuilder.ts

export class AnalysisPromptBuilder {
  private config: any;

  constructor(config: any) {
    this.config = config;
  }

  /**
   * 构建分析报告的提示词
   * @param answers - 用户问卷答案
   * @param version - 'standard' or 'professional'
   * @param externalData - 【新增】处理后的外部数据
   * @returns 为LLM API格式化好的提示词
   */
  build(answers: any, version: 'standard' | 'professional', externalData: any = null): any {
    const systemMessage = this.config.prompt_template.system_message;
    const framework = JSON.stringify(this.config.framework, null, 2);
    const answersText = JSON.stringify(answers, null, 2);
    
    let taskDescription = `请基于以下问卷答案进行分析:\n${answersText}\n`;

    // 【新增】如果存在外部数据，则加入到提示词中
    if (version === 'professional' && externalData) {
      const externalDataText = JSON.stringify(externalData, null, 2);
      taskDescription += `\n请结合以下补充材料进行深度融合分析:\n${externalDataText}\n`;
    }

    const reportStructure = this.config.output_formats[version]
      .map(s => `- ${s.section}: ${s.content}`)
      .join('\n');

    taskDescription += `\n请严格按照以下结构生成JSON格式的报告:\n${reportStructure}`;

    // 返回适用于特定模型的提示词格式
    return {
      model: "deepseek-chat", // 示例
      messages: [
        { role: "system", content: `${systemMessage}\n分析框架:\n${framework}` },
        { role: "user", content: taskDescription },
      ],
      response_format: { type: "json_object" }
    };
  }
}
```

## 3. 智能体实现

### 3.1 问卷设计师 (`QuestionnaireAgent`)

负责生成问卷。**【已增强】** 现在支持根据版本类型（标准版/专业版）生成不同深度的问题。

```typescript
// src/services/agents/QuestionnaireAgent.ts

import { LLMApiClient } from '../llm/client/LLMApiClient';
import { QuestionnairePromptBuilder } from '../llm/prompt/QuestionnairePromptBuilder';

export class QuestionnaireAgent {
  private llmClient: LLMApiClient;
  private config: any;

  constructor(config: any) {
    this.llmClient = new LLMApiClient();
    this.config = config;
  }

  /**
   * 生成问卷
   * @param version - 'standard' 或 'professional'
   * @param organizationInfo - 组织基本信息（可选）
   * @returns 生成的问卷JSON
   */
  async generate(version: 'standard' | 'professional', organizationInfo?: any): Promise<any> {
    const builder = new QuestionnairePromptBuilder(this.config);
    const prompt = builder.build(version, organizationInfo);
    
    // 问卷生成通常使用一个固定的、性价比高的模型
    const response = await this.llmClient.call('minimax', prompt);
    
    // 此处应有对response的验证和解析逻辑
    const questionnaire = JSON.parse(response.choices[0].message.content);
    
    // 验证生成的问卷是否符合深度要求
    this.validateQuestionDepth(questionnaire, version);
    
    return questionnaire;
  }

  /**
   * 验证问卷深度是否符合版本要求
   * @param questionnaire - 生成的问卷
   * @param version - 版本类型
   */
  private validateQuestionDepth(questionnaire: any, version: 'standard' | 'professional'): void {
    const versionSettings = this.config.configuration_parameters.version_specific_settings[version];
    const complexityKeywords = versionSettings.complexity_keywords;
    
    // 简单验证：检查问题文本中是否包含相应的复杂度关键词
    let keywordMatchCount = 0;
    questionnaire.questions?.forEach((question: any) => {
      const questionText = question.text.toLowerCase();
      if (complexityKeywords.some((keyword: string) => questionText.includes(keyword))) {
        keywordMatchCount++;
      }
    });
    
    // 至少30%的问题应该体现相应的深度特征
    const expectedMinMatches = Math.floor((questionnaire.questions?.length || 0) * 0.3);
    if (keywordMatchCount < expectedMinMatches) {
      console.warn(`问卷深度验证警告: ${version}版本问题深度可能不足`);
    }
  }
}
```

#### 3.1.1 问卷提示词构建器增强

```typescript
// src/services/llm/prompt/QuestionnairePromptBuilder.ts

export class QuestionnairePromptBuilder {
  private config: any;

  constructor(config: any) {
    this.config = config;
  }

  /**
   * 构建问卷生成的提示词
   * @param version - 'standard' 或 'professional'
   * @param organizationInfo - 组织信息（可选）
   * @returns 格式化的提示词
   */
  build(version: 'standard' | 'professional', organizationInfo?: any): any {
    const systemMessage = this.config.prompt_template.system_message;
    const framework = JSON.stringify(this.config.prompt_template.framework, null, 2);
    const versionSettings = this.config.configuration_parameters.version_specific_settings[version];
    const depthInstructions = this.config.configuration_parameters.depth_control_instructions[version];
    
    let taskDescription = `请生成一套OCTI组织能力评估问卷，包含60道题目（每个维度15题）。\n\n`;
    
    // 添加版本特定的深度控制指令
    taskDescription += `**问题深度要求（${version === 'standard' ? '标准版' : '专业版'}）**:\n`;
    taskDescription += `- 深度级别: ${versionSettings.question_depth_level}\n`;
    taskDescription += `- 复杂度关键词: ${versionSettings.complexity_keywords.join('、')}\n`;
    taskDescription += `- 情境复杂度: ${versionSettings.scenario_complexity}\n`;
    taskDescription += `- 认知负荷: ${versionSettings.cognitive_load}\n`;
    taskDescription += `- 问题风格: ${versionSettings.question_style}\n\n`;
    
    taskDescription += `**具体要求**:\n${depthInstructions}\n\n`;
    
    if (organizationInfo) {
      taskDescription += `**组织背景信息**:\n${JSON.stringify(organizationInfo, null, 2)}\n\n`;
    }
    
    taskDescription += `请严格按照以下JSON格式返回问卷:\n`;
    taskDescription += `{
      "questionnaire": {
        "version": "${version}",
        "total_questions": 60,
        "dimensions": {
          "SF": { "questions": [...] },
          "IT": { "questions": [...] },
          "MV": { "questions": [...] },
          "AD": { "questions": [...] }
        }
      }
    }`;

    return {
      model: "abab6.5s-chat",
      messages: [
        { role: "system", content: `${systemMessage}\n\n评估框架:\n${framework}` },
        { role: "user", content: taskDescription },
      ],
      response_format: { type: "json_object" }
    };
  }
}
```

### 3.2 组织评估导师 (`AnalysisAgent`)

**【已修复】** 负责生成分析报告。专业版逻辑中加入了**数据融合**步骤，并实现了**双模型协作**。

```typescript
// src/services/agents/AnalysisAgent.ts

import { LLMApiClient } from '../llm/client/LLMApiClient';
import { AnalysisPromptBuilder } from '../llm/prompt/AnalysisPromptBuilder';
import { DataFusionEngine } from '../data/DataFusionEngine'; // 【新增】

export class AnalysisAgent {
  private llmClient: LLMApiClient;
  private config: any;
  private dataFusionEngine: DataFusionEngine; // 【新增】

  constructor(config: any) {
    this.llmClient = new LLMApiClient();
    this.config = config;
    this.dataFusionEngine = new DataFusionEngine(); // 【新增】
  }

  async analyze(assessmentId: string, answers: any, version: 'standard' | 'professional'): Promise<any> {
    if (version === 'standard') {
      return this.runStandardAnalysis(answers);
    } else {
      return this.runProfessionalAnalysis(assessmentId, answers);
    }
  }

  private async runStandardAnalysis(answers: any): Promise<any> {
    const builder = new AnalysisPromptBuilder(this.config);
    const prompt = builder.build(answers, 'standard');
    
    const response = await this.llmClient.call('minimax', prompt);
    return JSON.parse(response.choices[0].message.content);
  }

  private async runProfessionalAnalysis(assessmentId: string, answers: any): Promise<any> {
    // 步骤1: 【新增】调用数据融合引擎获取外部数据
    const externalData = await this.dataFusionEngine.getFusedData(assessmentId);

    // 步骤2: 使用主模型（如DeepSeek）进行深度分析
    const builder = new AnalysisPromptBuilder(this.config);
    const deepseekPrompt = builder.build(answers, 'professional', externalData);
    const deepseekResponse = await this.llmClient.call('deepseek', deepseekPrompt);
    const mainReport = JSON.parse(deepseekResponse.choices[0].message.content);

    // 步骤3: 【新增】使用次模型（如MiniMax）进行交叉验证或生成摘要（双模型协作）
    const summaryPrompt = this.createSummaryPrompt(mainReport);
    const minimaxResponse = await this.llmClient.call('minimax', summaryPrompt);
    const summary = JSON.parse(minimaxResponse.choices[0].message.content);

    // 步骤4: 合并最终报告
    mainReport.executive_summary = summary; // 将摘要合并到主报告中
    return mainReport;
  }

  private createSummaryPrompt(report: any): any {
    // ... 此处省略为生成摘要而创建新提示词的逻辑 ...
    return { /* ... */ };
  }
}
```

### 3.3 【新增】数据融合引擎 (`DataFusionEngine`)

这是一个新的服务，负责从数据库中检索和准备用于分析的外部数据。

```typescript
// src/services/data/DataFusionEngine.ts
import { prisma } from '@/prisma/client';

export class DataFusionEngine {
  /**
   * 根据评估ID，获取所有已处理完成的外部数据源内容
   * @param assessmentId 
   * @returns 包含所有外部数据文本的对象，或null
   */
  async getFusedData(assessmentId: string): Promise<any | null> {
    const dataSources = await prisma.externalDataSource.findMany({
      where: {
        assessmentId: assessmentId,
        status: 'COMPLETED',
      },
      include: {
        processedData: true,
      },
    });

    if (dataSources.length === 0) {
      return null;
    }

    const fusedData = {
      uploaded_files: [],
      scraped_urls: [],
    };

    for (const source of dataSources) {
      if (source.type === 'FILE') {
        fusedData.uploaded_files.push({
          fileName: source.source,
          content: source.processedData?.extractedContent,
        });
      } else if (source.type === 'URL') {
        fusedData.scraped_urls.push({
          url: source.source,
          content: source.processedData?.extractedContent,
        });
      }
    }

    return fusedData;
  }
}