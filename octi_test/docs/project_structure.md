# OCTI智能评估系统 - 项目结构

## 1. 目录结构

```
octi-assessment-system/
├── .github/                      # GitHub Actions配置
├── .vscode/                      # VSCode配置
├── public/                       # 静态资源
│   ├── images/                   # 图片资源
│   ├── fonts/                    # 字体资源
│   └── locales/                  # 国际化资源
├── src/
│   ├── components/               # React组件
│   │   ├── common/               # 通用组件
│   │   ├── layout/               # 布局组件
│   │   ├── questionnaire/        # 问卷相关组件
│   │   └── report/               # 报告相关组件
│   ├── config/                   # 配置文件
│   │   ├── question_design_prompt.json  # 问卷设计师配置
│   │   └── organization_tutor_prompt.json  # 组织评估导师配置
│   ├── hooks/                    # 自定义React Hooks
│   ├── pages/                    # 页面组件
│   │   ├── api/                  # API路由
│   │   ├── assessment/           # 评估相关页面
│   │   ├── auth/                 # 认证相关页面
│   │   ├── dashboard/            # 仪表盘页面
│   │   └── report/               # 报告相关页面
│   ├── services/                 # 服务层
│   │   ├── api/                  # API客户端
│   │   ├── llm/                  # LLM服务
│   │   │   ├── questionnaire-agent/  # 问卷设计师
│   │   │   └── analysis-agent/       # 组织评估导师
│   │   └── storage/              # 存储服务
│   ├── store/                    # 状态管理
│   ├── styles/                   # 样式文件
│   ├── types/                    # TypeScript类型定义
│   └── utils/                    # 工具函数
├── prisma/                       # Prisma ORM
│   └── schema.prisma             # 数据库模型
├── tests/                        # 测试文件
│   ├── unit/                     # 单元测试
│   ├── integration/              # 集成测试
│   └── e2e/                      # 端到端测试
├── .env.example                  # 环境变量示例
├── .eslintrc.js                  # ESLint配置
├── .prettierrc                   # Prettier配置
├── jest.config.js                # Jest配置
├── next.config.js                # Next.js配置
├── package.json                  # 项目依赖
├── tsconfig.json                 # TypeScript配置
└── README.md                     # 项目说明
```

## 2. 核心模块说明

### 2.1 前端模块

#### 2.1.1 问卷渲染引擎
- `src/components/questionnaire/`：问卷相关组件
  - `QuestionnaireRenderer.tsx`：问卷渲染器
  - `QuestionTypes/`：各种题型组件
  - `Navigation.tsx`：问卷导航
  - `ProgressBar.tsx`：进度条

#### 2.1.2 报告展示模块
- `src/components/report/`：报告相关组件
  - `ReportViewer.tsx`：报告查看器
  - `Charts/`：各种图表组件
  - `RecommendationCard.tsx`：建议卡片
  - `ExportTools.tsx`：导出工具

#### 2.1.3 管理界面
- `src/pages/dashboard/`：管理界面
  - `ConfigEditor.tsx`：配置编辑器
  - `ABTestingPanel.tsx`：A/B测试面板
  - `AnalyticsView.tsx`：数据分析视图

### 2.2 后端模块

#### 2.2.1 智能体服务
- `src/services/llm/questionnaire-agent/`：问卷设计师
  - `index.ts`：服务入口
  - `prompt-builder.ts`：提示词构建器
  - `response-parser.ts`：响应解析器

- `src/services/llm/analysis-agent/`：组织评估导师
  - `index.ts`：服务入口
  - `standard-analyzer.ts`：标准版分析器
  - `professional-analyzer.ts`：专业版分析器
  - `model-fusion.ts`：模型融合

#### 2.2.2 配置管理服务
- `src/services/config/`：配置管理
  - `config-engine.ts`：配置引擎
  - `validator.ts`：配置验证器
  - `version-control.ts`：版本控制

#### 2.2.3 API服务
- `src/pages/api/`：API路由
  - `assessment/`：评估相关API
  - `report/`：报告相关API
  - `config/`：配置相关API

### 2.3 数据存储

#### 2.3.1 数据库模型
- `prisma/schema.prisma`：数据库模型定义
  - 用户模型
  - 组织模型
  - 评估模型
  - 报告模型
  - 配置模型

## 3. 依赖管理

### 3.1 核心依赖
- Next.js：React框架
- React：UI库
- TypeScript：类型系统
- Prisma：ORM
- Tailwind CSS：样式框架
- Axios：HTTP客户端
- Zustand：状态管理
- ECharts：图表库
- React Hook Form：表单管理
- Zod：验证库

### 3.2 开发依赖
- ESLint：代码检查
- Prettier：代码格式化
- Jest：测试框架
- Testing Library：测试工具
- Cypress：端到端测试
- Husky：Git钩子
- Commitlint：提交信息检查

## 4. 环境配置

### 4.1 开发环境
- Node.js 18+
- npm 8+
- PostgreSQL 14+
- Redis 6+

### 4.2 环境变量
- `DATABASE_URL`：数据库连接URL
- `REDIS_URL`：Redis连接URL
- `MINIMAX_API_KEY`：MiniMax API密钥
- `DEEPSEEK_API_KEY`：DeepSeek API密钥
- `NEXT_PUBLIC_API_URL`：API基础URL
- `SESSION_SECRET`：会话密钥