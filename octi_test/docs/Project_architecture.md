# OCTI智能评估系统 - 项目架构文档

## 1. 架构概述

OCTI智能评估系统v4.0采用基于**配置驱动的智能体架构**，并引入了**多源数据融合**能力。系统被设计为模块化、可扩展和易于维护的，以支持快速迭代和差异化的产品服务。

### 1.1 核心设计原则
- **配置驱动**: 核心业务逻辑（问卷生成、分析策略）由JSON文件配置，实现业务与代码解耦。
- **智能体化**: 将问卷生成和分析功能封装为独立的、可配置的智能体。
- **多源数据融合**: 具备处理用户上传文档和采集网络公开数据的能力，为专业版提供深度分析基础。
- **服务解耦**: 各个模块（如API、数据采集、数据处理）职责清晰，可独立部署和扩展。

### 1.2 v4.0 架构演进图

```mermaid
graph TD
    subgraph "OCTI 系统 v3.0 (单一流程架构)"
        A[用户] --> B{问卷系统 (固定题目)};
        B --> C{后端分析 (单一逻辑)};
        C --> D[生成报告];
    end

    subgraph "OCTI 系统 v4.0 (配置驱动 + 数据融合架构)"
        U[用户] --> |发起评估| PA(应用层);

        subgraph "配置层"
            C1[question_design_prompt.json];
            C2[organization_tutor_prompt.json];
        end
        
        subgraph "智能体层"
            A1[问卷设计师] -- 读取 --> C1;
            A2[组织评估导师] -- 读取 --> C2;
        end

        subgraph "应用层 (Next.js)"
            PA -- 调用 --> A1;
            A1 -- 生成问卷 --> PA;
            PA -- 渲染问卷 --> U;
            U -- 提交答案 --> PA;
            PA -- 调用 --> A2;
            A2 -- 生成报告 --> PA;
            PA -- 展示报告 --> U;
        end

        subgraph "数据采集层 (专业版)"
            U -- 上传文件 --> D1[文件上传服务];
            PA -- 触发 --> D2[网络数据采集服务];
        end

        subgraph "数据处理层 (专业版)"
            D1 & D2 -- 直接处理 --> DB[(数据库)];
        end
        
        A2 -- 融合分析 --> DB;
    end
```

## 2. 系统分层架构

OCTI v4.0 系统分为以下几个核心层次：

```
┌────────────────────────────────────────────────────────────────────────┐
│                                  用户层                                  │
│                      (Web浏览器, 移动端浏览器)                         │
└──────────────────────────────────┬─────────────────────────────────────┘
                                   │
┌──────────────────────────────────▼─────────────────────────────────────┐
│                              应用层 (Next.js)                            │
│ ┌─────────────────┐ ┌─────────────────┐ ┌──────────────────┐           │
│ │  问卷渲染引擎   │ │  报告展示模块   │ │  配置管理后台    │           │
│ └─────────────────┘ └─────────────────┘ └──────────────────┘           │
└──────────────────────────────────┬─────────────────────────────────────┘
                                   │
┌──────────────────────────────────▼─────────────────────────────────────┐
│                              智能体层                                  │
│ ┌─────────────────┐ ┌─────────────────┐ ┌──────────────────┐           │
│ │   问卷设计师    │ │  组织评估导师   │ │    配置管理器    │           │
│ └─────────────────┘ └─────────────────┘ └──────────────────┘           │
└───────────────────┬──────────────────┬─────────────────────────────────┘
                    │                  │
┌───────────────────▼──────────────────┐ │
│            API服务层 (Next.js API Routes)            │ │
└───────────────────┬──────────────────┘ │
                    │                    │
┌───────────────────▼────────────────────▼────────────────────────────────┐
│                        后端服务与数据处理层                            │
│ ┌─────────────────┐ ┌─────────────────┐ ┌──────────────────┐           │
│ │ 数据采集服务    │ │ 数据处理服务    │ │   数据融合引擎   │           │
│ │ (文件上传/爬虫) │ │ (简单异步处理)  │ │ (集成于评估导师) │           │
│ └─────────────────┘ └─────────────────┘ └──────────────────┘           │
└──────────────────────────────────┬─────────────────────────────────────┘
                                   │
┌──────────────────────────────────▼─────────────────────────────────────┐
│                                数据持久层                                │
│ ┌─────────────────┐ ┌─────────────────┐ ┌──────────────────┐           │
│ │ PostgreSQL数据库  │ │   Redis缓存     │ │  对象存储 (COS)  │           │
│ └─────────────────┘ └─────────────────┘ └──────────────────┘           │
└────────────────────────────────────────────────────────────────────────┘
```

## 3. 核心组件详解

### 3.1 配置层
- **`question_design_prompt.json`**: 定义问卷设计师的行为，包括OCTI框架、问题类型、复杂度等。
- **`organization_tutor_prompt.json`**: 定义组织评估导师的行为，包括分析框架、版本差异、报告结构等。

### 3.2 智能体层
- **问卷设计师**: 根据配置动态生成问卷JSON数据。
- **组织评估导师**: 核心分析模块。
    - **标准版**: 仅分析问卷答案。
    - **专业版**: 集成**数据融合引擎**，结合问卷答案、外部文档和网络数据进行综合评估。
- **配置管理器**: 负责配置的加载、验证、缓存和热更新。
    ```javascript
    // 配置引擎核心实现
    class ConfigEngine {
      constructor() {
        this.configCache = new Map(); // 配置缓存
        this.validators = new Map();  // 验证器
        this.subscribers = new Set(); // 订阅者
      }
      
      // 加载和验证配置
      async loadConfig(configType, version = 'latest') {
        const configKey = `${configType}:${version}`;
        
        // 检查缓存
        if (this.configCache.has(configKey)) {
          return this.configCache.get(configKey);
        }
        
        // 从存储加载配置
        const config = await this.fetchConfig(configType, version);
        const validatedConfig = await this.validateConfig(config, configType);
        
        // 更新缓存
        this.configCache.set(configKey, validatedConfig);
        return validatedConfig;
      }
      
      // 热更新配置
      async updateConfig(configType, newConfig) {
        const validatedConfig = await this.validateConfig(newConfig, configType);
        const version = this.generateVersion();
        
        await this.saveConfig(configType, validatedConfig, version);
        this.configCache.set(`${configType}:latest`, validatedConfig);
        
        // 通知订阅者
        this.notifySubscribers(configType, validatedConfig);
      }
    }
    ```

### 3.3 应用层 (Next.js)
- **问卷渲染引擎**: 将智能体生成的JSON动态渲染为可交互的React问卷组件。
- **报告展示模块**: 可视化展示结构化的分析报告，支持多级下钻。
- **配置管理后台**: 提供给运营人员的可视化界面，用于管理和A/B测试配置文件。

### 3.4 数据采集服务 (专业版核心)
- **文件上传服务**:
    - **技术栈**: Next.js API Route + `multer`
    - **功能**: 接收用户上传的年报、财报等文件（PDF, DOCX, TXT），将其存入对象存储（COS），并进行简单的异步处理。
- **网络数据采集服务**:
    - **技术栈**: `Puppeteer` / `Cheerio`
    - **功能**: 根据用户提供的URL，抓取其官网、公开报告等信息，进行初步清洗后，进行简单的异步处理。

### 3.5 数据处理层 (专业版核心)
- **文件处理服务**:
    - **技术栈**: Next.js API Route + `pdf-parse`
    - **功能**: 直接处理用户上传的文件，提取文本内容并存入数据库。处理过程同步完成，简化系统复杂度。
- **网页数据处理服务**:
    - **技术栈**: Next.js API Route + `Cheerio`
    - **功能**: 直接处理网页抓取任务，提取关键信息并存入数据库。处理过程同步完成。

### 3.6 数据持久层
- **PostgreSQL**: 存储核心业务数据，包括用户信息、评估结果、**外部数据源信息**、**解析后的外部数据**等。
- **Redis**: 用于基础缓存，包括会话存储和配置缓存。
- **腾讯云COS**: 存储用户上传的原始文件和生成的PDF报告。

## 4. 数据流

### 4.1 标准版评估流程
1.  用户选择“标准版评估”。
2.  前端请求API，触发**问卷设计师**。
3.  问卷设计师读取`question_design_prompt.json`，生成问卷数据。
4.  前端**问卷渲染引擎**渲染问卷。
5.  用户提交答案。
6.  前端请求分析API，触发**组织评估导师**。
7.  组织评估导师读取`organization_tutor_prompt.json`（标准版配置），分析问卷答案，生成报告。
8.  前端**报告展示模块**渲染报告。

### 4.2 专业版评估流程
1.  用户选择"专业版评估"。
2.  **（新增）** 系统引导用户上传补充文档（可选），文件通过**文件上传服务**同步处理并存入数据库。
3.  **（新增）** 系统引导用户提供公开信息URL（可选），**网络数据采集服务**同步处理并存入数据库。
4.  后续流程同标准版，但**组织评估导师**在分析时会：
    a. 读取`organization_tutor_prompt.json`（专业版配置）。
    b. 调用**数据融合引擎**，从数据库中获取已处理的外部数据。
    c. 结合问卷答案和外部数据进行多模型、深度的综合分析，最终生成专业报告。
