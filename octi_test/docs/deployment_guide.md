# OCTI智能评估系统部署指南

本文档详细介绍了OCTI智能评估系统的部署流程、配置说明和运维指南。

## 目录

- [系统要求](#系统要求)
- [环境准备](#环境准备)
- [配置文件](#配置文件)
- [部署流程](#部署流程)
- [监控配置](#监控配置)
- [备份恢复](#备份恢复)
- [故障排除](#故障排除)
- [性能优化](#性能优化)
- [安全配置](#安全配置)

## 1. 部署架构概述

OCTI智能评估系统采用现代化的云原生架构，支持灵活的部署选项。系统架构如下：

```
┌─────────────────────────────────────────────────────────┐
│                    用户层                                │
│                      │                                  │
│                      ▼                                  │
│            ┌───────────────────┐                        │
│            │   简单Web服务器    │                        │
│            │   (Next.js)       │                        │
│            └─────────┬─────────┘                        │
└────────────────────┬─┴────────────────────────────────┬─┘
                     │                                  │
┌────────────────────▼─────────────────┐ ┌──────────────▼─────────────────┐
│           数据存储层                  │ │           文件存储层             │
│  ┌─────────────────────────────────┐ │ │  ┌─────────────────────────┐   │
│  │   PostgreSQL数据库              │ │ │  │   对象存储 (腾讯云COS)    │   │
│  │   Redis缓存                     │ │ │  └─────────────────────────┘   │
│  └─────────────────────────────────┘ │ └────────────────────────────────┘
└─────────────────────────────────────┘                                   
```

## 2. 环境要求

### 2.1 开发环境

- Node.js 18+
- npm 8+
- PostgreSQL 14+
- Redis 6+
- Git

### 2.2 生产环境

- 腾讯云服务器 (推荐2核4G即可)
- 腾讯云数据库 PostgreSQL (基础版)
- 腾讯云Redis (基础版)
- 腾讯云对象存储 COS

## 3. 部署流程

### 3.1 开发环境部署

#### 3.1.1 克隆代码库

```bash
git clone https://github.com/your-org/octi-assessment-system.git
cd octi-assessment-system
```

#### 3.1.2 安装依赖

```bash
npm install
```

#### 3.1.3 环境变量配置

创建`.env.local`文件：

```
# 数据库
DATABASE_URL=postgresql://username:password@localhost:5432/octi_db

# Redis
REDIS_URL=redis://localhost:6379

# LLM API
MINIMAX_API_KEY=your_minimax_api_key
MINIMAX_API_URL=https://api.minimax.chat
DEEPSEEK_API_KEY=your_deepseek_api_key
DEEPSEEK_API_URL=https://api.deepseek.com

# 认证
NEXTAUTH_SECRET=your_nextauth_secret
NEXTAUTH_URL=http://localhost:3000

# 腾讯云
TENCENT_SECRET_ID=your_tencent_secret_id
TENCENT_SECRET_KEY=your_tencent_secret_key
TENCENT_COS_BUCKET=your_cos_bucket
TENCENT_COS_REGION=ap-guangzhou
```

#### 3.1.4 数据库迁移

```bash
npx prisma migrate dev
```

#### 3.1.5 启动开发服务器

```bash
npm run dev
```

### 3.2 生产环境部署

#### 3.2.1 构建应用

```bash
npm run build
```

#### 3.2.2 使用Docker部署

创建`Dockerfile`：

```dockerfile
FROM node:18-alpine AS base

# 安装依赖
FROM base AS deps
WORKDIR /app
COPY package.json package-lock.json ./
RUN npm ci

# 构建应用
FROM base AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .
RUN npm run build

# 生产环境
FROM base AS runner
WORKDIR /app
ENV NODE_ENV production

# 复制必要文件
COPY --from=builder /app/public ./public
COPY --from=builder /app/.next ./.next
COPY --from=builder /app/node_modules ./node_modules
COPY --from=builder /app/package.json ./package.json
COPY --from=builder /app/next.config.js ./next.config.js
COPY --from=builder /app/prisma ./prisma

# 设置用户
RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs
RUN chown -R nextjs:nodejs /app
USER nextjs

EXPOSE 3000
CMD ["npm", "start"]
```

创建`docker-compose.yml`：

```yaml
version: '3'

services:
  app:
    build:
      context: .
      dockerfile: Dockerfile
    restart: always
    ports:
      - "3000:3000"
    env_file:
      - .env.production
    depends_on:
      - postgres
      - redis
    networks:
      - octi-network

  postgres:
    image: postgres:14
    restart: always
    volumes:
      - postgres-data:/var/lib/postgresql/data
    environment:
      POSTGRES_USER: ${POSTGRES_USER}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      POSTGRES_DB: ${POSTGRES_DB}
    networks:
      - octi-network

  redis:
    image: redis:6
    restart: always
    volumes:
      - redis-data:/data
    networks:
      - octi-network

networks:
  octi-network:
    driver: bridge

volumes:
  postgres-data:
  redis-data:
```

#### 3.2.3 腾讯云部署

1. **创建腾讯云资源**

   - 创建腾讯云服务器实例 (2核4G)
   - 创建PostgreSQL数据库实例 (基础版)
   - 创建Redis实例 (基础版)
   - 创建对象存储桶

2. **配置域名和SSL证书**

   - 在腾讯云购买域名或导入已有域名
   - 申请SSL证书
   - 配置DNS解析

3. **部署应用**

   ```bash
   # 登录腾讯云服务器
   ssh root@your-server-ip
   
   # 安装Docker和Docker Compose
   curl -fsSL https://get.docker.com | sh
   apt-get install -y docker-compose
   
   # 克隆代码库
   git clone https://github.com/your-org/octi-assessment-system.git
   cd octi-assessment-system
   
   # 创建环境变量文件
   nano .env.production
   
   # 启动应用
   docker-compose up -d
   ```

4. **配置Nginx反向代理**

   ```nginx
   server {
       listen 80;
       server_name your-domain.com;
       return 301 https://$host$request_uri;
   }
   
   server {
       listen 443 ssl;
       server_name your-domain.com;
       
       ssl_certificate /etc/nginx/ssl/your-domain.com.crt;
       ssl_certificate_key /etc/nginx/ssl/your-domain.com.key;
       
       location / {
           proxy_pass http://localhost:3000;
           proxy_set_header Host $host;
           proxy_set_header X-Real-IP $remote_addr;
           proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
           proxy_set_header X-Forwarded-Proto $scheme;
       }
   }
   ```

## 4. 配置管理

### 4.1 配置文件管理

OCTI系统的核心配置文件包括：

- `question_design_prompt.json` - 问卷设计师配置
- `organization_tutor_prompt.json` - 组织评估导师配置

这些配置文件应该存储在安全的位置，并通过CI/CD流程进行管理。

### 4.2 环境变量管理

生产环境的敏感配置应通过环境变量管理，避免硬编码在代码中。

### 4.3 配置热更新

系统支持配置热更新，无需重启服务即可应用新配置：

```typescript
// 示例：热更新配置
import { ConfigEngine } from '@/services/config/config-engine';

const configEngine = new ConfigEngine();

// 更新配置
async function updateConfig(configType, newConfig) {
  await configEngine.updateConfig(configType, newConfig);
  console.log(`配置 ${configType} 已更新`);
}
```

## 5. 监控和日志

### 5.1 应用监控

使用腾讯云监控或Prometheus + Grafana监控应用性能：

```yaml
# docker-compose.yml中添加监控服务
prometheus:
  image: prom/prometheus
  volumes:
    - ./prometheus.yml:/etc/prometheus/prometheus.yml
  ports:
    - "9090:9090"
  networks:
    - octi-network

grafana:
  image: grafana/grafana
  ports:
    - "3001:3000"
  networks:
    - octi-network
```

### 5.2 日志管理

使用ELK栈或腾讯云日志服务管理日志：

```yaml
# docker-compose.yml中添加日志服务
elasticsearch:
  image: docker.elastic.co/elasticsearch/elasticsearch:7.14.0
  environment:
    - discovery.type=single-node
  volumes:
    - elasticsearch-data:/usr/share/elasticsearch/data
  networks:
    - octi-network

kibana:
  image: docker.elastic.co/kibana/kibana:7.14.0
  ports:
    - "5601:5601"
  networks:
    - octi-network

logstash:
  image: docker.elastic.co/logstash/logstash:7.14.0
  volumes:
    - ./logstash.conf:/usr/share/logstash/pipeline/logstash.conf
  networks:
    - octi-network

volumes:
  elasticsearch-data:
```

## 6. 备份和恢复

### 6.1 数据库备份

设置定时任务进行数据库备份：

```bash
#!/bin/bash
# /etc/cron.daily/backup-octi-db

DATE=$(date +%Y%m%d)
BACKUP_DIR=/var/backups/octi

# 创建备份目录
mkdir -p $BACKUP_DIR

# 备份PostgreSQL数据库
pg_dump -h $DB_HOST -U $DB_USER -d $DB_NAME -F c -f $BACKUP_DIR/octi_db_$DATE.dump

# 上传到腾讯云COS
/usr/local/bin/coscmd upload $BACKUP_DIR/octi_db_$DATE.dump backups/

# 删除7天前的本地备份
find $BACKUP_DIR -name "octi_db_*.dump" -mtime +7 -delete
```

### 6.2 配置备份

定期备份配置文件：

```bash
#!/bin/bash
# /etc/cron.daily/backup-octi-config

DATE=$(date +%Y%m%d)
BACKUP_DIR=/var/backups/octi/config
CONFIG_DIR=/app/config

# 创建备份目录
mkdir -p $BACKUP_DIR

# 备份配置文件
tar -czf $BACKUP_DIR/octi_config_$DATE.tar.gz $CONFIG_DIR

# 上传到腾讯云COS
/usr/local/bin/coscmd upload $BACKUP_DIR/octi_config_$DATE.tar.gz backups/config/

# 删除7天前的本地备份
find $BACKUP_DIR -name "octi_config_*.tar.gz" -mtime +7 -delete
```

### 6.3 恢复流程

数据库恢复流程：

```bash
# 从备份恢复PostgreSQL数据库
pg_restore -h $DB_HOST -U $DB_USER -d $DB_NAME -c $BACKUP_FILE
```

配置恢复流程：

```bash
# 从备份恢复配置文件
tar -xzf $BACKUP_FILE -C /tmp
cp -r /tmp/app/config/* /app/config/
```

## 7. 扩展和优化

### 7.1 基础优化

- 配置基础Redis缓存
- 优化数据库查询
- 启用服务端渲染和静态生成

### 7.2 扩展考虑

当用户量增长时，可以考虑：
- 增加服务器配置
- 添加CDN加速
- 配置基础部署

### 7.3 安全加固

- 启用WAF (Web应用防火墙)
- 配置网络ACL
- 实施HTTPS和HSTS
- 定期更新依赖和系统补丁

## 8. 故障排除

### 8.1 常见问题

| 问题 | 可能原因 | 解决方案 |
|------|---------|---------|
| 应用无法启动 | 环境变量配置错误 | 检查.env文件配置 |
| 数据库连接失败 | 数据库凭证错误或网络问题 | 验证数据库连接信息和网络连通性 |
| LLM API调用失败 | API密钥无效或超出限额 | 检查API密钥和使用配额 |
| 页面加载缓慢 | 资源未优化或CDN配置问题 | 优化前端资源和检查CDN配置 |

### 8.2 日志查看

```bash
# 查看容器日志
docker-compose logs -f app

# 查看应用日志
docker exec -it octi-assessment-system_app_1 cat /app/logs/app.log

# 查看Nginx日志
tail -f /var/log/nginx/access.log
tail -f /var/log/nginx/error.log
```

### 8.3 健康检查

创建健康检查端点：

```typescript
// src/pages/api/health.ts
import type { NextApiRequest, NextApiResponse } from 'next';
import { prisma } from '@/prisma/client';
import { createClient } from 'redis';

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  try {
    // 检查数据库连接
    await prisma.$queryRaw`SELECT 1`;
    
    // 检查Redis连接
    const redis = createClient({ url: process.env.REDIS_URL });
    await redis.connect();
    await redis.ping();
    await redis.disconnect();
    
    // 返回健康状态
    res.status(200).json({
      status: 'healthy',
      timestamp: new Date().toISOString(),
      services: {
        database: 'up',
        redis: 'up',
        app: 'up'
      }
    });
  } catch (error) {
    console.error('健康检查失败:', error);
    res.status(500).json({
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      error: error.message
    });
  }
}