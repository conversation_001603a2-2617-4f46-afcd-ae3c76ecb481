'use client'

import { useState, useCallback, useMemo } from 'react'
import { QuestionnaireConfig, QuestionnaireResponse } from '@/types'

interface UseQuestionnaireOptions {
  config: QuestionnaireConfig
  initialResponses: QuestionnaireResponse[]
  onComplete: (responses: QuestionnaireResponse[]) => Promise<void>
  onSave?: (responses: QuestionnaireResponse[]) => Promise<void>
}

/**
 * 问卷逻辑管理Hook
 * 将复杂的状态管理逻辑从组件中抽离
 */
export function useQuestionnaire({
  config,
  initialResponses,
  onComplete,
  onSave
}: UseQuestionnaireOptions) {
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0)
  const [responses, setResponses] = useState<QuestionnaireResponse[]>(initialResponses)
  const [errors, setErrors] = useState<Record<string, string>>({})
  const [isLoading, setIsLoading] = useState(false)

  // 计算派生状态
  const currentQuestion = useMemo(() => 
    config.questions[currentQuestionIndex], 
    [config.questions, currentQuestionIndex]
  )
  
  const isFirst = currentQuestionIndex === 0
  const isLast = currentQuestionIndex === config.questions.length - 1
  const progress = ((currentQuestionIndex + 1) / config.questions.length) * 100

  // 更新回答
  const updateResponse = useCallback((questionId: string, answer: any) => {
    setResponses(prev => {
      const existing = prev.find(r => r.questionId === questionId)
      if (existing) {
        return prev.map(r => 
          r.questionId === questionId ? { ...r, answer } : r
        )
      }
      return [...prev, { questionId, answer }]
    })
    
    // 清除错误
    setErrors(prev => ({ ...prev, [questionId]: '' }))
  }, [])

  // 验证当前问题
  const validateCurrentQuestion = useCallback(() => {
    if (!currentQuestion) return true

    const response = responses.find(r => r.questionId === currentQuestion.id)
    if (!response || response.answer === undefined || response.answer === null || response.answer === '') {
      setErrors(prev => ({
        ...prev,
        [currentQuestion.id]: '请回答此问题后再继续'
      }))
      return false
    }
    return true
  }, [currentQuestion, responses])

  // 下一题
  const handleNext = useCallback(async () => {
    if (!validateCurrentQuestion()) return

    if (isLast) {
      setIsLoading(true)
      try {
        await onComplete(responses)
      } catch (error) {
        console.error('提交问卷失败:', error)
      } finally {
        setIsLoading(false)
      }
    } else {
      setCurrentQuestionIndex(prev => prev + 1)
    }
  }, [validateCurrentQuestion, isLast, responses, onComplete])

  // 上一题
  const handlePrevious = useCallback(() => {
    if (!isFirst) {
      setCurrentQuestionIndex(prev => prev - 1)
    }
  }, [isFirst])

  return {
    currentQuestion,
    currentQuestionIndex,
    responses,
    errors,
    isLoading,
    isFirst,
    isLast,
    progress,
    handleNext,
    handlePrevious,
    updateResponse
  }
}