import { Logger } from '@/lib/logger';
import { ConfigService } from '../config/ConfigService';
import { QuestionDesignerAgent } from './QuestionDesignerAgent';
import { OrganizationTutorAgent } from './OrganizationTutorAgent';
// 添加缺少的导入
import { LLMApiClient, type LLMConfig } from '../llm/llm-api-client';
import { PromptBuilder, type PromptBuilderConfig } from '../llm/prompt-builder';
import { DataFusionEngine, type FusionConfig } from '../data/data-fusion-engine';
import type { BaseAgent, AgentStatus, AgentInput, AgentOutput } from '@/types/agents';

/**
 * 智能体服务基类
 * 提供智能体的基础功能和管理
 */
export abstract class AgentService {
  protected logger: Logger
  protected configService: ConfigService
  protected agentName: string
  protected isInitialized: boolean = false
  protected llmClient: LLMApiClient;
  protected promptBuilder: PromptBuilder;
  protected dataFusionEngine: DataFusionEngine;

  constructor(agentName: string) {
    this.agentName = agentName
    this.logger = new Logger(`Agent:${agentName}`)
    // ConfigService.getInstance()返回Promise，需要在initialize中处理
    this.configService = null as any; // 临时设置，在initialize中正确初始化
    // ✅ 使用默认配置初始化
    this.llmClient = new LLMApiClient();
    this.promptBuilder = new PromptBuilder();
    this.dataFusionEngine = new DataFusionEngine();
  }

  /**
   * 使用自定义配置初始化服务
   */
  protected initializeWithConfig(
    llmConfig?: LLMConfig,
    promptConfig?: PromptBuilderConfig,
    fusionConfig?: Partial<FusionConfig>
  ) {
    if (llmConfig) {
      this.llmClient = new LLMApiClient(llmConfig);
    }
    if (promptConfig) {
      this.promptBuilder = new PromptBuilder(promptConfig);
    }
    if (fusionConfig) {
      this.dataFusionEngine = new DataFusionEngine(fusionConfig);
    }
  }

  /**
   * 初始化智能体
   */
  public async initialize(): Promise<void> {
    if (this.isInitialized) {
      this.logger.warn('智能体已经初始化')
      return
    }

    try {
      this.logger.info('开始初始化智能体')
      await this.onInitialize()
      this.isInitialized = true
      this.logger.info('智能体初始化完成')
    } catch (error) {
      this.logger.error('智能体初始化失败', { error })
      throw error
    }
  }

  /**
   * 执行智能体任务
   */
  public async execute(input: AgentInput): Promise<AgentOutput> {
    if (!this.isInitialized) {
      throw new Error('智能体未初始化')
    }

    try {
      this.logger.info('开始执行智能体任务', { input })
      const result = await this.onExecute(input)
      this.logger.info('智能体任务执行完成', { result })
      return result
    } catch (error) {
      this.logger.error('智能体任务执行失败', { error, input })
      throw error
    }
  }

  /**
   * 获取智能体状态
   */
  public getStatus(): AgentStatus {
    return {
      name: this.agentName,
      initialized: this.isInitialized,
      lastActivity: new Date(),
      config: this.getAgentConfig()
    }
  }

  /**
   * 获取智能体配置
   */
  protected getAgentConfig(): Record<string, any> {
    const configKey = `octi.agents.${this.agentName.toLowerCase().replace(/\s+/g, '_')}`
    return this.configService.get(configKey, {})
  }

  /**
   * 子类需要实现的初始化方法
   */
  protected abstract onInitialize(): Promise<void>

  /**
   * 子类需要实现的执行方法
   */
  protected abstract onExecute(input: AgentInput): Promise<AgentOutput>
}

/**
 * 智能体管理器
 * 负责管理所有智能体的生命周期
 */
export class AgentManager {
  private agents = new Map<string, BaseAgent>();
  private logger: Logger;

  constructor() {
    this.logger = new Logger('AgentManager');
  }

  public async initialize(): Promise<void> {
    try {
      this.logger.info('开始初始化智能体管理器');

      // 创建依赖项实例
      const llmClient = new LLMApiClient();
      const promptBuilder = new PromptBuilder();
      const dataFusionEngine = new DataFusionEngine();

      // 注册智能体
      this.registerAgent(new QuestionDesignerAgent(
        llmClient,
        promptBuilder,
        dataFusionEngine
      ));
      
      this.registerAgent(new OrganizationTutorAgent(
        llmClient,
        promptBuilder,
        dataFusionEngine
      ));

      // 初始化所有智能体
      for (const [name, agent] of Array.from(this.agents.entries())) {
        await agent.initialize();
        this.logger.info(`智能体 ${name} 初始化完成`);
      }

      this.logger.info('智能体管理器初始化完成');
    } catch (error) {
      this.logger.error('智能体管理器初始化失败', { error });
      throw error;
    }
  }

  /**
   * 注册智能体
   */
  public registerAgent(agent: BaseAgent): void {
    this.agents.set(agent.name, agent);
    this.logger.info(`注册智能体: ${agent.name}`);
  }

  /**
   * 获取智能体 - 增强类型安全
   */
  public getAgent<T extends BaseAgent>(name: string): T | undefined {
    return this.agents.get(name) as T | undefined;
  }

  /**
   * 执行智能体任务 - 保持向后兼容
   */
  public async executeAgent(name: string, input: any): Promise<any> {
    const agent = this.getAgent(name);
    if (!agent) {
      throw new Error(`智能体不存在: ${name}`);
    }

    // 根据智能体类型调用相应方法
    if (name === 'question_designer') {
      const questionAgent = agent as QuestionDesignerAgent;
      return await questionAgent.designQuestionnaire(input);
    } else if (name === 'organization_tutor') {
      const tutorAgent = agent as OrganizationTutorAgent;
      return await tutorAgent.generateAssessment(input.assessmentData, input.options);
    }

    throw new Error(`不支持的智能体操作: ${name}`);
  }

  /**
   * 获取所有智能体状态
   */
  public getAllStatus(): Record<string, AgentStatus> {
    const status: Record<string, AgentStatus> = {}
    for (const [name, agent] of Array.from(this.agents.entries())) {
      status[name] = agent.getStatus()
    }
    return status
  }

  /**
   * 获取可用的智能体列表
   */
  public getAvailableAgents(): string[] {
    return Array.from(this.agents.keys())
  }
}

// 移除重复的类型定义，使用从@/types/agents导入的类型

export interface QuestionnaireDesignInput extends AgentInput {
  assessmentType: string
  dimensions: string[]
  requirements?: string
}

export interface AssessmentAnalysisInput extends AgentInput {
  assessmentResults: any
  userProfile: any
}
