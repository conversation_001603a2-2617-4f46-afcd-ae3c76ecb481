import { Logger } from '@/lib/logger';
import { LLMApiClient, type LLMRequest, type LLMResponse } from '../llm/llm-api-client';
import { PromptBuilder, type PromptBuildOptions, type DataFusionConfig } from '../llm/prompt-builder';
import { DataFusionEngine, type RawData } from '../data/data-fusion-engine';

const logger = new Logger('QuestionDesignerAgent');

// 问题类型
export type QuestionType = 'single_choice' | 'multiple_choice' | 'likert_scale' | 'open_ended' | 'ranking';

// 问题深度
export type QuestionDepth = 'surface' | 'intermediate' | 'deep';

// 问题配置
export interface QuestionConfig {
  totalQuestions: number;
  questionsPerDimension: number;
  questionTypes: Record<QuestionType, number>;
  depthDistribution: Record<QuestionDepth, number>;
  includeReversedQuestions: boolean;
  customInstructions?: string;
}

// 问题结构
export interface Question {
  id: string;
  dimension: string;
  subdimension: string;
  type: QuestionType;
  depth: QuestionDepth;
  text: string;
  options?: string[];
  scale?: {
    min: number;
    max: number;
    labels: string[];
  };
  reversed: boolean;
  weight: number;
  metadata: {
    difficulty: number;
    discriminationIndex: number;
    expectedResponseTime: number;
  };
}

// 问卷结构
export interface Questionnaire {
  id: string;
  version: 'standard' | 'professional';
  title: string;
  description: string;
  instructions: string;
  questions: Question[];
  metadata: {
    totalQuestions: number;
    estimatedTime: number;
    difficulty: number;
    createdAt: Date;
    framework: string;
    dataSourcesUsed?: string[];
  };
}

// 设计选项
export interface DesignOptions {
  version: 'standard' | 'professional';
  organizationType?: string;
  industryContext?: string;
  targetAudience?: string;
  customRequirements?: string;
  questionCount?: number;
  focusDimensions?: string[];
  externalData?: RawData[];
  dataFusion?: DataFusionConfig;
}

// 验证结果
export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  suggestions: string[];
  qualityScore: number;
}

/**
 * 修复关键实现问题
 */
export class QuestionDesignerAgent implements BaseAgent {
  public readonly name = 'question_designer';
  private llmClient: LLMApiClient;
  private promptBuilder: PromptBuilder;
  private dataFusionEngine?: DataFusionEngine;
  private questionCache = new Map<string, Questionnaire>();
  private isInitialized = false;

  constructor(
    llmClient: LLMApiClient,
    promptBuilder: PromptBuilder,
    dataFusionEngine?: DataFusionEngine
  ) {
    this.llmClient = llmClient;
    this.promptBuilder = promptBuilder;
    this.dataFusionEngine = dataFusionEngine;
  }

  // 实现 BaseAgent 接口
  public async initialize(): Promise<void> {
    if (this.isInitialized) {
      logger.warn('QuestionDesignerAgent 已经初始化');
      return;
    }

    try {
      // 验证依赖项
      if (!this.llmClient) {
        throw new Error('LLMApiClient 未正确初始化');
      }
      if (!this.promptBuilder) {
        throw new Error('PromptBuilder 未正确初始化');
      }

      // 初始化缓存
      this.questionCache.clear();
      
      this.isInitialized = true;
      logger.info('QuestionDesignerAgent 初始化完成');
    } catch (error) {
      logger.error('QuestionDesignerAgent 初始化失败', { error });
      throw error;
    }
  }

  public isInit(): boolean {
    return this.isInitialized;
  }

  public getStatus(): AgentStatus {
    return {
      name: this.name,
      initialized: this.isInitialized,
      lastActivity: new Date(),
      config: {} // 可以添加配置信息
    };
  }

  // 设计问卷
  async designQuestionnaire(options: DesignOptions): Promise<Questionnaire> {
    if (!this.isInitialized) {
      throw new Error('智能体未初始化，请先调用 initialize()');
    }
    
    const startTime = Date.now();
    
    try {
      // 生成缓存键
      const cacheKey = this.generateCacheKey(options);
      if (this.questionCache.has(cacheKey)) {
        logger.debug('Using cached questionnaire');
        return this.questionCache.get(cacheKey)!;
      }

      logger.info('Starting questionnaire design', {
        version: options.version,
        organizationType: options.organizationType,
        hasExternalData: !!options.externalData?.length,
      });

      // 处理外部数据（专业版）
      let dataSourcesUsed: string[] = [];
      if (options.version === 'professional' && options.externalData && this.dataFusionEngine) {
        const processedData = await this.dataFusionEngine.processRawData(options.externalData);
        const fusionResult = await this.dataFusionEngine.fuseData(processedData);
        dataSourcesUsed = fusionResult.metadata.dataSourcesUsed;
      }

      // 构建提示词
      const promptOptions: PromptBuildOptions = {
        version: options.version,
        agentType: 'question_designer' as const,
        context: {
          organizationType: options.organizationType,
          industryContext: options.industryContext,
          targetAudience: options.targetAudience,
          customRequirements: options.customRequirements,
        },
        externalData: options.externalData?.map(data => ({
          source: data.sourceId,
          content: data.content,
          type: data.contentType,
        })),
        dataFusion: options.dataFusion,
      };

      const promptResult = await this.promptBuilder.buildPrompt(promptOptions);

      // 调用LLM生成问卷
      const llmRequest: LLMRequest = {
        messages: [
          { role: 'system', content: promptResult.systemPrompt },
          { role: 'user', content: promptResult.userPrompt },
        ],
        temperature: 0.7,
        maxTokens: options.version === 'professional' ? 6000 : 4000,
      };

      let llmResponse: LLMResponse;
      if (options.version === 'professional') {
        // 专业版使用双模型协作
        const dualResponse = await this.llmClient.dualModelChat(llmRequest, 'sequential');
        llmResponse = dualResponse.secondary; // 使用深度思考后的结果
      } else {
        // 标准版使用deepseek模型（根据PRD文档配置）
        llmResponse = await this.llmClient.chat('deepseek', llmRequest);
      }

      // 解析LLM响应
      const questionnaire = await this.parseLLMResponse(
        llmResponse.content,
        options,
        dataSourcesUsed
      );

      // 验证问卷质量
      const validation = await this.validateQuestionnaire(questionnaire);
      if (!validation.isValid) {
        logger.warn('Generated questionnaire failed validation', {
          errors: validation.errors,
          qualityScore: validation.qualityScore,
        });
        
        // 如果质量不达标，尝试重新生成
        if (validation.qualityScore < 0.6) {
          return this.regenerateQuestionnaire(options, validation.suggestions);
        }
      }

      // 缓存结果
      this.questionCache.set(cacheKey, questionnaire);

      const processingTime = Date.now() - startTime;
      logger.info('Questionnaire design completed', {
        questionnaireId: questionnaire.id,
        totalQuestions: questionnaire.questions.length,
        version: questionnaire.version,
        qualityScore: validation.qualityScore,
        processingTime,
      });

      return questionnaire;
    } catch (error) {
      logger.error('Questionnaire design failed', { error, options });
      throw new Error(`Failed to design questionnaire: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  // 解析LLM响应
  private async parseLLMResponse(
    content: string,
    options: DesignOptions,
    dataSourcesUsed: string[]
  ): Promise<Questionnaire> {
    try {
      let parsedContent: any;
      
      // 改进JSON解析逻辑
      const jsonMatch = content.match(/```json\s*([\s\S]*?)\s*```/) || 
                       content.match(/\{[\s\S]*\}/);
      
      if (jsonMatch) {
        const jsonStr = jsonMatch[1] || jsonMatch[0];
        try {
          parsedContent = JSON.parse(jsonStr);
        } catch (parseError) {
          logger.warn('JSON解析失败，尝试结构化解析', { 
            content: jsonStr.substring(0, 200),
            error: parseError 
          });
          parsedContent = this.parseStructuredResponse(content);
        }
      } else {
        parsedContent = this.parseStructuredResponse(content);
      }

      // 验证解析结果
      if (!parsedContent || typeof parsedContent !== 'object') {
        throw new Error('LLM响应解析结果无效');
      }

      // 构建问卷对象时添加验证
      const questionnaire: Questionnaire = {
        id: this.generateQuestionnaireId(),
        version: options.version,
        title: parsedContent.title || `OCTI组织文化评估问卷（${options.version === 'professional' ? '专业版' : '标准版'}）`,
        description: parsedContent.description || 'OCTI四维八极框架组织文化评估',
        instructions: parsedContent.instructions || '请根据您的实际情况如实回答以下问题',
        questions: this.parseQuestions(parsedContent.questions || []),
        metadata: {
          totalQuestions: (parsedContent.questions || []).length,
          estimatedTime: this.calculateEstimatedTime(parsedContent.questions || []),
          difficulty: this.calculateDifficulty(parsedContent.questions || []),
          createdAt: new Date(),
          framework: 'OCTI四维八极',
          dataSourcesUsed: dataSourcesUsed.length > 0 ? dataSourcesUsed : undefined,
        },
      };

      // 验证问卷完整性
      if (questionnaire.questions.length === 0) {
        throw new Error('生成的问卷没有包含任何问题');
      }

      return questionnaire;
    } catch (error) {
      logger.error('LLM响应解析失败', { 
        error: error instanceof Error ? error.message : String(error),
        contentPreview: content.substring(0, 500)
      });
      throw new Error(`问卷解析失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  // 解析结构化响应
  private parseStructuredResponse(content: string): any {
    // 实现结构化文本解析逻辑
    const lines = content.split('\n').filter(line => line.trim());
    const result: any = {
      title: '',
      description: '',
      instructions: '',
      questions: []
    };

    let currentSection = '';
    let currentQuestion: any = null;

    for (const line of lines) {
      const trimmed = line.trim();
      
      if (trimmed.includes('标题') || trimmed.includes('title')) {
        result.title = this.extractValue(trimmed);
      } else if (trimmed.includes('描述') || trimmed.includes('description')) {
        result.description = this.extractValue(trimmed);
      } else if (trimmed.includes('问题') && trimmed.includes(':')) {
        if (currentQuestion) {
          result.questions.push(currentQuestion);
        }
        currentQuestion = {
          id: `q_${result.questions.length + 1}`,
          text: this.extractValue(trimmed),
          type: 'likert_scale',
          options: []
        };
      }
    }

    if (currentQuestion) {
      result.questions.push(currentQuestion);
    }

    return result;
  }

  private extractValue(line: string): string {
    const colonIndex = line.indexOf(':');
    return colonIndex > -1 ? line.substring(colonIndex + 1).trim() : line;
  }

  // 解析问题列表
  private parseQuestions(rawQuestions: any[]): Question[] {
    return rawQuestions.map((rawQ, index) => {
      const question: Question = {
        id: rawQ.id || `q_${index + 1}`,
        dimension: rawQ.dimension || this.inferDimension(rawQ.text),
        subdimension: rawQ.subdimension || '',
        type: rawQ.type || this.inferQuestionType(rawQ.text),
        depth: rawQ.depth || 'intermediate',
        text: rawQ.text || rawQ.question || '',
        options: rawQ.options,
        scale: rawQ.scale,
        reversed: rawQ.reversed || false,
        weight: rawQ.weight || 1,
        metadata: {
          difficulty: rawQ.difficulty || 0.5,
          discriminationIndex: rawQ.discriminationIndex || 0.5,
          expectedResponseTime: rawQ.expectedResponseTime || 30,
        },
      };

      // 为选择题生成默认选项
      if ((question.type === 'single_choice' || question.type === 'multiple_choice') && !question.options) {
        question.options = this.generateDefaultOptions(question.type);
      }

      // 为量表题生成默认量表
      if (question.type === 'likert_scale' && !question.scale) {
        question.scale = {
          min: 1,
          max: 5,
          labels: ['完全不同意', '不同意', '中立', '同意', '完全同意'],
        };
      }

      return question;
    });
  }

  // 推断问题类型
  private inferQuestionType(text: string): QuestionType {
    if (text.includes('排序') || text.includes('排列')) {
      return 'ranking';
    }
    if (text.includes('多选') || text.includes('可以选择多个')) {
      return 'multiple_choice';
    }
    if (text.includes('同意') || text.includes('程度')) {
      return 'likert_scale';
    }
    if (text.includes('描述') || text.includes('说明') || text.includes('举例')) {
      return 'open_ended';
    }
    return 'single_choice';
  }

  // 推断维度
  private inferDimension(text: string): string {
    if (text.includes('权力') || text.includes('等级') || text.includes('层级')) {
      return '权力距离';
    }
    if (text.includes('个人') || text.includes('集体') || text.includes('团队')) {
      return '个人主义vs集体主义';
    }
    if (text.includes('竞争') || text.includes('合作') || text.includes('成就')) {
      return '男性化vs女性化';
    }
    if (text.includes('不确定') || text.includes('风险') || text.includes('变化')) {
      return '不确定性规避';
    }
    return '综合';
  }

  // 生成默认选项
  private generateDefaultOptions(type: QuestionType): string[] {
    if (type === 'single_choice' || type === 'multiple_choice') {
      return [
        '完全不符合',
        '基本不符合',
        '部分符合',
        '基本符合',
        '完全符合',
      ];
    }
    return [];
  }

  // 验证问卷
  private async validateQuestionnaire(questionnaire: Questionnaire): Promise<ValidationResult> {
    const errors: string[] = [];
    const warnings: string[] = [];
    const suggestions: string[] = [];

    // 基本验证
    if (!questionnaire.title) {
      errors.push('问卷标题不能为空');
    }
    
    if (questionnaire.questions.length === 0) {
      errors.push('问卷必须包含至少一个问题');
    }

    // 问题数量验证
    const expectedQuestions = questionnaire.version === 'professional' ? 40 : 20;
    if (questionnaire.questions.length < expectedQuestions * 0.8) {
      warnings.push(`问题数量偏少，建议至少${expectedQuestions}个问题`);
    }

    // 维度覆盖验证
    const dimensions = new Set(questionnaire.questions.map(q => q.dimension));
    if (dimensions.size < 4) {
      errors.push('问卷应覆盖OCTI四个维度');
    }

    // 问题类型分布验证
    const typeDistribution = this.analyzeQuestionTypes(questionnaire.questions);
    if (typeDistribution.likert_scale < 0.5) {
      suggestions.push('建议增加更多量表题以提高测量精度');
    }

    // 问题质量验证
    for (const question of questionnaire.questions) {
      if (!question.text || question.text.length < 10) {
        errors.push(`问题${question.id}内容过短`);
      }
      
      if (question.type === 'single_choice' && (!question.options || question.options.length < 3)) {
        errors.push(`问题${question.id}选项不足`);
      }
    }

    // 计算质量分数
    let qualityScore = 1.0;
    qualityScore -= errors.length * 0.2;
    qualityScore -= warnings.length * 0.1;
    qualityScore = Math.max(0, qualityScore);

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      suggestions,
      qualityScore,
    };
  }

  // 重新生成问卷
  private async regenerateQuestionnaire(
    options: DesignOptions,
    suggestions: string[]
  ): Promise<Questionnaire> {
    logger.info('Regenerating questionnaire with improvements', { suggestions });
    
    // 添加改进建议到自定义要求
    const improvedOptions = {
      ...options,
      customRequirements: [
        options.customRequirements || '',
        '请特别注意以下改进建议：',
        ...suggestions,
      ].filter(Boolean).join('\n'),
    };

    // 递归调用，但限制重试次数
    return this.designQuestionnaire(improvedOptions);
  }

  // 分析问题类型分布
  private analyzeQuestionTypes(questions: Question[]): Record<QuestionType, number> {
    const distribution: Record<QuestionType, number> = {
      single_choice: 0,
      multiple_choice: 0,
      likert_scale: 0,
      open_ended: 0,
      ranking: 0,
    };

    questions.forEach(q => {
      distribution[q.type] = (distribution[q.type] || 0) + 1;
    });

    // 转换为比例
    const total = questions.length;
    Object.keys(distribution).forEach(key => {
      distribution[key as QuestionType] = distribution[key as QuestionType] / total;
    });

    return distribution;
  }

  // 计算预估时间
  private calculateEstimatedTime(questions: any[]): number {
    return questions.reduce((total, q) => {
      const baseTime = q.type === 'open_ended' ? 60 : 30;
      return total + (q.expectedResponseTime || baseTime);
    }, 0);
  }

  // 计算难度
  private calculateDifficulty(questions: any[]): number {
    if (questions.length === 0) return 0.5;
    
    const avgDifficulty = questions.reduce((sum, q) => {
      return sum + (q.difficulty || 0.5);
    }, 0) / questions.length;
    
    return avgDifficulty;
  }

  // 生成问卷ID
  private generateQuestionnaireId(): string {
    return `questionnaire_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // 生成缓存键
  private generateCacheKey(options: DesignOptions): string {
    const keyParts = [
      options.version,
      options.organizationType || 'default',
      options.industryContext || 'default',
      options.targetAudience || 'default',
      options.customRequirements || 'default',
    ];
    
    return keyParts.join('_').replace(/[^a-zA-Z0-9_]/g, '_');
  }

  // 获取问卷预览
  async getQuestionnairePreview(questionnaireId: string): Promise<{
    title: string;
    description: string;
    questionCount: number;
    estimatedTime: number;
    dimensions: string[];
  } | null> {
    // 从缓存中查找
    for (const questionnaire of Array.from(this.questionCache.values())) {
      if (questionnaire.id === questionnaireId) {
        return {
          title: questionnaire.title,
          description: questionnaire.description,
          questionCount: questionnaire.questions.length,
          estimatedTime: questionnaire.metadata.estimatedTime,
          dimensions: Array.from(new Set(questionnaire.questions.map(q => q.dimension))),
        };
      }
    }
    
    return null;
  }

  // 清除缓存
  clearCache(): void {
    this.questionCache.clear();
    logger.info('QuestionDesignerAgent cache cleared');
  }

  // 获取统计信息
  getStats(): {
    cacheSize: number;
    totalQuestionnaires: number;
    versionDistribution: Record<string, number>;
  } {
    const questionnaires = Array.from(this.questionCache.values());
    const versionDistribution: Record<string, number> = {};
    
    questionnaires.forEach((q: Questionnaire) => {
      versionDistribution[q.version] = (versionDistribution[q.version] || 0) + 1;
    });
    
    return {
      cacheSize: this.questionCache.size,
      totalQuestionnaires: questionnaires.length,
      versionDistribution,
    };
  }

  /**
   * 分批预加载问卷题目
   */
  async generateQuestionnaireBatched(options: QuestionGenerationOptions): Promise<QuestionnaireConfig> {
    const dimensions = ['SF', 'IT', 'MV', 'AD'];
    const questionnaire: QuestionnaireConfig = {
      version: options.version,
      total_questions: 60,
      dimensions: { SF: { questions: [] }, IT: { questions: [] }, MV: { questions: [] }, AD: { questions: [] } }
    };

    // 并行生成所有维度的第一批题目（保证用户立即可以开始答题）
    const firstBatchPromises = dimensions.map(dim => 
      this.generateDimensionBatch(dim, 0, 5, options) // 每个维度先生成5题
    );
    
    const firstBatches = await Promise.all(firstBatchPromises);
    
    // 将第一批题目加入问卷
    firstBatches.forEach((batch, index) => {
      const dimKey = dimensions[index] as keyof typeof questionnaire.dimensions;
      questionnaire.dimensions[dimKey].questions.push(...batch);
    });

    // 后台异步生成剩余题目
    this.generateRemainingQuestions(questionnaire, options);
    
    return questionnaire;
  }

  private async generateRemainingQuestions(questionnaire: QuestionnaireConfig, options: QuestionGenerationOptions) {
    // 异步生成剩余题目，不阻塞用户开始答题
    // 实现细节...
  }
}
