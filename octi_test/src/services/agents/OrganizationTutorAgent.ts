import { Logger } from '@/lib/logger';
import { LLMApiClient, type LLMRequest, type LLMResponse } from '../llm/llm-api-client';
import { PromptBuilder, type PromptBuildOptions, type DataFusionConfig } from '../llm/prompt-builder';
import { DataFusionEngine, type RawData } from '../data/data-fusion-engine';

const logger = new Logger('OrganizationTutorAgent');

// 评估数据接口
export interface AssessmentData {
  organizationId: string;
  responses: Array<{
    questionId: string;
    answer: number | string;
    dimension: string;
    subdimension: string;
  }>;
  metadata: {
    completedAt: string;
    version: 'standard' | 'professional';
    participantCount: number;
    organizationType?: string;
    industryContext?: string;
  };
}

// 外部数据源
export interface ExternalDataSource {
  type: 'financial' | 'hr' | 'operational' | 'market' | 'custom';
  source: string;
  data: any;
  timestamp: string;
  reliability: number; // 0-1
}

// 评估选项
export interface TutorOptions {
  version: 'standard' | 'professional';
  analysisMode: 'basic' | 'comprehensive' | 'comparative';
  includeRecommendations: boolean;
  customFocus?: string[];
  externalData?: ExternalDataSource[];
  dataFusion?: DataFusionConfig;
  outputLanguage?: 'zh' | 'en';
}

// 评估结果
export interface AssessmentResult {
  organizationId: string;
  overallScore: number;
  dimensionScores: Record<string, {
    score: number;
    level: 'low' | 'medium' | 'high' | 'excellent';
    subdimensions: Record<string, number>;
  }>;
  insights: {
    strengths: string[];
    weaknesses: string[];
    opportunities: string[];
    risks: string[];
  };
  recommendations: {
    immediate: string[];
    shortTerm: string[];
    longTerm: string[];
    priority: 'high' | 'medium' | 'low';
  }[];
  report: {
    executive: string;
    detailed: string;
    actionPlan: string;
  };
  metadata: {
    version: string;
    generatedAt: string;
    dataSourcesUsed: string[];
    confidenceLevel: number;
    analysisDepth: string;
  };
}

// 缓存条目
interface CacheEntry {
  data: AssessmentReport;
  timestamp: number;
}

interface AssessmentReport {
  id: string;
  organizationId: string;
  version: 'standard' | 'professional';
  overallScore: number;
  dimensionScores: Record<string, number>;
  strengths: string[];
  improvements: string[];
  recommendations: any[];
  nextSteps: string[];
  metadata: {
    generatedAt: Date;
    analysisMode: string;
    dataSourcesUsed: string[];
  };
}

export class OrganizationTutorAgent {
  private llmClient: LLMApiClient;
  private promptBuilder: PromptBuilder;
  private dataFusionEngine: DataFusionEngine;
  private resultCache = new Map<string, CacheEntry>();
  private readonly cacheExpiry = 24 * 60 * 60 * 1000; // 24小时
  private isInitialized = false;

  constructor(
    llmClient: LLMApiClient,
    promptBuilder: PromptBuilder,
    dataFusionEngine: DataFusionEngine
  ) {
    this.llmClient = llmClient;
    this.promptBuilder = promptBuilder;
    this.dataFusionEngine = dataFusionEngine;
  }

  // 添加缺失的初始化方法
  public async initialize(): Promise<void> {
    if (this.isInitialized) {
      logger.warn('OrganizationTutorAgent 已经初始化');
      return;
    }

    try {
      // 验证必需的依赖项
      if (!this.llmClient) {
        throw new Error('LLMApiClient 是必需的依赖项');
      }
      if (!this.promptBuilder) {
        throw new Error('PromptBuilder 是必需的依赖项');
      }
      if (!this.dataFusionEngine) {
        throw new Error('DataFusionEngine 是必需的依赖项');
      }

      // 清理过期缓存
      this.cleanExpiredCache();
      
      this.isInitialized = true;
      logger.info('OrganizationTutorAgent 初始化完成');
    } catch (error) {
      logger.error('OrganizationTutorAgent 初始化失败', { error });
      throw error;
    }
  }

  // 生成评估报告
  public async generateAssessment(
    assessmentData: AssessmentData,
    options: TutorOptions = {
      version: 'standard',
      analysisMode: 'basic',
      includeRecommendations: true,
      outputLanguage: 'zh'
    }
  ): Promise<AssessmentReport> {
    if (!this.isInitialized) {
      throw new Error('OrganizationTutorAgent 未初始化');
    }

    // 输入验证
    if (!assessmentData || !assessmentData.organizationId) {
      throw new Error('评估数据无效：缺少组织ID');
    }

    if (!assessmentData.responses || assessmentData.responses.length === 0) {
      throw new Error('评估数据无效：缺少响应数据');
    }

    const startTime = Date.now();
    
    try {
      // 检查缓存
      const cacheKey = this.generateCacheKey(assessmentData, options);
      const cached = this.resultCache.get(cacheKey);
      
      if (cached && (Date.now() - cached.timestamp) < this.cacheExpiry) {
        logger.info('使用缓存的评估结果', { cacheKey });
        return cached.data;
      }

      // 数据预处理
      const processedData = this.preprocessAssessmentData(assessmentData);
      
      // 数据融合（如果有外部数据）
      let fusedData: any = null;
      if (options.externalData && options.externalData.length > 0) {
        fusedData = await this.dataFusionEngine.fuseData(
          options.externalData.map(source => ({
            sourceId: source.source,
            content: JSON.stringify(source.data),
            contentType: source.type,
            metadata: {
              timestamp: source.timestamp,
              reliability: source.reliability
            }
          })),
          options.dataFusion
        );
      }

      // 构建提示词
      const promptOptions: PromptBuildOptions = {
        version: options.version,
        agentType: 'organization_tutor' as const,
        context: {
          assessmentData: processedData,
          analysisMode: options.analysisMode,
          includeRecommendations: options.includeRecommendations,
          customFocus: options.customFocus,
          outputLanguage: options.outputLanguage,
          organizationType: assessmentData.metadata.organizationType,
          industryContext: assessmentData.metadata.industryContext
        },
        externalData: fusedData ? [{
          source: 'external_data_fusion',
          content: JSON.stringify(fusedData),
          type: 'fusion_result',
          weight: 1.0
        }] : undefined,
        dataFusion: options.dataFusion
      };

      const prompt = await this.promptBuilder.buildPrompt(promptOptions);

      // 调用LLM生成评估
      let llmResponse: LLMResponse;
      if (options.version === 'professional') {
        const dualResult = await this.llmClient.dualModelChat({
          messages: [
            { role: 'system', content: prompt.systemPrompt },
            { role: 'user', content: prompt.userPrompt }
          ],
          temperature: 0.3,
          maxTokens: 8000
        }, 'sequential');
        llmResponse = dualResult.secondary;
      } else {
        llmResponse = await this.llmClient.chat('minimax', {
          messages: [
            { role: 'system', content: prompt.systemPrompt },
            { role: 'user', content: prompt.userPrompt }
          ],
          temperature: 0.3,
          maxTokens: 4000
        });
      }

      // 解析响应
      const report = await this.parseAssessmentResponse(
        llmResponse.content,
        assessmentData,
        options
      );

      // 缓存结果
      this.resultCache.set(cacheKey, {
        data: report,
        timestamp: Date.now()
      });

      const processingTime = Date.now() - startTime;
      logger.info('评估报告生成完成', {
        organizationId: assessmentData.organizationId,
        version: options.version,
        processingTime
      });

      return report;
    } catch (error) {
      logger.error('评估报告生成失败', { 
        error: error instanceof Error ? error.message : String(error),
        organizationId: assessmentData.organizationId,
        options 
      });
      throw new Error(`评估报告生成失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  // 添加缺失的辅助方法
  private generateCacheKey(data: AssessmentData, options: TutorOptions): string {
    const keyParts = [
      data.organizationId,
      options.version,
      options.analysisMode,
      JSON.stringify(options.customFocus || []),
      data.metadata.completedAt
    ];
    return keyParts.join('_').replace(/[^a-zA-Z0-9_]/g, '_');
  }

  private preprocessAssessmentData(data: AssessmentData): any {
    // 实现数据预处理逻辑
    return {
      organizationId: data.organizationId,
      responses: data.responses.map(r => ({
        questionId: r.questionId,
        answer: r.answer,
        dimension: r.dimension,
        subdimension: r.subdimension
      })),
      metadata: data.metadata
    };
  }

  private async parseAssessmentResponse(
    content: string,
    assessmentData: AssessmentData,
    options: TutorOptions
  ): Promise<AssessmentReport> {
    // 实现响应解析逻辑
    try {
      const jsonMatch = content.match(/```json\s*([\s\S]*?)\s*```/) || 
                       content.match(/\{[\s\S]*\}/);
      
      let parsedContent: any;
      if (jsonMatch) {
        parsedContent = JSON.parse(jsonMatch[1] || jsonMatch[0]);
      } else {
        // 结构化解析
        parsedContent = this.parseStructuredReport(content);
      }

      return {
        id: `report_${Date.now()}`,
        organizationId: assessmentData.organizationId,
        version: options.version,
        overallScore: parsedContent.overallScore || 0,
        dimensionScores: parsedContent.dimensionScores || {},
        strengths: parsedContent.strengths || [],
        improvements: parsedContent.improvements || [],
        recommendations: parsedContent.recommendations || [],
        nextSteps: parsedContent.nextSteps || [],
        metadata: {
          generatedAt: new Date(),
          analysisMode: options.analysisMode,
          dataSourcesUsed: options.externalData?.map(d => d.source) || []
        }
      };
    } catch (error) {
      logger.error('评估响应解析失败', { error, content: content.substring(0, 500) });
      throw new Error('评估响应解析失败');
    }
  }

  private parseStructuredReport(content: string): any {
    // 实现结构化报告解析
    return {
      overallScore: 75,
      dimensionScores: {},
      strengths: [],
      improvements: [],
      recommendations: [],
      nextSteps: []
    };
  }

  // 添加缓存清理方法
  private cleanExpiredCache(): void {
    const now = Date.now();
    for (const [key, entry] of this.resultCache.entries()) {
      if (now - entry.timestamp > this.cacheExpiry) {
        this.resultCache.delete(key);
      }
    }
  }

  // 获取缓存统计
  getCacheStats(): {
    size: number;
    hitRate: number;
    oldestEntry: string | null;
  } {
    const entries = Array.from(this.resultCache.values());
    const oldestEntry = entries.length > 0 
      ? entries.reduce((oldest, entry) => 
          new Date(entry.timestamp) < new Date(oldest.timestamp) ? entry : oldest
        ).timestamp
      : null;
    
    return {
      size: this.resultCache.size,
      hitRate: 0, // 需要额外跟踪命中率
      oldestEntry
    };
  }

  // 清空缓存
  clearCache(): void {
    this.resultCache.clear();
    logger.info('Assessment cache cleared');
  }
}

export default OrganizationTutorAgent;
