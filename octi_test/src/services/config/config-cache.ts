import { Logger } from '@/lib/logger'
import { Redis } from 'ioredis'

/**
 * 配置缓存管理器
 */
export class ConfigCache {
  private logger = new Logger('ConfigCache')
  private redis: Redis | null = null
  private localCache = new Map<string, { value: any; timestamp: number; ttl: number }>()
  private readonly DEFAULT_TTL = 5 * 60 * 1000 // 5分钟

  constructor() {
    this.initializeRedis()
  }

  /**
   * 初始化Redis连接
   */
  private async initializeRedis(): Promise<void> {
    try {
      if (process.env.REDIS_URL) {
        this.redis = new Redis(process.env.REDIS_URL)
        this.logger.info('Redis缓存已连接')
      } else {
        this.logger.warn('未配置Redis，使用本地内存缓存')
      }
    } catch (error) {
      this.logger.error('Redis连接失败，降级到本地缓存', { error })
      this.redis = null
    }
  }

  /**
   * 设置缓存
   */
  async set(key: string, value: any, ttl: number = this.DEFAULT_TTL): Promise<void> {
    try {
      const serializedValue = JSON.stringify(value)
      
      // Redis缓存
      if (this.redis) {
        await this.redis.setex(`config:${key}`, Math.floor(ttl / 1000), serializedValue)
      }
      
      // 本地缓存作为降级
      this.localCache.set(key, {
        value,
        timestamp: Date.now(),
        ttl
      })

      this.logger.debug(`配置缓存已设置: ${key}`)
    } catch (error) {
      this.logger.error(`配置缓存设置失败: ${key}`, { error })
    }
  }

  /**
   * 获取缓存
   */
  async get(key: string): Promise<any | null> {
    try {
      // 优先从Redis获取
      if (this.redis) {
        const cached = await this.redis.get(`config:${key}`)
        if (cached) {
          return JSON.parse(cached)
        }
      }

      // 降级到本地缓存
      const localCached = this.localCache.get(key)
      if (localCached) {
        const { value, timestamp, ttl } = localCached
        
        // 检查是否过期
        if (Date.now() - timestamp < ttl) {
          return value
        } else {
          // 清除过期缓存
          this.localCache.delete(key)
        }
      }

      return null
    } catch (error) {
      this.logger.error(`配置缓存获取失败: ${key}`, { error })
      return null
    }
  }

  /**
   * 删除缓存
   */
  async delete(key: string): Promise<void> {
    try {
      if (this.redis) {
        await this.redis.del(`config:${key}`)
      }
      this.localCache.delete(key)
      
      this.logger.debug(`配置缓存已删除: ${key}`)
    } catch (error) {
      this.logger.error(`配置缓存删除失败: ${key}`, { error })
    }
  }

  /**
   * 清空所有缓存
   */
  async clear(): Promise<void> {
    try {
      if (this.redis) {
        const keys = await this.redis.keys('config:*')
        if (keys.length > 0) {
          await this.redis.del(...keys)
        }
      }
      this.localCache.clear()
      
      this.logger.info('所有配置缓存已清空')
    } catch (error) {
      this.logger.error('配置缓存清空失败', { error })
    }
  }

  /**
   * 获取缓存统计信息
   */
  async getStats(): Promise<{ redis: boolean; localSize: number; redisSize?: number }> {
    const stats = {
      redis: !!this.redis,
      localSize: this.localCache.size
    }

    if (this.redis) {
      try {
        const keys = await this.redis.keys('config:*')
        return { ...stats, redisSize: keys.length }
      } catch (error) {
        this.logger.error('获取Redis缓存统计失败', { error })
      }
    }

    return stats
  }
}