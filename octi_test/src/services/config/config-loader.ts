import { Logger } from '@/lib/logger'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

/**
 * 配置加载器 - 负责从多个数据源加载配置
 */
export class ConfigLoader {
  private logger = new Logger('ConfigLoader')
  private configCache = new Map<string, any>()
  private lastLoadTime = 0
  private readonly CACHE_TTL = 5 * 60 * 1000 // 5分钟

  /**
   * 从数据库加载配置
   */
  async loadFromDatabase(): Promise<Map<string, any>> {
    try {
      const configs = await prisma.configuration.findMany({
        where: { 
          isActive: true,
          deletedAt: null 
        },
        orderBy: { updatedAt: 'desc' }
      })

      const configMap = new Map<string, any>()
      
      for (const config of configs) {
        try {
          const value = JSON.parse(config.value)
          configMap.set(config.key, value)
        } catch (parseError) {
          this.logger.warn(`配置解析失败: ${config.key}`, { parseError })
          // 使用原始字符串值作为降级
          configMap.set(config.key, config.value)
        }
      }

      this.logger.info(`从数据库加载了 ${configMap.size} 个配置项`)
      return configMap

    } catch (error) {
      this.logger.error('数据库配置加载失败', { error })
      throw new Error('配置加载失败')
    }
  }

  /**
   * 从环境变量加载配置
   */
  loadFromEnvironment(): Map<string, any> {
    const envConfigs = new Map<string, any>()
    
    // OCTI核心配置
    const coreConfigs = {
      'octi.database.url': process.env.DATABASE_URL,
      'octi.redis.url': process.env.REDIS_URL,
      'octi.api.minimax_key': process.env.MINIMAX_API_KEY,
      'octi.api.deepseek_key': process.env.DEEPSEEK_API_KEY,
      'octi.cache.ttl': parseInt(process.env.CONFIG_CACHE_TTL || '3600'),
      'octi.security.jwt_secret': process.env.JWT_SECRET,
      'octi.app.environment': process.env.NODE_ENV || 'development'
    }

    for (const [key, value] of Object.entries(coreConfigs)) {
      if (value !== undefined && value !== null) {
        envConfigs.set(key, value)
      }
    }

    return envConfigs
  }

  /**
   * 加载默认配置
   */
  loadDefaults(): Map<string, any> {
    const defaults = new Map<string, any>()

    // OCTI评估维度默认配置
    defaults.set('octi.assessment.dimensions', [
      { id: 'S_F', name: '结构化-灵活化', weight: 0.25, poles: ['结构化', '灵活化'] },
      { id: 'I_T', name: '直觉-思考', weight: 0.25, poles: ['直觉', '思考'] },
      { id: 'M_V', name: '管理-愿景', weight: 0.25, poles: ['管理', '愿景'] },
      { id: 'A_D', name: '行动-深思', weight: 0.25, poles: ['行动', '深思'] }
    ])

    // 智能体默认配置
    defaults.set('octi.agents.questionnaire_designer', {
      model: 'abab6.5-chat',
      temperature: 0.7,
      maxTokens: 2000,
      retryAttempts: 3,
      timeout: 30000
    })

    defaults.set('octi.agents.organization_tutor', {
      standardModel: 'abab6.5-chat',
      professionalModel: 'deepseek-chat',
      temperature: 0.5,
      maxTokens: 3000,
      fusionWeight: 0.6
    })

    return defaults
  }

  /**
   * 合并配置 - 优先级：数据库 > 环境变量 > 默认值
   */
  async loadAllConfigs(): Promise<Map<string, any>> {
    const now = Date.now()
    
    // 检查缓存
    if (this.configCache.size > 0 && (now - this.lastLoadTime) < this.CACHE_TTL) {
      this.logger.debug('使用缓存的配置')
      return new Map(this.configCache)
    }

    try {
      // 加载各层配置
      const defaults = this.loadDefaults()
      const envConfigs = this.loadFromEnvironment()
      const dbConfigs = await this.loadFromDatabase()

      // 合并配置（后者覆盖前者）
      const mergedConfigs = new Map<string, any>()

      // 手动合并各个配置源，使用Array.from避免迭代器问题
      Array.from(defaults.entries()).forEach(([key, value]) => {
        mergedConfigs.set(key, value)
      })
      Array.from(envConfigs.entries()).forEach(([key, value]) => {
        mergedConfigs.set(key, value)
      })
      Array.from(dbConfigs.entries()).forEach(([key, value]) => {
        mergedConfigs.set(key, value)
      })

      // 更新缓存
      this.configCache.clear()
      Array.from(mergedConfigs.entries()).forEach(([key, value]) => {
        this.configCache.set(key, value)
      })
      this.lastLoadTime = now

      this.logger.info(`配置加载完成，共 ${mergedConfigs.size} 个配置项`)
      return mergedConfigs

    } catch (error) {
      this.logger.error('配置加载失败，使用默认配置', { error })
      return this.loadDefaults()
    }
  }

  /**
   * 清除缓存
   */
  clearCache(): void {
    this.configCache.clear()
    this.lastLoadTime = 0
    this.logger.info('配置缓存已清除')
  }
}