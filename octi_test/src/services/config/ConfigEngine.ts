import { ConfigService } from './ConfigService'
import { Logger } from '@/lib/logger'
import { z } from 'zod';

/**
 * 修复类型安全问题
 */
export type ConfigTemplate = string | number | boolean | ConfigTemplateObject | ConfigTemplate[];

export interface ConfigTemplateObject {
  [key: string]: ConfigTemplate;
}

export interface EnvironmentConfig {
  [key: string]: any;
}

/**
 * 添加输入验证Schema
 */
const TemplateVariablesSchema = z.record(z.union([
  z.string(),
  z.number(),
  z.boolean()
]));

const ConfigKeySchema = z.string().min(1).regex(/^[a-zA-Z0-9._-]+$/);

/**
 * 配置引擎 - 提供高级配置管理功能
 * 包括配置验证、模板处理、环境管理等
 */
export class ConfigEngine {
  private static instance: ConfigEngine | null = null
  private static isInitializing = false
  private configService: ConfigService | null = null
  private logger: Logger
  private templates: Map<string, ConfigTemplate> = new Map()
  private environments: Map<string, string> = new Map()

  private constructor() {
    this.logger = new Logger('ConfigEngine')
    this.initializeEnvironments()
  }

  /**
   * 获取配置引擎单例
   */
  public static async getInstance(): Promise<ConfigEngine> {
    if (ConfigEngine.instance && ConfigEngine.instance.configService) {
      return ConfigEngine.instance
    }

    if (ConfigEngine.isInitializing) {
      // 等待初始化完成
      while (ConfigEngine.isInitializing) {
        await new Promise(resolve => setTimeout(resolve, 10))
      }
      return ConfigEngine.instance!
    }

    ConfigEngine.isInitializing = true
    try {
      if (!ConfigEngine.instance) {
        ConfigEngine.instance = new ConfigEngine()
      }

      // 初始化ConfigService
      ConfigEngine.instance.configService = await ConfigService.getInstance()

      return ConfigEngine.instance
    } finally {
      ConfigEngine.isInitializing = false
    }
  }

  /**
   * 确保配置服务已初始化
   */
  private async ensureConfigService(): Promise<ConfigService> {
    if (!this.configService) {
      this.configService = await ConfigService.getInstance()
    }
    return this.configService
  }

  /**
   * 初始化配置引擎
   */
  public async initialize(): Promise<void> {
    try {
      this.logger.info('初始化配置引擎...')

      // 确保配置服务已初始化
      const configService = await this.ensureConfigService()

      // 初始化配置服务
      await configService.initialize()

      // 加载配置模板
      await this.loadTemplates()

      // 验证环境配置
      await this.validateEnvironmentConfig()

      this.logger.info('配置引擎初始化完成')
    } catch (error) {
      this.logger.error('配置引擎初始化失败', { error })
      throw error
    }
  }

  /**
   * 根据模板创建配置
   */
  public async createFromTemplate(
    templateName: string, 
    variables: Record<string, any>
  ): Promise<Record<string, any>> {
    // 验证输入
    ConfigKeySchema.parse(templateName);
    TemplateVariablesSchema.parse(variables);
    
    try {
      const template = this.templates.get(templateName)
      if (!template) {
        throw new Error(`配置模板不存在: ${templateName}`)
      }

      // 处理模板变量
      const config = this.processTemplate(template, variables)
      
      // 验证生成的配置
      await this.validateConfig(config)
      
      this.logger.info(`从模板创建配置: ${templateName}`, { variables })
      return config
    } catch (error) {
      this.logger.error(`从模板创建配置失败: ${templateName}`, { error, variables })
      throw error
    }
  }

  /**
   * 获取环境特定配置
   */
  public async getEnvironmentConfig(environment?: string): Promise<Record<string, any>> {
    const configService = await this.ensureConfigService()
    const env = environment || this.getCurrentEnvironment()
    const configKeys = await this.getEnvironmentConfigKeys(env)

    const config: Record<string, any> = {}
    for (const key of configKeys) {
      try {
        config[key] = await configService.get(key)
      } catch (error) {
        this.logger.warn(`获取环境配置失败: ${key}`, { environment: env, error })
      }
    }

    return config
  }

  /**
   * 批量更新配置
   */
  public async batchUpdate(configs: Record<string, any>): Promise<void> {
    const configService = await this.ensureConfigService()
    const errors: Array<{ key: string; error: any }> = []

    for (const [key, value] of Object.entries(configs)) {
      try {
        await configService.set(key, value)
      } catch (error) {
        errors.push({ key, error })
      }
    }

    if (errors.length > 0) {
      this.logger.error('批量更新配置部分失败', { errors })
      throw new Error(`批量更新失败，${errors.length} 个配置项更新失败`)
    }

    this.logger.info('批量更新配置成功', { count: Object.keys(configs).length })
  }

  /**
   * 导出配置
   */
  public async exportConfig(
    keys?: string[],
    format: 'json' | 'yaml' | 'env' = 'json'
  ): Promise<string> {
    try {
      const configService = await this.ensureConfigService()
      const configKeys = keys || configService.getKeys()
      const config = await configService.getMultiple(configKeys)

      switch (format) {
        case 'json':
          return JSON.stringify(config, null, 2)
        case 'yaml':
          return this.toYaml(config)
        case 'env':
          return this.toEnvFormat(config)
        default:
          throw new Error(`不支持的导出格式: ${format}`)
      }
    } catch (error) {
      this.logger.error('导出配置失败', { error, keys, format })
      throw error
    }
  }

  /**
   * 导入配置
   */
  public async importConfig(
    data: string,
    format: 'json' | 'yaml' | 'env' = 'json',
    merge: boolean = true
  ): Promise<void> {
    try {
      const configService = await this.ensureConfigService()
      let config: Record<string, any>

      switch (format) {
        case 'json':
          config = JSON.parse(data)
          break
        case 'yaml':
          config = this.fromYaml(data)
          break
        case 'env':
          config = this.fromEnvFormat(data)
          break
        default:
          throw new Error(`不支持的导入格式: ${format}`)
      }

      if (merge) {
        await this.batchUpdate(config)
      } else {
        // 清空现有配置
        const existingKeys = configService.getKeys()
        for (const key of existingKeys) {
          await configService.delete(key)
        }
        await this.batchUpdate(config)
      }

      this.logger.info('导入配置成功', { format, merge, count: Object.keys(config).length })
    } catch (error) {
      this.logger.error('导入配置失败', { error, format, merge })
      throw error
    }
  }

  /**
   * 验证配置完整性
   */
  public async validateIntegrity(): Promise<{
    valid: boolean
    errors: string[]
    warnings: string[]
  }> {
    const errors: string[] = []
    const warnings: string[] = []

    try {
      const configService = await this.ensureConfigService()

      // 检查必需配置
      const requiredConfigs = await this.getRequiredConfigs()
      for (const key of requiredConfigs) {
        if (!configService.has(key)) {
          errors.push(`缺少必需配置: ${key}`)
        }
      }

      // 检查配置依赖
      const dependencies = await this.getConfigDependencies()
      for (const [key, deps] of Object.entries(dependencies)) {
        if (configService.has(key)) {
          for (const dep of deps) {
            if (!configService.has(dep)) {
              warnings.push(`配置 ${key} 依赖的配置 ${dep} 不存在`)
            }
          }
        }
      }

      // 检查配置值有效性
      const configKeys = configService.getKeys()
      for (const key of configKeys) {
        try {
          const value = await configService.get(key)
          await this.validateConfigValue(key, value)
        } catch (error) {
          errors.push(`配置值无效: ${key} - ${error}`)
        }
      }

      return {
        valid: errors.length === 0,
        errors,
        warnings
      }
    } catch (error) {
      this.logger.error('验证配置完整性失败', { error })
      return {
        valid: false,
        errors: [`验证过程失败: ${error}`],
        warnings
      }
    }
  }

  /**
   * 获取配置变更历史
   */
  public async getChangeHistory(_key?: string): Promise<any[]> {
    // 这里应该从数据库或日志系统获取变更历史
    // 暂时返回空数组
    return []
  }

  /**
   * 回滚配置到指定版本
   */
  public async rollback(_version: string): Promise<void> {
    // 这里应该实现配置回滚逻辑
    throw new Error('配置回滚功能尚未实现')
  }

  /**
   * 初始化环境映射
   */
  private initializeEnvironments(): void {
    this.environments.set('development', 'dev')
    this.environments.set('testing', 'test')
    this.environments.set('staging', 'stage')
    this.environments.set('production', 'prod')
  }

  /**
   * 加载配置模板
   */
  private async loadTemplates(): Promise<void> {
    // OCTI评估配置模板
    this.templates.set('octi.assessment', {
      'octi.assessment.dimensions': '{{dimensions}}',
      'octi.assessment.scoring.method': '{{scoringMethod}}',
      'octi.assessment.weights': '{{weights}}',
      'octi.assessment.thresholds': '{{thresholds}}'
    })
    
    // AI智能体配置模板
    this.templates.set('octi.agents', {
      'octi.agents.questionnaire_designer.model': '{{questionnaireModel}}',
      'octi.agents.questionnaire_designer.temperature': '{{questionnaireTemperature}}',
      'octi.agents.assessment_mentor.model': '{{mentorModel}}',
      'octi.agents.assessment_mentor.temperature': '{{mentorTemperature}}'
    })
    
    // 数据库配置模板
    this.templates.set('database', {
      'database.url': 'postgresql://{{username}}:{{password}}@{{host}}:{{port}}/{{database}}',
      'database.pool.min': '{{poolMin}}',
      'database.pool.max': '{{poolMax}}',
      'database.ssl': '{{ssl}}'
    })
  }

  /**
   * 处理模板变量
   */
  private processTemplate(template: ConfigTemplate, variables: Record<string, any>): any {
    if (typeof template === 'string') {
      return template.replace(/\{\{(\w+)\}\}/g, (match: string, key: string) => {
        return variables[key] !== undefined ? String(variables[key]) : match
      })
    }

    if (typeof template === 'number' || typeof template === 'boolean') {
      return template
    }

    if (Array.isArray(template)) {
      return template.map(item => this.processTemplate(item as ConfigTemplate, variables))
    }

    if (typeof template === 'object' && template !== null) {
      const result: Record<string, any> = {}
      for (const [key, value] of Object.entries(template)) {
        result[key] = this.processTemplate(value as ConfigTemplate, variables)
      }
      return result
    }

    return template
  }

  /**
   * 验证配置
   */
  private async validateConfig(_config: Record<string, any>): Promise<void> {
    // 这里应该根据配置模式进行验证
    // 暂时跳过详细验证
  }

  /**
   * 获取当前环境
   */
  private getCurrentEnvironment(): string {
    return process.env.NODE_ENV || 'development'
  }

  /**
   * 获取环境特定配置键
   */
  private async getEnvironmentConfigKeys(environment: string): Promise<string[]> {
    // 这里应该根据环境返回相应的配置键
    const configService = await this.ensureConfigService()
    const allKeys = configService.getKeys()
    return allKeys.filter(key =>
      key.includes(environment) ||
      key.startsWith('octi.') ||
      key.startsWith('database.') ||
      key.startsWith('redis.')
    )
  }

  /**
   * 获取必需配置列表
   */
  private async getRequiredConfigs(): Promise<string[]> {
    return [
      'octi.assessment.dimensions',
      'octi.agents.questionnaire_designer.model',
      'octi.agents.assessment_mentor.model'
    ]
  }

  /**
   * 获取配置依赖关系
   */
  private async getConfigDependencies(): Promise<Record<string, string[]>> {
    return {
      'octi.agents.questionnaire_designer': ['octi.assessment.dimensions'],
      'octi.agents.assessment_mentor': ['octi.assessment.dimensions', 'octi.assessment.scoring.method']
    }
  }

  /**
   * 验证配置值
   */
  private async validateConfigValue(key: string, value: any): Promise<void> {
    // 根据配置键进行特定验证
    if (key.includes('temperature')) {
      if (typeof value !== 'number' || value < 0 || value > 2) {
        throw new Error('temperature 必须是 0-2 之间的数字')
      }
    }
    
    if (key.includes('model')) {
      if (typeof value !== 'string' || value.trim() === '') {
        throw new Error('model 必须是非空字符串')
      }
    }
    
    if (key.includes('url')) {
      if (typeof value !== 'string' || !value.startsWith('http')) {
        throw new Error('url 必须是有效的HTTP地址')
      }
    }
  }

  /**
   * 转换为YAML格式
   */
  private toYaml(obj: any): string {
    // 简单的YAML转换实现
    const yamlLines: string[] = []
    
    function processObject(obj: any, indent: string = ''): void {
      for (const [key, value] of Object.entries(obj)) {
        if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
          yamlLines.push(`${indent}${key}:`)
          processObject(value, indent + '  ')
        } else if (Array.isArray(value)) {
          yamlLines.push(`${indent}${key}:`)
          for (const item of value) {
            yamlLines.push(`${indent}  - ${item}`)
          }
        } else {
          yamlLines.push(`${indent}${key}: ${value}`)
        }
      }
    }
    
    processObject(obj)
    return yamlLines.join('\n')
  }

  /**
   * 从YAML格式解析
   */
  private fromYaml(_yaml: string): Record<string, any> {
    // 简单的YAML解析实现
    // 在实际项目中应该使用专业的YAML库
    throw new Error('YAML解析功能尚未实现')
  }

  /**
   * 转换为环境变量格式
   */
  private toEnvFormat(obj: any): string {
    const envLines: string[] = []
    
    function processObject(obj: any, prefix: string = ''): void {
      for (const [key, value] of Object.entries(obj)) {
        const envKey = prefix ? `${prefix}_${key.toUpperCase()}` : key.toUpperCase()
        
        if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
          processObject(value, envKey)
        } else {
          envLines.push(`${envKey}=${value}`)
        }
      }
    }
    
    processObject(obj)
    return envLines.join('\n')
  }

  /**
   * 从环境变量格式解析
   */
  private fromEnvFormat(env: string): Record<string, any> {
    const result: Record<string, any> = {}
    const lines = env.split('\n').filter(line => line.trim() && !line.startsWith('#'))
    
    for (const line of lines) {
      const [key, ...valueParts] = line.split('=')
      if (key && valueParts.length > 0) {
        const value = valueParts.join('=')
        result[key.toLowerCase()] = value
      }
    }
    
    return result
  }

  /**
   * 验证环境配置
   */
  private async validateEnvironmentConfig(): Promise<void> {
    const currentEnv = this.getCurrentEnvironment()
    this.logger.info(`验证环境配置: ${currentEnv}`)
    
    // 检查环境特定的必需配置
    const envConfigs = await this.getEnvironmentConfig(currentEnv)
    
    if (Object.keys(envConfigs).length === 0) {
      this.logger.warn(`环境 ${currentEnv} 没有找到任何配置`)
    }
    
    this.logger.info(`环境配置验证完成: ${currentEnv}`)
  }
}
