import { ConfigSchema, ConfigValue, CacheEntry } from '@/types'
import { Logger } from '@/lib/logger'
import { RedisCache } from '@/lib/cache'
import { z } from 'zod'

/**
 * 配置服务 - OCTI系统的核心配置管理
 * 支持配置的加载、验证、缓存和热更新
 */
export class ConfigService {
  private static instance: ConfigService | null = null;
  private static isInitializing = false;

  public static async getInstance(): Promise<ConfigService> {
    if (ConfigService.instance) {
      return ConfigService.instance;
    }

    if (ConfigService.isInitializing) {
      // 等待初始化完成
      while (ConfigService.isInitializing) {
        await new Promise(resolve => setTimeout(resolve, 10));
      }
      return ConfigService.instance!;
    }

    ConfigService.isInitializing = true;
    try {
      ConfigService.instance = new ConfigService();
      await ConfigService.instance.initialize();
      return ConfigService.instance;
    } finally {
      ConfigService.isInitializing = false;
    }
  }

  private cache: RedisCache
  private logger: Logger
  private configSchemas: Map<string, ConfigSchema> = new Map()
  private configValues: Map<string, any> = new Map()
  private watchers: Map<string, Array<(value: any) => void>> = new Map()

  private constructor() {
    this.cache = new RedisCache({
      ttl: parseInt(process.env.CONFIG_CACHE_TTL || '3600'),
      keyPrefix: 'config:',
      strategy: 'ttl'
    })
    this.logger = new Logger('ConfigService')
  }

  /**
   * 初始化配置服务
   */
  public async initialize(): Promise<void> {
    try {
      this.logger.info('初始化配置服务...')
      
      // 加载配置模式
      await this.loadConfigSchemas()
      
      // 加载配置值
      await this.loadConfigValues()
      
      // 验证配置
      await this.validateConfigs()
      
      this.logger.info('配置服务初始化完成')
    } catch (error) {
      this.logger.error('配置服务初始化失败', { error })
      throw error
    }
  }

  /**
   * 获取配置值
   */
  public async get<T = any>(key: string, defaultValue?: T): Promise<T> {
    try {
      // 添加输入验证
      if (!key || typeof key !== 'string') {
        throw new Error('配置键必须是非空字符串');
      }

      const cached = await this.cache.get<T>(key);
      if (cached !== null) {
        return cached;
      }

      const value = this.configValues.get(key);
      if (value !== undefined) {
        await this.cache.set(key, value);
        return value;
      }

      if (defaultValue !== undefined) {
        return defaultValue;
      }

      throw new Error(`配置项 ${key} 不存在且未提供默认值`);
    } catch (error) {
      this.logger.error(`获取配置失败: ${key}`, { 
        error: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined
      });
      throw error;
    }
  }

  /**
   * 设置配置值
   */
  public async set(key: string, value: any): Promise<void> {
    try {
      // 验证配置值
      await this.validateConfigValue(key, value)
      
      // 更新内存
      this.configValues.set(key, value)
      
      // 更新缓存
      await this.cache.set(key, value)
      
      // 通知观察者
      await this.notifyWatchers(key, value)
      
      this.logger.info(`配置已更新: ${key}`, { value })
    } catch (error) {
      this.logger.error(`设置配置失败: ${key}`, { error, value })
      throw error
    }
  }

  /**
   * 批量获取配置
   */
  public async getMultiple(keys: string[]): Promise<Record<string, any>> {
    const result: Record<string, any> = {};
    const uncachedKeys: string[] = [];
    
    // 批量从缓存获取
    const cachePromises = keys.map(async (key) => {
      const cached = await this.cache.get(key);
      if (cached !== null) {
        result[key] = cached;
      } else {
        uncachedKeys.push(key);
      }
    });
    
    await Promise.all(cachePromises);
    
    // 批量获取未缓存的配置
    for (const key of uncachedKeys) {
      try {
        const value = this.configValues.get(key);
        if (value !== undefined) {
          result[key] = value;
          // 异步缓存，不阻塞返回
          this.cache.set(key, value).catch(err => 
            this.logger.warn(`缓存配置失败: ${key}`, { error: err })
          );
        } else {
          result[key] = null;
        }
      } catch (error) {
        this.logger.warn(`获取配置失败: ${key}`, { error });
        result[key] = null;
      }
    }
    
    return result;
  }

  /**
   * 获取配置模式
   */
  public getSchema(schemaId: string): ConfigSchema | undefined {
    return this.configSchemas.get(schemaId)
  }

  /**
   * 注册配置模式
   */
  public async registerSchema(schema: ConfigSchema): Promise<void> {
    try {
      // 验证模式
      this.validateSchema(schema)
      
      // 存储模式
      this.configSchemas.set(schema.id, schema)
      
      this.logger.info(`配置模式已注册: ${schema.id}`, { schema: schema.name })
    } catch (error) {
      this.logger.error(`注册配置模式失败: ${schema.id}`, { error })
      throw error
    }
  }

  /**
   * 监听配置变化
   */
  public watch(key: string, callback: (value: any) => void): () => void {
    if (!this.watchers.has(key)) {
      this.watchers.set(key, [])
    }
    
    const watchers = this.watchers.get(key)!
    watchers.push(callback)
    
    // 返回取消监听的函数
    return () => {
      const index = watchers.indexOf(callback)
      if (index > -1) {
        watchers.splice(index, 1)
      }
    }
  }

  /**
   * 重新加载配置
   */
  public async reload(): Promise<void> {
    try {
      this.logger.info('重新加载配置...')
      
      // 清除缓存
      await this.cache.clear()
      
      // 重新加载
      await this.loadConfigValues()
      await this.validateConfigs()
      
      this.logger.info('配置重新加载完成')
    } catch (error) {
      this.logger.error('重新加载配置失败', { error })
      throw error
    }
  }

  /**
   * 获取所有配置键
   */
  public getKeys(): string[] {
    return Array.from(this.configValues.keys())
  }

  /**
   * 检查配置是否存在
   */
  public has(key: string): boolean {
    return this.configValues.has(key)
  }

  /**
   * 删除配置
   */
  public async delete(key: string): Promise<void> {
    try {
      this.configValues.delete(key)
      await this.cache.delete(key)
      
      this.logger.info(`配置已删除: ${key}`)
    } catch (error) {
      this.logger.error(`删除配置失败: ${key}`, { error })
      throw error
    }
  }

  /**
   * 加载配置模式
   */
  private async loadConfigSchemas(): Promise<void> {
    // 这里应该从数据库或文件系统加载配置模式
    // 暂时使用硬编码的默认模式
    const defaultSchemas: ConfigSchema[] = [
      {
        id: 'octi.assessment',
        name: 'OCTI评估配置',
        version: '1.0.0',
        description: 'OCTI四维八极评估相关配置',
        schema: {
          type: 'object',
          properties: {
            dimensions: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  id: { type: 'string' },
                  name: { type: 'string' },
                  weight: { type: 'number', minimum: 0, maximum: 1 }
                }
              }
            }
          }
        },
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        id: 'octi.agents',
        name: 'AI智能体配置',
        version: '1.0.0',
        description: 'AI智能体相关配置',
        schema: {
          type: 'object',
          properties: {
            questionnaire_designer: {
              type: 'object',
              properties: {
                model: { type: 'string' },
                temperature: { type: 'number', minimum: 0, maximum: 2 },
                maxTokens: { type: 'number', minimum: 1 }
              }
            }
          }
        },
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      }
    ]

    for (const schema of defaultSchemas) {
      this.configSchemas.set(schema.id, schema)
    }
  }

  /**
   * 加载配置值
   */
  private async loadConfigValues(): Promise<void> {
    // 这里应该从数据库加载配置值
    // 暂时使用环境变量和默认值
    const defaultConfigs = {
      'octi.assessment.dimensions': [
        { id: 'org', name: '组织维度', weight: 0.25 },
        { id: 'culture', name: '文化维度', weight: 0.25 },
        { id: 'talent', name: '人才维度', weight: 0.25 },
        { id: 'innovation', name: '创新维度', weight: 0.25 }
      ],
      'octi.agents.questionnaire_designer': {
        model: process.env.MINIMAX_MODEL || 'abab6.5-chat',
        temperature: 0.7,
        maxTokens: 2000
      },
      'octi.agents.assessment_mentor': {
        model: process.env.DEEPSEEK_MODEL || 'deepseek-chat',
        temperature: 0.5,
        maxTokens: 3000
      },
      'octi.cache.ttl': parseInt(process.env.CONFIG_CACHE_TTL || '3600'),
      'octi.database.url': process.env.DATABASE_URL,
      'octi.redis.url': process.env.REDIS_URL
    }

    for (const [key, value] of Object.entries(defaultConfigs)) {
      this.configValues.set(key, value)
    }
  }

  /**
   * 验证所有配置
   */
  private async validateConfigs(): Promise<void> {
    const entries = Array.from(this.configValues.entries())
    for (const [key, value] of entries) {
      await this.validateConfigValue(key, value)
    }
  }

  /**
   * 验证配置值
   */
  private async validateConfigValue(key: string, value: any): Promise<void> {
    // 根据配置键找到对应的模式
    const schemaId = this.getSchemaIdFromKey(key)
    if (!schemaId) {
      return // 没有模式则跳过验证
    }

    const schema = this.configSchemas.get(schemaId)
    if (!schema) {
      return
    }

    // 使用 Zod 进行验证
    try {
      // 这里应该根据 schema.schema 创建 Zod 验证器
      // 暂时跳过详细验证
    } catch (error) {
      throw new Error(`配置验证失败: ${key} - ${error}`)
    }
  }

  /**
   * 验证配置模式
   */
  private validateSchema(schema: ConfigSchema): void {
    if (!schema.id || !schema.name || !schema.version) {
      throw new Error('配置模式缺少必要字段')
    }

    if (!schema.schema || typeof schema.schema !== 'object') {
      throw new Error('配置模式格式无效')
    }
  }

  /**
   * 从配置键获取模式ID
   */
  private getSchemaIdFromKey(key: string): string | null {
    // 简单的键到模式映射
    if (key.startsWith('octi.assessment')) {
      return 'octi.assessment'
    }
    if (key.startsWith('octi.agents')) {
      return 'octi.agents'
    }
    return null
  }

  /**
   * 通知配置变化观察者
   */
  private async notifyWatchers(key: string, value: any): Promise<void> {
    const watchers = this.watchers.get(key)
    if (!watchers || watchers.length === 0) {
      return
    }

    for (const callback of watchers) {
      try {
        callback(value)
      } catch (error) {
        this.logger.error(`配置观察者回调失败: ${key}`, { error })
      }
    }
  }
}
