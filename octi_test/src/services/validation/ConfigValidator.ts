import { Logger } from '@/lib/logger'

/**
 * 配置验证器
 * 提供配置值的验证功能
 */
export class ConfigValidator {
  private logger: Logger
  private validationRules: Map<string, ValidationRule[]> = new Map()

  constructor() {
    this.logger = new Logger('ConfigValidator')
    this.initializeValidationRules()
  }

  /**
   * 验证配置值
   */
  public async validate(key: string, value: any): Promise<ValidationResult> {
    try {
      const rules = this.validationRules.get(key) || []
      const errors: string[] = []
      const warnings: string[] = []

      for (const rule of rules) {
        const result = await this.applyRule(rule, value)
        if (!result.valid) {
          if (rule.severity === 'error') {
            errors.push(result.message || '验证失败')
          } else {
            warnings.push(result.message || '验证警告')
          }
        }
      }

      return {
        valid: errors.length === 0,
        errors,
        warnings
      }
    } catch (error) {
      this.logger.error(`验证配置失败: ${key}`, { error, value })
      return {
        valid: false,
        errors: [`验证过程失败: ${error}`],
        warnings: []
      }
    }
  }

  /**
   * 批量验证配置
   */
  public async validateBatch(configs: Record<string, any>): Promise<BatchValidationResult> {
    const results: Record<string, ValidationResult> = {}
    let hasErrors = false

    for (const [key, value] of Object.entries(configs)) {
      const result = await this.validate(key, value)
      results[key] = result
      if (!result.valid) {
        hasErrors = true
      }
    }

    return {
      valid: !hasErrors,
      results
    }
  }

  /**
   * 添加验证规则
   */
  public addRule(key: string, rule: ValidationRule): void {
    if (!this.validationRules.has(key)) {
      this.validationRules.set(key, [])
    }
    this.validationRules.get(key)!.push(rule)
  }

  /**
   * 移除验证规则
   */
  public removeRule(key: string, ruleName?: string): void {
    if (!ruleName) {
      this.validationRules.delete(key)
      return
    }

    const rules = this.validationRules.get(key)
    if (rules) {
      const filteredRules = rules.filter(rule => rule.name !== ruleName)
      this.validationRules.set(key, filteredRules)
    }
  }

  /**
   * 获取配置的验证规则
   */
  public getRules(key: string): ValidationRule[] {
    return this.validationRules.get(key) || []
  }

  /**
   * 初始化验证规则
   */
  private initializeValidationRules(): void {
    // OCTI评估相关验证规则
    this.addRule('octi.assessment.dimensions', {
      name: 'dimensions_array',
      type: 'array',
      severity: 'error',
      validator: (value) => {
        if (!Array.isArray(value)) {
          return { valid: false, message: '维度配置必须是数组' }
        }
        if (value.length === 0) {
          return { valid: false, message: '至少需要一个维度' }
        }
        return { valid: true }
      }
    })

    // AI智能体相关验证规则
    this.addRule('octi.agents.questionnaire_designer.model', {
      name: 'model_string',
      type: 'string',
      severity: 'error',
      validator: (value) => {
        if (typeof value !== 'string' || value.trim() === '') {
          return { valid: false, message: '模型名称必须是非空字符串' }
        }
        return { valid: true }
      }
    })

    this.addRule('octi.agents.questionnaire_designer.temperature', {
      name: 'temperature_range',
      type: 'number',
      severity: 'error',
      validator: (value) => {
        if (typeof value !== 'number') {
          return { valid: false, message: 'temperature必须是数字' }
        }
        if (value < 0 || value > 2) {
          return { valid: false, message: 'temperature必须在0-2之间' }
        }
        return { valid: true }
      }
    })

    this.addRule('octi.agents.assessment_mentor.model', {
      name: 'model_string',
      type: 'string',
      severity: 'error',
      validator: (value) => {
        if (typeof value !== 'string' || value.trim() === '') {
          return { valid: false, message: '模型名称必须是非空字符串' }
        }
        return { valid: true }
      }
    })

    // 数据库相关验证规则
    this.addRule('octi.database.url', {
      name: 'database_url',
      type: 'string',
      severity: 'error',
      validator: (value) => {
        if (typeof value !== 'string') {
          return { valid: false, message: '数据库URL必须是字符串' }
        }
        if (!value.startsWith('postgresql://')) {
          return { valid: false, message: '数据库URL必须是有效的PostgreSQL连接字符串' }
        }
        return { valid: true }
      }
    })

    // Redis相关验证规则
    this.addRule('octi.redis.url', {
      name: 'redis_url',
      type: 'string',
      severity: 'error',
      validator: (value) => {
        if (typeof value !== 'string') {
          return { valid: false, message: 'Redis URL必须是字符串' }
        }
        if (!value.startsWith('redis://')) {
          return { valid: false, message: 'Redis URL必须是有效的Redis连接字符串' }
        }
        return { valid: true }
      }
    })

    // 缓存TTL验证规则
    this.addRule('octi.cache.ttl', {
      name: 'ttl_positive',
      type: 'number',
      severity: 'error',
      validator: (value) => {
        if (typeof value !== 'number') {
          return { valid: false, message: 'TTL必须是数字' }
        }
        if (value <= 0) {
          return { valid: false, message: 'TTL必须是正数' }
        }
        return { valid: true }
      }
    })

    // API密钥验证规则
    this.addRule('octi.api.minimax_key', {
      name: 'api_key_format',
      type: 'string',
      severity: 'error',
      validator: (value) => {
        if (typeof value !== 'string' || value.trim() === '') {
          return { valid: false, message: 'API密钥必须是非空字符串' }
        }
        if (value.length < 10) {
          return { valid: false, message: 'API密钥长度不足' }
        }
        return { valid: true }
      }
    })

    this.addRule('octi.api.deepseek_key', {
      name: 'api_key_format',
      type: 'string',
      severity: 'error',
      validator: (value) => {
        if (typeof value !== 'string' || value.trim() === '') {
          return { valid: false, message: 'API密钥必须是非空字符串' }
        }
        if (value.length < 10) {
          return { valid: false, message: 'API密钥长度不足' }
        }
        return { valid: true }
      }
    })
  }

  /**
   * 应用验证规则
   */
  private async applyRule(rule: ValidationRule, value: any): Promise<RuleResult> {
    try {
      if (rule.validator) {
        return await rule.validator(value)
      }

      // 基础类型验证
      switch (rule.type) {
        case 'string':
          if (typeof value !== 'string') {
            return { valid: false, message: '值必须是字符串' }
          }
          break
        case 'number':
          if (typeof value !== 'number') {
            return { valid: false, message: '值必须是数字' }
          }
          break
        case 'boolean':
          if (typeof value !== 'boolean') {
            return { valid: false, message: '值必须是布尔值' }
          }
          break
        case 'array':
          if (!Array.isArray(value)) {
            return { valid: false, message: '值必须是数组' }
          }
          break
        case 'object':
          if (typeof value !== 'object' || value === null || Array.isArray(value)) {
            return { valid: false, message: '值必须是对象' }
          }
          break
      }

      return { valid: true }
    } catch (error) {
      this.logger.error(`应用验证规则失败: ${rule.name}`, { error, value })
      return { valid: false, message: `验证规则执行失败: ${error}` }
    }
  }
}

// 类型定义
export interface ValidationRule {
  name: string
  type: 'string' | 'number' | 'boolean' | 'array' | 'object'
  severity: 'error' | 'warning'
  validator?: (value: any) => Promise<RuleResult> | RuleResult
  description?: string
}

export interface RuleResult {
  valid: boolean
  message?: string
}

export interface ValidationResult {
  valid: boolean
  errors: string[]
  warnings: string[]
}

export interface BatchValidationResult {
  valid: boolean
  results: Record<string, ValidationResult>
}