import { Logger } from '@/lib/logger'
import { z } from 'zod'

// 提示词模板Schema
const PromptTemplateSchema = z.object({
  id: z.string(),
  name: z.string(),
  systemPrompt: z.string(),
  userPromptTemplate: z.string(),
  variables: z.array(z.string()),
  version: z.string().optional(),
  metadata: z.record(z.any()).optional()
})

export type PromptTemplate = z.infer<typeof PromptTemplateSchema>

/**
 * 提示词构建器配置接口
 */
export interface PromptBuilderConfig {
  templatePath?: string;
  cacheEnabled?: boolean;
  maxTokens?: number;
  temperature?: number;
}

/**
 * 提示词构建器类
 */
export class PromptBuilder {
  private logger = new Logger('PromptBuilder')
  private templates = new Map<string, PromptTemplate>()
  private config: PromptBuilderConfig;

  constructor(config: PromptBuilderConfig = {}) {
    this.config = {
      templatePath: './config/prompts',
      cacheEnabled: true,
      maxTokens: 4000,
      temperature: 0.7,
      ...config
    };
  }

  /**
   * 获取默认配置
   */
  private getDefaultConfig(): PromptBuilderConfig {
    return {
      templatePath: './config/prompts',
      cacheEnabled: true,
      maxTokens: 4000,
      temperature: 0.7
    };
  }

  /**
   * 初始化默认提示词模板
   */
  private initializeDefaultTemplates(): void {
    // 问卷设计师提示词模板
    this.addTemplate({
      id: 'questionnaire_designer',
      name: '问卷设计师',
      systemPrompt: `你是OCTI组织能力评估系统的专业问卷设计师。你的任务是根据用户需求设计高质量的评估问卷。

核心要求：
1. 严格遵循OCTI四维八极理论框架
2. 问题设计要具有科学性和专业性
3. 确保问题的区分度和信效度
4. 输出标准JSON格式的问卷结构

OCTI四维八极框架：
- S/F维度：结构化 ↔ 灵活化
- I/T维度：直觉 ↔ 思考  
- M/V维度：管理 ↔ 愿景
- A/D维度：行动 ↔ 深思

请确保每个维度的问题数量均衡，问题表述清晰准确。`,
      userPromptTemplate: `请为以下评估需求设计问卷：

评估类型：{{assessmentType}}
目标维度：{{dimensions}}
问卷版本：{{version}}
特殊要求：{{requirements}}

请生成包含以下结构的JSON格式问卷：
{
  "id": "问卷唯一标识",
  "title": "问卷标题",
  "description": "问卷描述",
  "version": "版本信息",
  "questions": [
    {
      "id": "问题ID",
      "text": "问题内容",
      "type": "问题类型",
      "dimension": "所属维度",
      "subdimension": "子维度",
      "options": [选项数组],
      "weight": 权重值
    }
  ],
  "metadata": {
    "estimatedTime": 预估完成时间,
    "totalQuestions": 问题总数,
    "dimensionDistribution": 维度分布统计
  }
}`,
      variables: ['assessmentType', 'dimensions', 'version', 'requirements']
    })

    // 组织评估导师提示词模板
    this.addTemplate({
      id: 'organization_tutor_standard',
      name: '组织评估导师-标准版',
      systemPrompt: `你是OCTI组织能力评估系统的专业分析师。你的任务是基于问卷回答结果，提供深入的组织能力分析和改进建议。

分析框架：
1. OCTI四维八极得分计算
2. 组织能力优势识别
3. 改进机会分析
4. 具体行动建议

输出要求：
- 客观准确的数据分析
- 实用可行的改进建议
- 结构化的报告格式
- 专业的语言表达`,
      userPromptTemplate: `请分析以下组织评估数据：

组织信息：{{organizationInfo}}
问卷回答：{{responses}}
评估版本：{{version}}

请提供包含以下内容的分析报告：
1. 总体评估得分
2. 四维八极详细分析
3. 组织优势总结
4. 改进建议
5. 下一步行动计划`,
      variables: ['organizationInfo', 'responses', 'version']
    })

    this.logger.info(`已加载 ${this.templates.size} 个默认提示词模板`)
  }

  /**
   * 添加提示词模板
   */
  addTemplate(template: PromptTemplate): void {
    const validated = PromptTemplateSchema.parse(template)
    this.templates.set(validated.id, validated)
    this.logger.debug(`已添加提示词模板: ${validated.id}`)
  }

  /**
   * 获取提示词模板
   */
  getTemplate(id: string): PromptTemplate | null {
    return this.templates.get(id) || null
  }

  /**
   * 构建提示词
   */
  buildPrompt(templateId: string, variables: Record<string, any>): {
    systemPrompt: string
    userPrompt: string
  } {
    const template = this.getTemplate(templateId)
    if (!template) {
      throw new Error(`提示词模板不存在: ${templateId}`)
    }

    // 检查必需变量
    const missingVars = template.variables.filter(varName => 
      !(varName in variables) || variables[varName] === undefined
    )
    
    if (missingVars.length > 0) {
      throw new Error(`缺少必需变量: ${missingVars.join(', ')}`)
    }

    // 替换变量
    let userPrompt = template.userPromptTemplate
    for (const [key, value] of Object.entries(variables)) {
      const placeholder = `{{${key}}}`
      const replacement = typeof value === 'string' ? value : JSON.stringify(value)
      userPrompt = userPrompt.replace(new RegExp(placeholder, 'g'), replacement)
    }

    this.logger.debug(`已构建提示词: ${templateId}`, {
      variablesUsed: Object.keys(variables)
    })

    return {
      systemPrompt: template.systemPrompt,
      userPrompt
    }
  }

  /**
   * 列出所有模板
   */
  listTemplates(): PromptTemplate[] {
    return Array.from(this.templates.values())
  }

  /**
   * 验证提示词安全性
   */
  validatePromptSafety(prompt: string): { safe: boolean; issues: string[] } {
    const issues: string[] = []
    
    // 检查潜在的注入攻击
    const dangerousPatterns = [
      /ignore\s+previous\s+instructions/i,
      /system\s*:\s*you\s+are\s+now/i,
      /forget\s+everything/i,
      /new\s+instructions/i,
      /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
      /javascript:/i,
      /on\w+\s*=/i
    ]

    for (const pattern of dangerousPatterns) {
      if (pattern.test(prompt)) {
        issues.push(`检测到潜在的注入攻击模式: ${pattern.source}`)
      }
    }

    // 检查敏感信息泄露
    const sensitivePatterns = [
      /api[_-]?key/i,
      /password/i,
      /secret/i,
      /token/i,
      /credential/i
    ]

    for (const pattern of sensitivePatterns) {
      if (pattern.test(prompt)) {
        issues.push(`检测到可能的敏感信息: ${pattern.source}`)
      }
    }

    return {
      safe: issues.length === 0,
      issues
    }
  }
}
