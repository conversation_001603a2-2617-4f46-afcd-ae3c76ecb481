import { Logger } from '@/lib/logger'
import { z } from 'zod'

// LLM请求配置Schema
const LLMRequestSchema = z.object({
  model: z.string(),
  messages: z.array(z.object({
    role: z.enum(['system', 'user', 'assistant']),
    content: z.string()
  })),
  temperature: z.number().min(0).max(2).optional(),
  maxTokens: z.number().positive().optional(),
  stream: z.boolean().optional()
})

// LLM响应Schema
const LLMResponseSchema = z.object({
  id: z.string(),
  choices: z.array(z.object({
    message: z.object({
      role: z.string(),
      content: z.string()
    }),
    finishReason: z.string().optional()
  })),
  usage: z.object({
    promptTokens: z.number(),
    completionTokens: z.number(),
    totalTokens: z.number()
  }).optional()
})

export type LLMRequest = z.infer<typeof LLMRequestSchema>
export type LLMResponse = z.infer<typeof LLMResponseSchema> & {
  // 添加便捷的content属性
  content?: string;
}

/**
 * LLM API客户端配置接口
 */
export interface LLMConfig {
  minimax?: {
    apiKey?: string;
    baseUrl?: string;
    timeout?: number;
  };
  deepseek?: {
    apiKey?: string;
    baseUrl?: string;
    timeout?: number;
  };
  defaultTimeout?: number;
  maxRetries?: number;
}

/**
 * LLM API客户端类
 */
export class LLMApiClient {
  private logger = new Logger('LLMApiClient')
  private config: LLMConfig;

  // 添加缺失的常量属性
  private readonly MINIMAX_BASE_URL: string;
  private readonly DEEPSEEK_BASE_URL: string;
  private readonly MAX_RETRIES: number;
  private readonly DEFAULT_TIMEOUT: number;

  constructor(config: LLMConfig = {}) {
    this.config = {
      defaultTimeout: 30000,
      maxRetries: 3,
      minimax: {
        apiKey: process.env.MINIMAX_API_KEY,
        baseUrl: process.env.MINIMAX_BASE_URL || 'https://api.minimax.chat',
        timeout: 30000,
        ...config.minimax
      },
      deepseek: {
        apiKey: process.env.DEEPSEEK_API_KEY,
        baseUrl: process.env.DEEPSEEK_BASE_URL || 'https://api.deepseek.com',
        timeout: 30000,
        ...config.deepseek
      },
      ...config
    };

    // 初始化常量属性
    this.MINIMAX_BASE_URL = this.config.minimax?.baseUrl || 'https://api.minimax.chat';
    this.DEEPSEEK_BASE_URL = this.config.deepseek?.baseUrl || 'https://api.deepseek.com';
    this.MAX_RETRIES = this.config.maxRetries || 3;
    this.DEFAULT_TIMEOUT = this.config.defaultTimeout || 30000;
  }

  /**
   * 获取默认配置
   */
  private getDefaultConfig(): LLMConfig {
    return {
      defaultTimeout: 30000,
      maxRetries: 3,
      minimax: {
        apiKey: process.env.MINIMAX_API_KEY,
        baseUrl: process.env.MINIMAX_BASE_URL || 'https://api.minimax.chat',
        timeout: 30000
      },
      deepseek: {
        apiKey: process.env.DEEPSEEK_API_KEY,
        baseUrl: process.env.DEEPSEEK_BASE_URL || 'https://api.deepseek.com',
        timeout: 30000
      }
    };
  }

  /**
   * 调用MiniMax API
   */
  async callMiniMax(request: LLMRequest): Promise<LLMResponse> {
    const apiKey = process.env.MINIMAX_API_KEY
    if (!apiKey) {
      throw new Error('MiniMax API密钥未配置')
    }

    return this.makeRequest(
      `${this.MINIMAX_BASE_URL}/text/chatcompletion`,
      {
        ...request,
        model: request.model || 'abab6.5-chat'
      },
      {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json'
      }
    )
  }

  /**
   * 调用DeepSeek API
   */
  async callDeepSeek(request: LLMRequest): Promise<LLMResponse> {
    const apiKey = process.env.DEEPSEEK_API_KEY
    if (!apiKey) {
      throw new Error('DeepSeek API密钥未配置')
    }

    return this.makeRequest(
      `${this.DEEPSEEK_BASE_URL}/chat/completions`,
      {
        ...request,
        model: request.model || 'deepseek-chat'
      },
      {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json'
      }
    )
  }

  /**
   * 统一的HTTP请求方法
   */
  private async makeRequest(
    url: string, 
    data: any, 
    headers: Record<string, string>
  ): Promise<LLMResponse> {
    let lastError: Error | null = null

    for (let attempt = 1; attempt <= this.MAX_RETRIES; attempt++) {
      try {
        this.logger.debug(`LLM API请求 (尝试 ${attempt}/${this.MAX_RETRIES})`, { 
          url: url.replace(/\/v1.*/, '/v1/***'),
          model: data.model 
        })

        const controller = new AbortController()
        const timeoutId = setTimeout(() => controller.abort(), this.DEFAULT_TIMEOUT)

        const response = await fetch(url, {
          method: 'POST',
          headers,
          body: JSON.stringify(data),
          signal: controller.signal
        })

        clearTimeout(timeoutId)

        if (!response.ok) {
          const errorText = await response.text()
          throw new Error(`HTTP ${response.status}: ${errorText}`)
        }

        const responseData = await response.json()
        const validatedResponse = LLMResponseSchema.parse(responseData)

        // 添加便捷的content属性
        const responseWithContent: LLMResponse = {
          ...validatedResponse,
          content: validatedResponse.choices[0]?.message?.content || ''
        }

        this.logger.info('LLM API调用成功', {
          model: data.model,
          tokensUsed: validatedResponse.usage?.totalTokens || 0,
          attempt
        })

        return responseWithContent

      } catch (error) {
        lastError = error as Error
        this.logger.warn(`LLM API调用失败 (尝试 ${attempt}/${this.MAX_RETRIES})`, { 
          error: lastError.message,
          model: data.model 
        })

        // 如果不是最后一次尝试，等待后重试
        if (attempt < this.MAX_RETRIES) {
          const delay = Math.pow(2, attempt) * 1000 // 指数退避
          await new Promise(resolve => setTimeout(resolve, delay))
        }
      }
    }

    this.logger.error('LLM API调用最终失败', { 
      error: lastError?.message,
      attempts: this.MAX_RETRIES 
    })
    throw lastError || new Error('LLM API调用失败')
  }

  /**
   * 统一的聊天接口
   */
  async chat(provider: 'minimax' | 'deepseek', request: LLMRequest): Promise<LLMResponse> {
    switch (provider) {
      case 'minimax':
        return this.callMiniMax(request);
      case 'deepseek':
        return this.callDeepSeek(request);
      default:
        throw new Error(`不支持的LLM提供商: ${provider}`);
    }
  }

  /**
   * 双模型聊天接口
   */
  async dualModelChat(
    request: LLMRequest,
    strategy: 'sequential' | 'parallel' = 'sequential'
  ): Promise<LLMResponse> {
    if (strategy === 'parallel') {
      // 并行调用两个模型
      const [minimaxResult, deepseekResult] = await Promise.allSettled([
        this.callMiniMax(request),
        this.callDeepSeek(request)
      ]);

      // 选择成功的结果，优先选择MiniMax
      if (minimaxResult.status === 'fulfilled') {
        return minimaxResult.value;
      } else if (deepseekResult.status === 'fulfilled') {
        return deepseekResult.value;
      } else {
        throw new Error('双模型调用均失败');
      }
    } else {
      // 顺序调用，失败时切换到另一个模型
      try {
        return await this.callMiniMax(request);
      } catch (error) {
        this.logger.warn('MiniMax调用失败，切换到DeepSeek', { error });
        return await this.callDeepSeek(request);
      }
    }
  }

  /**
   * 健康检查
   */
  async healthCheck(): Promise<{ minimax: boolean; deepseek: boolean }> {
    const results = {
      minimax: false,
      deepseek: false
    }

    // 检查MiniMax
    try {
      await this.callMiniMax({
        model: 'abab6.5-chat',
        messages: [{ role: 'user', content: 'ping' }],
        maxTokens: 10
      })
      results.minimax = true
    } catch (error) {
      this.logger.warn('MiniMax健康检查失败', { error })
    }

    // 检查DeepSeek
    try {
      await this.callDeepSeek({
        model: 'deepseek-chat',
        messages: [{ role: 'user', content: 'ping' }],
        maxTokens: 10
      })
      results.deepseek = true
    } catch (error) {
      this.logger.warn('DeepSeek健康检查失败', { error })
    }

    return results
  }
}
