import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: 'OCTI智能评估系统',
  description: '基于OCTI四维八极理论的智能组织评估平台',
  keywords: ['OCTI', '组织评估', '智能评估', '四维八极'],
  authors: [{ name: 'OCTI Team' }],
  viewport: 'width=device-width, initial-scale=1',
  robots: 'index, follow',
  openGraph: {
    title: 'OCTI智能评估系统',
    description: '基于OCTI四维八极理论的智能组织评估平台',
    type: 'website',
    locale: 'zh_CN',
  },
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="zh-CN" className="h-full">
      <body className={`${inter.className} h-full antialiased`}>
        <div id="root" className="min-h-full">
          {children}
        </div>
      </body>
    </html>
  )
}