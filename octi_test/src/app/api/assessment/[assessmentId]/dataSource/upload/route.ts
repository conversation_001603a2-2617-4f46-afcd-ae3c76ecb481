import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { writeFile, mkdir } from 'fs/promises'
import { join } from 'path'
import { successResponse, validationErrorResponse, serverErrorResponse } from '@/lib/api-response'

// 支持的文件类型
const ALLOWED_FILE_TYPES = ['application/pdf', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'text/plain']
const MAX_FILE_SIZE = 50 * 1024 * 1024 // 50MB

/**
 * POST /api/assessment/[assessmentId]/dataSource/upload
 * 上传外部文件用于专业版评估
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { assessmentId: string } }
) {
  try {
    const { assessmentId } = params

    // 验证assessmentId
    if (!assessmentId || assessmentId.length < 1) {
      return NextResponse.json({
        success: false,
        error: {
          code: 'INVALID_ASSESSMENT_ID',
          message: '无效的评估ID'
        }
      }, { status: 400 })
    }

    const formData = await request.formData()
    const file = formData.get('file') as File

    if (!file) {
      return NextResponse.json({
        success: false,
        error: {
          code: 'NO_FILE',
          message: '请选择要上传的文件'
        }
      }, { status: 400 })
    }

    // 验证文件类型
    if (!ALLOWED_FILE_TYPES.includes(file.type)) {
      return NextResponse.json({
        success: false,
        error: {
          code: 'INVALID_TYPE',
          message: '不支持的文件类型，仅支持 PDF、DOCX、TXT 格式'
        }
      }, { status: 400 })
    }

    // 验证文件大小
    if (file.size > MAX_FILE_SIZE) {
      return NextResponse.json({
        success: false,
        error: {
          code: 'FILE_TOO_LARGE',
          message: '文件大小超过 50MB 限制'
        }
      }, { status: 400 })
    }

    // 生成唯一文件名
    const dataSourceId = `ds-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
    const fileName = `${dataSourceId}-${file.name}`
    
    // 确保上传目录存在
    const uploadDir = join(process.cwd(), 'uploads', assessmentId)
    await mkdir(uploadDir, { recursive: true })

    // 保存文件
    const filePath = join(uploadDir, fileName)
    const bytes = await file.arrayBuffer()
    const buffer = Buffer.from(bytes)
    await writeFile(filePath, buffer)

    // 这里应该触发异步处理任务
    // 例如：解析文件内容、提取关键信息等
    // await queueFileProcessing(dataSourceId, filePath, assessmentId)

    return successResponse({
      dataSourceId,
      fileName: file.name,
      fileSize: file.size,
      fileType: file.type,
      message: '文件已接收，正在排队处理'
    }, 202)

  } catch (error) {
    console.error('文件上传失败:', error)
    return serverErrorResponse('文件上传失败')
  }
}