import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { successResponse, validationErrorResponse, serverErrorResponse } from '@/lib/api-response'

// URL验证Schema
const ScrapeSchema = z.object({
  url: z.string().url('请提供有效的URL地址')
})

/**
 * POST /api/assessment/[assessmentId]/dataSource/scrape
 * 网络数据采集
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { assessmentId: string } }
) {
  try {
    const { assessmentId } = params
    const body = await request.json()
    const validatedData = ScrapeSchema.parse(body)

    // 验证assessmentId
    if (!assessmentId || assessmentId.length < 1) {
      return NextResponse.json({
        success: false,
        error: {
          code: 'INVALID_ASSESSMENT_ID',
          message: '无效的评估ID'
        }
      }, { status: 400 })
    }

    // 生成数据源ID
    const dataSourceId = `ds-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`

    // 这里应该触发异步网络数据采集任务
    // 例如：使用Cheerio或Puppeteer抓取网页内容
    // await queueWebScraping(dataSourceId, validatedData.url, assessmentId)

    return successResponse({
      dataSourceId,
      url: validatedData.url,
      message: 'URL已接收，正在排队进行数据采集'
    }, 202)

  } catch (error) {
    if (error instanceof z.ZodError) {
      return validationErrorResponse(error)
    }

    console.error('网络数据采集请求失败:', error)
    return serverErrorResponse('数据采集请求失败')
  }
}