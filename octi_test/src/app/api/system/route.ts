import { NextRequest, NextResponse } from 'next/server'
import { ApiService } from '@/services/api/ApiService'
import { SystemApiRequest } from '@/services/api/ApiService'

let apiService: ApiService | null = null

/**
 * 获取API服务实例
 */
async function getApiService(): Promise<ApiService> {
  if (!apiService) {
    apiService = ApiService.getInstance()
    await apiService.initialize()
  }
  return apiService
}

/**
 * GET /api/system - 获取系统状态
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const action = searchParams.get('action') || 'status'

    const service = await getApiService()
    
    const apiRequest: SystemApiRequest = {
      action: action as 'health' | 'status' | 'metrics'
    }

    const result = await service.handleSystemRequest(apiRequest)
    
    if (result.success) {
      return NextResponse.json(result, { status: 200 })
    } else {
      return NextResponse.json(result, { status: 400 })
    }
  } catch (error) {
    console.error('系统API请求失败:', error)
    return NextResponse.json(
      { success: false, error: '服务器内部错误' },
      { status: 500 }
    )
  }
}