import { NextRequest, NextResponse } from 'next/server'
import { ApiService } from '@/services/api/ApiService'
import { AgentApiRequest } from '@/services/api/ApiService'

let apiService: ApiService | null = null

/**
 * 获取API服务实例
 */
async function getApiService(): Promise<ApiService> {
  if (!apiService) {
    apiService = ApiService.getInstance()
    await apiService.initialize()
  }
  return apiService
}

/**
 * GET /api/agents - 获取智能体状态或列表
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const agentName = searchParams.get('agent')
    const action = searchParams.get('action') || 'list'

    const service = await getApiService()
    
    const apiRequest: AgentApiRequest = {
      action: action as 'status' | 'list',
      agentName: agentName || undefined
    }

    const result = await service.handleAgentRequest(apiRequest)
    
    if (result.success) {
      return NextResponse.json(result, { status: 200 })
    } else {
      return NextResponse.json(result, { status: 400 })
    }
  } catch (error) {
    console.error('智能体API GET请求失败:', error)
    return NextResponse.json(
      { success: false, error: '服务器内部错误' },
      { status: 500 }
    )
  }
}

/**
 * POST /api/agents/execute - 执行智能体任务
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { agentName, input } = body

    if (!agentName || !input) {
      return NextResponse.json(
        { success: false, error: '缺少必要参数: agentName 或 input' },
        { status: 400 }
      )
    }

    const service = await getApiService()
    
    const apiRequest: AgentApiRequest = {
      action: 'execute',
      agentName,
      input
    }

    const result = await service.handleAgentRequest(apiRequest)
    
    if (result.success) {
      return NextResponse.json(result, { status: 200 })
    } else {
      return NextResponse.json(result, { status: 400 })
    }
  } catch (error) {
    console.error('智能体执行失败:', error)
    return NextResponse.json(
      { success: false, error: '服务器内部错误' },
      { status: 500 }
    )
  }
}