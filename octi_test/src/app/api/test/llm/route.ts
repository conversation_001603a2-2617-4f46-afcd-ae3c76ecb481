import { NextRequest, NextResponse } from 'next/server';
import { successResponse, serverErrorResponse } from '@/lib/api-response'

/**
 * 测试LLM API连接的接口
 * @description 验证MiniMax和DeepSeek API key是否正常工作
 */
export async function GET(request: NextRequest) {
  try {
    const results: {
      minimax: { status: string; error: string | null; response?: string };
      deepseek: { status: string; error: string | null; response?: string };
    } = {
      minimax: { status: 'unknown', error: null },
      deepseek: { status: 'unknown', error: null }
    };

    // 测试MiniMax API
    try {
      const minimaxResponse = await fetch('https://api.minimax.chat/v1/text/chatcompletion', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${process.env.MINIMAX_API_KEY}`
        },
        body: JSON.stringify({
          model: 'abab6.5s-chat',
          messages: [{
            role: 'user',
            content: '你好，这是一个API连接测试。请简单回复"连接成功"。'
          }],
          max_tokens: 50,
          temperature: 0.1
        })
      });

      if (minimaxResponse.ok) {
        const data = await minimaxResponse.json();
        results.minimax.status = 'success';
        results.minimax.response = data.choices?.[0]?.message?.content || '响应为空';
      } else {
        const errorData = await minimaxResponse.text();
        results.minimax.status = 'error';
        results.minimax.error = `HTTP ${minimaxResponse.status}: ${errorData}`;
      }
    } catch (error) {
      results.minimax.status = 'error';
      results.minimax.error = error instanceof Error ? error.message : '未知错误';
    }

    // 测试DeepSeek API
    try {
      const deepseekResponse = await fetch('https://api.deepseek.com/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${process.env.DEEPSEEK_API_KEY}`
        },
        body: JSON.stringify({
          model: 'deepseek-chat',
          messages: [{
            role: 'user',
            content: '你好，这是一个API连接测试。请简单回复"连接成功"。'
          }],
          max_tokens: 50,
          temperature: 0.1
        })
      });

      if (deepseekResponse.ok) {
        const data = await deepseekResponse.json();
        results.deepseek.status = 'success';
        results.deepseek.response = data.choices?.[0]?.message?.content || '响应为空';
      } else {
        const errorData = await deepseekResponse.text();
        results.deepseek.status = 'error';
        results.deepseek.error = `HTTP ${deepseekResponse.status}: ${errorData}`;
      }
    } catch (error) {
      results.deepseek.status = 'error';
      results.deepseek.error = error instanceof Error ? error.message : '未知错误';
    }

    // 检查环境变量
    const envCheck = {
      minimax_key_exists: !!process.env.MINIMAX_API_KEY,
      deepseek_key_exists: !!process.env.DEEPSEEK_API_KEY,
      minimax_key_length: process.env.MINIMAX_API_KEY?.length || 0,
      deepseek_key_length: process.env.DEEPSEEK_API_KEY?.length || 0
    };

    return successResponse({
      environment: envCheck,
      results
    })

  } catch (error) {
    console.error('LLM测试API错误:', error);
    return serverErrorResponse('LLM连接测试失败')
  }
}

/**
 * 简化的测试接口，只检查环境变量 - 安全版本
 */
export async function POST(request: NextRequest) {
  try {
    const envCheck = {
      minimax_configured: !!process.env.MINIMAX_API_KEY,
      deepseek_configured: !!process.env.DEEPSEEK_API_KEY,
    };

    return successResponse({
      message: 'API密钥环境变量检查完成',
      environment: envCheck
    })

  } catch (error) {
    return serverErrorResponse('环境变量检查失败')
  }
}
