import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { hash } from 'bcryptjs'
import { prisma } from '@/lib/prisma'
import { successResponse, validationErrorResponse, serverErrorResponse } from '@/lib/api-response'

// 用户注册验证Schema
const RegisterSchema = z.object({
  email: z.string().email('邮箱格式无效'),
  password: z.string().min(8, '密码至少8位').max(100, '密码不能超过100位'),
  name: z.string().min(1, '姓名不能为空').max(50, '姓名不能超过50字符'),
  organizationName: z.string().optional(),
  role: z.enum(['USER', 'ADMIN']).default('USER')
})

/**
 * POST /api/auth/register
 * 用户注册
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const validatedData = RegisterSchema.parse(body)

    // 检查邮箱是否已存在
    const existingUser = await prisma.user.findUnique({
      where: { email: validatedData.email }
    })

    if (existingUser) {
      return NextResponse.json({
        success: false,
        error: {
          code: 'EMAIL_EXISTS',
          message: '该邮箱已被注册'
        }
      }, { status: 400 })
    }

    // 加密密码
    const hashedPassword = await hash(validatedData.password, 12)

    // 创建用户
    const user = await prisma.user.create({
      data: {
        email: validatedData.email,
        password: hashedPassword,
        name: validatedData.name,
        role: validatedData.role
      },
      select: {
        id: true,
        email: true,
        name: true,
        role: true,
        createdAt: true
      }
    })

    return successResponse({
      user,
      message: '用户注册成功'
    }, 201)

  } catch (error) {
    if (error instanceof z.ZodError) {
      return validationErrorResponse(error)
    }

    console.error('用户注册失败:', error)
    return serverErrorResponse('注册失败，请稍后重试')
  }
}