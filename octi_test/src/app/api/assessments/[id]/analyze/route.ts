import { NextRequest, NextResponse } from 'next/server'
import { ApiService } from '@/services/api/ApiService'
import { AssessmentApiRequest } from '@/services/api/ApiService'

let apiService: ApiService | null = null

/**
 * 获取API服务实例
 */
async function getApiService(): Promise<ApiService> {
  if (!apiService) {
    apiService = ApiService.getInstance()
    await apiService.initialize()
  }
  return apiService
}

/**
 * POST /api/assessments/[id]/analyze - 分析评估结果
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const assessmentId = params.id

    if (!assessmentId) {
      return NextResponse.json(
        { success: false, error: '缺少评估ID' },
        { status: 400 }
      )
    }

    const service = await getApiService()
    
    const apiRequest: AssessmentApiRequest = {
      action: 'analyze',
      assessmentId
    }

    const result = await service.handleAssessmentRequest(apiRequest)
    
    if (result.success) {
      return NextResponse.json(result, { status: 200 })
    } else {
      return NextResponse.json(result, { status: 400 })
    }
  } catch (error) {
    console.error('评估分析失败:', error)
    return NextResponse.json(
      { success: false, error: '服务器内部错误' },
      { status: 500 }
    )
  }
}