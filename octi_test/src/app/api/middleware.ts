import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'

// API密钥验证Schema
const ApiKeySchema = z.string().min(32, 'API密钥格式无效')

// JWT Token验证Schema  
const AuthTokenSchema = z.object({
  sub: z.string(),
  email: z.string().email(),
  role: z.enum(['USER', 'ADMIN']),
  exp: z.number()
})

/**
 * API认证中间件
 */
export async function authMiddleware(request: NextRequest): Promise<NextResponse | null> {
  const pathname = request.nextUrl.pathname
  
  // 公开路由跳过认证
  const publicRoutes = ['/api/health', '/api/test']
  if (publicRoutes.some(route => pathname.startsWith(route))) {
    return null
  }

  // API密钥认证路由
  const apiKeyRoutes = ['/api/webhook', '/api/external']
  if (apiKeyRoutes.some(route => pathname.startsWith(route))) {
    const apiKey = request.headers.get('x-api-key')
    
    try {
      ApiKeySchema.parse(apiKey)
      // 验证API密钥有效性
      const isValid = await validateApiKey(apiKey!)
      if (!isValid) {
        return NextResponse.json(
          { error: 'Invalid API key' },
          { status: 401 }
        )
      }
    } catch (error) {
      return NextResponse.json(
        { error: 'API key required' },
        { status: 401 }
      )
    }
    
    return null
  }

  // JWT认证
  const authHeader = request.headers.get('authorization')
  if (!authHeader?.startsWith('Bearer ')) {
    return NextResponse.json(
      { error: 'Authorization header required' },
      { status: 401 }
    )
  }

  try {
    const token = authHeader.substring(7)
    const decoded = await verifyJWT(token)
    AuthTokenSchema.parse(decoded)
    
    // 将用户信息添加到请求头
    const response = NextResponse.next()
    response.headers.set('x-user-id', decoded.sub)
    response.headers.set('x-user-role', decoded.role)
    
    return null
  } catch (error) {
    return NextResponse.json(
      { error: 'Invalid token' },
      { status: 401 }
    )
  }
}

/**
 * 验证API密钥
 */
async function validateApiKey(apiKey: string): Promise<boolean> {
  // 实现API密钥验证逻辑
  const validKeys = process.env.VALID_API_KEYS?.split(',') || []
  return validKeys.includes(apiKey)
}

/**
 * 验证JWT Token
 */
async function verifyJWT(token: string): Promise<any> {
  // 实现JWT验证逻辑
  // 这里应该使用实际的JWT库如jose或jsonwebtoken
  throw new Error('JWT verification not implemented')
}