import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { AssessmentReport } from '@/types'

// 模拟报告数据存储
const mockReports: Record<string, AssessmentReport> = {
  'demo-report-1': {
    id: 'demo-report-1',
    assessmentId: 'assessment-demo-1',
    organizationName: '示例科技公司',
    version: 'standard',
    generatedAt: new Date('2024-01-15T10:30:00Z'),
    radarChart: {
      dimensions: {
        SF: 3.8, // 战略聚焦度
        IT: 4.2, // 团队协同度
        MV: 3.5, // 激励价值观
        AD: 3.9  // 适应性发展
      },
      maxScore: 5
    },
    sections: [
      {
        id: 'executive-summary',
        title: '执行摘要',
        type: 'text',
        content: '基于OCTI模型的评估结果显示，贵组织在团队协同度方面表现优秀，达到4.2分，显示出良好的内部沟通和协作能力。战略聚焦度和适应性发展也处于良好水平，分别为3.8分和3.9分。激励价值观方面还有提升空间，建议加强员工激励机制和企业文化建设。',
        order: 1
      },
      {
        id: 'strengths',
        title: '组织优势',
        type: 'list',
        content: '',
        data: [
          '团队协作机制完善，跨部门沟通顺畅',
          '组织适应性强，能够快速响应市场变化',
          '战略目标相对明确，执行力较强',
          '员工专业能力突出，学习意愿强烈'
        ],
        order: 2
      },
      {
        id: 'improvement-areas',
        title: '改进领域',
        type: 'list',
        content: '',
        data: [
          '激励机制需要进一步完善和个性化',
          '企业文化价值观传播有待加强',
          '长期战略规划的细化和落地执行',
          '创新文化氛围的营造和制度支持'
        ],
        order: 3
      },
      {
        id: 'dimension-analysis',
        title: '维度详细分析',
        type: 'table',
        content: '',
        data: {
          headers: ['维度', '得分', '水平', '关键特征'],
          rows: [
            ['战略聚焦度', '3.8', '良好', '目标明确，执行有力'],
            ['团队协同度', '4.2', '优秀', '沟通顺畅，协作高效'],
            ['激励价值观', '3.5', '一般', '制度完善，文化待强化'],
            ['适应性发展', '3.9', '良好', '变化敏感，学习积极']
          ]
        },
        order: 4
      }
    ]
  }
}

// 获取报告
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const reportId = params.id
    
    if (!reportId) {
      return NextResponse.json(
        { error: '缺少报告ID' },
        { status: 400 }
      )
    }

    // 从模拟数据中获取报告
    const report = mockReports[reportId]
    
    if (!report) {
      return NextResponse.json(
        { error: '报告不存在' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      report
    })
  } catch (error) {
    console.error('获取报告失败:', error)
    return NextResponse.json(
      { error: '服务器内部错误' },
      { status: 500 }
    )
  }
}

// 更新报告
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const reportId = params.id
    const body = await request.json()
    
    // 验证请求数据
    const updateSchema = z.object({
      organizationName: z.string().optional(),
      sections: z.array(z.object({
        id: z.string(),
        title: z.string(),
        type: z.enum(['text', 'list', 'table']),
        content: z.string(),
        data: z.any().optional(),
        order: z.number()
      })).optional()
    })
    
    const validatedData = updateSchema.parse(body)
    
    if (!mockReports[reportId]) {
      return NextResponse.json(
        { error: '报告不存在' },
        { status: 404 }
      )
    }
    
    // 更新报告
    mockReports[reportId] = {
      ...mockReports[reportId],
      ...validatedData
    }
    
    return NextResponse.json({
      success: true,
      report: mockReports[reportId]
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: '请求数据格式错误', details: error.errors },
        { status: 400 }
      )
    }
    
    console.error('更新报告失败:', error)
    return NextResponse.json(
      { error: '服务器内部错误' },
      { status: 500 }
    )
  }
}

// 删除报告
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const reportId = params.id
    
    if (!mockReports[reportId]) {
      return NextResponse.json(
        { error: '报告不存在' },
        { status: 404 }
      )
    }
    
    delete mockReports[reportId]
    
    return NextResponse.json({
      success: true,
      message: '报告已删除'
    })
  } catch (error) {
    console.error('删除报告失败:', error)
    return NextResponse.json(
      { error: '服务器内部错误' },
      { status: 500 }
    )
  }
}