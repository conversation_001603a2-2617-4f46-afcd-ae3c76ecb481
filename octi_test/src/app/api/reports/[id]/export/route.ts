import { NextRequest, NextResponse } from 'next/server'
import { AssessmentReport } from '@/types'

// 模拟报告数据存储（实际项目中应该从数据库获取）
const mockReports: Record<string, AssessmentReport> = {
  'demo-report-1': {
    id: 'demo-report-1',
    assessmentId: 'assessment-demo-1',
    organizationName: '示例科技公司',
    version: 'standard',
    generatedAt: new Date('2024-01-15T10:30:00Z'),
    radarChart: {
      dimensions: {
        SF: 3.8,
        IT: 4.2,
        MV: 3.5,
        AD: 3.9
      },
      maxScore: 5
    },
    sections: [
      {
        id: 'executive-summary',
        title: '执行摘要',
        type: 'text',
        content: '基于OCTI模型的评估结果显示，贵组织在团队协同度方面表现优秀，达到4.2分，显示出良好的内部沟通和协作能力。',
        order: 1
      }
    ]
  }
}

/**
 * 导出评估报告为PDF
 * @param request - Next.js请求对象
 * @param params - 路由参数，包含报告ID
 * @returns PDF文件流或错误响应
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const reportId = params.id
    
    if (!reportId) {
      return NextResponse.json(
        { error: '缺少报告ID' },
        { status: 400 }
      )
    }

    // 获取报告数据
    const report = mockReports[reportId]
    
    if (!report) {
      return NextResponse.json(
        { error: '报告不存在' },
        { status: 404 }
      )
    }

    // 生成PDF内容（简化版本，实际项目中应使用专业的PDF生成库）
    const pdfContent = generatePDFContent(report)
    
    // 创建PDF响应
    const response = new NextResponse(pdfContent, {
      status: 200,
      headers: {
        'Content-Type': 'application/pdf',
        'Content-Disposition': `attachment; filename="OCTI评估报告_${report.organizationName}_${formatDate(report.generatedAt)}.pdf"`,
        'Cache-Control': 'no-cache'
      }
    })
    
    return response
  } catch (error) {
    console.error('导出报告失败:', error)
    return NextResponse.json(
      { error: '导出失败，请稍后重试' },
      { status: 500 }
    )
  }
}

/**
 * 生成PDF内容（模拟实现）
 * @param report - 评估报告数据
 * @returns PDF内容的Buffer
 */
function generatePDFContent(report: AssessmentReport): Buffer {
  // 这里是一个简化的PDF生成实现
  // 实际项目中应该使用如puppeteer、jsPDF或PDFKit等专业库
  
  const pdfHeader = `%PDF-1.4
1 0 obj
<<
/Type /Catalog
/Pages 2 0 R
>>
endobj

`
  
  const pdfContent = `
OCTI 组织文化评估报告

组织名称: ${report.organizationName}
报告版本: ${report.version === 'professional' ? '专业版' : '标准版'}
生成时间: ${formatDate(report.generatedAt)}

总体评分:
`
  
  let dimensionContent = ''
  if (report.radarChart) {
    const { dimensions, maxScore } = report.radarChart
    const overallScore = (dimensions.SF + dimensions.IT + dimensions.MV + dimensions.AD) / 4
    
    dimensionContent = `
整体得分: ${overallScore.toFixed(1)} / ${maxScore}

维度详情:
- 战略聚焦度 (SF): ${dimensions.SF} / ${maxScore}
- 团队协同度 (IT): ${dimensions.IT} / ${maxScore}
- 激励价值观 (MV): ${dimensions.MV} / ${maxScore}
- 适应性发展 (AD): ${dimensions.AD} / ${maxScore}

`
  }
  
  let sectionsContent = '\n报告内容:\n'
  report.sections
    .sort((a, b) => a.order - b.order)
    .forEach(section => {
      sectionsContent += `\n${section.title}:\n${section.content}\n`
      
      if (section.type === 'list' && section.data) {
        section.data.forEach((item: string, index: number) => {
          sectionsContent += `${index + 1}. ${item}\n`
        })
      }
    })
  
  const fullContent = pdfHeader + pdfContent + dimensionContent + sectionsContent
  
  // 返回简单的文本内容作为PDF（实际应该生成真正的PDF格式）
  return Buffer.from(fullContent, 'utf-8')
}

/**
 * 格式化日期
 * @param date - 日期对象
 * @returns 格式化的日期字符串
 */
function formatDate(date: Date): string {
  return new Intl.DateTimeFormat('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  }).format(date).replace(/\//g, '-')
}

/**
 * 获取导出选项
 * @param request - Next.js请求对象
 * @returns 支持的导出格式和选项
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const reportId = params.id
    
    if (!reportId) {
      return NextResponse.json(
        { error: '缺少报告ID' },
        { status: 400 }
      )
    }

    // 检查报告是否存在
    const report = mockReports[reportId]
    
    if (!report) {
      return NextResponse.json(
        { error: '报告不存在' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      exportOptions: {
        formats: [
          {
            type: 'pdf',
            name: 'PDF报告',
            description: '完整的PDF格式报告，包含图表和详细分析',
            available: true
          },
          {
            type: 'excel',
            name: 'Excel数据',
            description: '包含原始数据和计算结果的Excel文件',
            available: false // 暂未实现
          },
          {
            type: 'word',
            name: 'Word文档',
            description: '可编辑的Word格式报告',
            available: false // 暂未实现
          }
        ],
        customization: {
          includeSections: true,
          includeCharts: true,
          includeRawData: report.version === 'professional',
          watermark: report.version === 'standard'
        }
      }
    })
  } catch (error) {
    console.error('获取导出选项失败:', error)
    return NextResponse.json(
      { error: '服务器内部错误' },
      { status: 500 }
    )
  }
}