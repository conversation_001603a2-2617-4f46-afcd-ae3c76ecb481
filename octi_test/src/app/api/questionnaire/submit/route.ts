import { NextRequest, NextResponse } from 'next/server'
import { Logger } from '@/lib/logger'
import { z } from 'zod'
import { QuestionnaireResponse, AssessmentResponse } from '@/types'

const logger = new Logger('QuestionnaireSubmitAPI')

// 问卷提交验证Schema
const submitQuestionnaireSchema = z.object({
  questionnaireId: z.string().min(1, '问卷ID不能为空'),
  organizationId: z.string().optional(),
  userId: z.string().optional(),
  responses: z.array(z.object({
    questionId: z.string(),
    answer: z.union([
      z.string(),
      z.number(),
      z.array(z.string()),
      z.object({
        value: z.union([z.string(), z.number()]),
        text: z.string().optional()
      })
    ]),
    dimension: z.string(),
    timeSpent: z.number().optional(),
    confidence: z.number().min(0).max(1).optional()
  })),
  metadata: z.object({
    startTime: z.string(),
    endTime: z.string(),
    totalTimeSpent: z.number(),
    deviceInfo: z.object({
      userAgent: z.string().optional(),
      screenResolution: z.string().optional(),
      timezone: z.string().optional()
    }).optional(),
    completionRate: z.number().min(0).max(1)
  })
})

// 保存问卷回答
export async function POST(request: NextRequest) {
  try {
    logger.info('问卷提交请求接收')
    
    // 解析请求体
    const body = await request.json()
    
    // 验证请求数据
    const validatedData = submitQuestionnaireSchema.parse(body)
    
    logger.info('请求数据验证通过', {
      questionnaireId: validatedData.questionnaireId,
      responseCount: validatedData.responses.length,
      completionRate: validatedData.metadata.completionRate
    })
    
    // 检查完成率
    if (validatedData.metadata.completionRate < 0.8) {
      logger.warn('问卷完成率过低', {
        completionRate: validatedData.metadata.completionRate
      })
    }
    
    // 构建问卷回答对象
    const questionnaireResponse: QuestionnaireResponse = {
      id: `response_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      questionnaireId: validatedData.questionnaireId,
      organizationId: validatedData.organizationId,
      userId: validatedData.userId,
      responses: validatedData.responses.map(response => ({
        questionId: response.questionId,
        answer: response.answer,
        dimension: response.dimension,
        timeSpent: response.timeSpent || 0,
        confidence: response.confidence || 1.0,
        timestamp: new Date().toISOString()
      })),
      metadata: {
        ...validatedData.metadata,
        submittedAt: new Date().toISOString(),
        ipAddress: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown',
        version: '1.0'
      },
      status: 'completed',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
    
    // TODO: 保存到数据库
    // await saveQuestionnaireResponse(questionnaireResponse)
    
    // 计算维度得分
    const dimensionScores = calculateDimensionScores(questionnaireResponse.responses)
    
    // 生成评估结果
    const assessmentResult = {
      id: `assessment_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      questionnaireResponseId: questionnaireResponse.id,
      organizationId: validatedData.organizationId,
      scores: dimensionScores,
      overallScore: Object.values(dimensionScores).reduce((sum, score) => sum + score, 0) / Object.keys(dimensionScores).length,
      insights: generateBasicInsights(dimensionScores),
      recommendations: generateBasicRecommendations(dimensionScores),
      createdAt: new Date().toISOString()
    }
    
    logger.info('问卷提交处理成功', {
      responseId: questionnaireResponse.id,
      assessmentId: assessmentResult.id,
      overallScore: assessmentResult.overallScore
    })
    
    return NextResponse.json({
      success: true,
      data: {
        response: {
          id: questionnaireResponse.id,
          status: questionnaireResponse.status,
          submittedAt: questionnaireResponse.metadata.submittedAt,
          completionRate: validatedData.metadata.completionRate
        },
        assessment: {
          id: assessmentResult.id,
          overallScore: assessmentResult.overallScore,
          dimensionScores: assessmentResult.scores,
          insights: assessmentResult.insights,
          recommendations: assessmentResult.recommendations
        }
      },
      metadata: {
        timestamp: new Date().toISOString(),
        processingTime: Date.now() - Date.now() // 实际应该记录处理时间
      }
    })
    
  } catch (error) {
    logger.error('问卷提交失败', { error })
    
    // 处理验证错误
    if (error instanceof z.ZodError) {
      return NextResponse.json({
        success: false,
        error: {
          type: 'validation_error',
          message: '请求数据格式不正确',
          details: error.errors.map(err => ({
            field: err.path.join('.'),
            message: err.message,
            code: err.code
          }))
        }
      }, { status: 400 })
    }
    
    // 处理业务逻辑错误
    if (error instanceof Error) {
      return NextResponse.json({
        success: false,
        error: {
          type: 'submission_error',
          message: error.message
        }
      }, { status: 500 })
    }
    
    // 处理未知错误
    return NextResponse.json({
      success: false,
      error: {
        type: 'unknown_error',
        message: '提交过程中发生未知错误'
      }
    }, { status: 500 })
  }
}

// 获取问卷回答状态
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const responseId = searchParams.get('responseId')
    const questionnaireId = searchParams.get('questionnaireId')
    const organizationId = searchParams.get('organizationId')
    
    if (!responseId && !questionnaireId) {
      return NextResponse.json({
        success: false,
        error: {
          type: 'validation_error',
          message: '需要提供回答ID或问卷ID'
        }
      }, { status: 400 })
    }
    
    // TODO: 从数据库查询
    // const responses = await getQuestionnaireResponses({ responseId, questionnaireId, organizationId })
    
    // 模拟返回数据
    const mockResponse = {
      id: responseId || 'mock_response_id',
      questionnaireId: questionnaireId || 'mock_questionnaire_id',
      status: 'completed',
      completionRate: 1.0,
      submittedAt: new Date().toISOString(),
      totalTimeSpent: 1800 // 30分钟
    }
    
    return NextResponse.json({
      success: true,
      data: {
        response: mockResponse
      }
    })
    
  } catch (error) {
    logger.error('获取问卷回答状态失败', { error })
    
    return NextResponse.json({
      success: false,
      error: {
        type: 'retrieval_error',
        message: error instanceof Error ? error.message : '获取回答状态失败'
      }
    }, { status: 500 })
  }
}

// 辅助函数：计算维度得分
function calculateDimensionScores(responses: AssessmentResponse[]): Record<string, number> {
  const dimensionScores: Record<string, number[]> = {}
  
  responses.forEach(response => {
    if (!dimensionScores[response.dimension]) {
      dimensionScores[response.dimension] = []
    }
    
    // 简单的得分计算逻辑
    let score = 0
    if (typeof response.answer === 'number') {
      score = response.answer
    } else if (typeof response.answer === 'string') {
      // 根据选项计算得分（简化版）
      const optionScores: Record<string, number> = {
        'A': 1, 'B': 2, 'C': 3, 'D': 4, 'E': 5,
        '完全不同意': 1, '不同意': 2, '中立': 3, '同意': 4, '完全同意': 5
      }
      score = optionScores[response.answer] || 3
    }
    
    dimensionScores[response.dimension].push(score)
  })
  
  // 计算每个维度的平均分
  const averageScores: Record<string, number> = {}
  Object.entries(dimensionScores).forEach(([dimension, scores]) => {
    averageScores[dimension] = scores.reduce((sum, score) => sum + score, 0) / scores.length
  })
  
  return averageScores
}

// 辅助函数：生成基础洞察
function generateBasicInsights(dimensionScores: Record<string, number>): string[] {
  const insights: string[] = []
  
  Object.entries(dimensionScores).forEach(([dimension, score]) => {
    if (score >= 4) {
      insights.push(`${dimension}维度表现优秀，得分${score.toFixed(1)}`)
    } else if (score <= 2) {
      insights.push(`${dimension}维度需要重点关注，得分${score.toFixed(1)}`)
    }
  })
  
  return insights
}

// 辅助函数：生成基础建议
function generateBasicRecommendations(dimensionScores: Record<string, number>): string[] {
  const recommendations: string[] = []
  
  Object.entries(dimensionScores).forEach(([dimension, score]) => {
    if (score <= 2.5) {
      recommendations.push(`建议加强${dimension}相关的培训和改进措施`)
    }
  })
  
  if (recommendations.length === 0) {
    recommendations.push('整体表现良好，建议继续保持并寻求进一步优化机会')
  }
  
  return recommendations
}