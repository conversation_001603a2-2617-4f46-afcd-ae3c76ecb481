import { NextRequest, NextResponse } from 'next/server';
import { Logger } from '@/lib/logger';
import { QuestionDesignerAgent } from '@/services/agents/QuestionDesignerAgent';
import { LLMApiClient } from '@/services/llm/llm-api-client';
import { PromptBuilder } from '@/services/llm/prompt-builder';
import { DataFusionEngine } from '@/services/data/data-fusion-engine';
import { z } from 'zod';

const logger = new Logger('QuestionnaireAPI');

// 请求验证Schema
const generateQuestionnaireSchema = z.object({
  version: z.enum(['standard', 'professional']).default('standard'),
  organizationType: z.string().optional(),
  industryContext: z.string().optional(),
  targetAudience: z.string().optional(),
  customRequirements: z.string().optional(),
  questionCount: z.number().min(10).max(100).optional(),
  focusDimensions: z.array(z.string()).optional(),
  externalData: z.array(z.object({
    source: z.string(),
    content: z.string(),
    type: z.string(),
    weight: z.number().min(0).max(1).optional()
  })).optional(),
  dataFusion: z.object({
    enabled: z.boolean(),
    strategy: z.enum(['weighted', 'priority', 'consensus']).default('weighted'),
    maxDataLength: z.number().default(10000),
    minConfidence: z.number().min(0).max(1).default(0.7),
    weightThreshold: z.number().min(0).max(1).default(0.1),
    qualityThreshold: z.number().min(0).max(1).default(0.8),
    dataSources: z.array(z.object({
      id: z.string(),
      type: z.enum(['file', 'url', 'database', 'api']),
      source: z.string(),
      weight: z.number().min(0).max(1),
      processingMethod: z.enum(['summary', 'extraction', 'analysis', 'classification']),
      enabled: z.boolean().default(true)
    })).default([])
  }).optional()
});

// 初始化服务
let questionDesignerAgent: QuestionDesignerAgent | null = null;

function initializeServices() {
  if (!questionDesignerAgent) {
    const llmClient = new LLMApiClient({
      minimax: {
        timeout: 45000 // 问卷生成需要更长时间
      },
      maxRetries: 2
    });

    const promptBuilder = new PromptBuilder({
      templatePath: './config/prompts/question-design',
      maxTokens: 6000, // 问卷生成需要更多token
      temperature: 0.8
    });

    const dataFusionEngine = new DataFusionEngine({
      strategy: 'consensus',
      threshold: 0.6,
      maxSources: 2
    });

    questionDesignerAgent = new QuestionDesignerAgent(
      llmClient,
      promptBuilder,
      dataFusionEngine
    );
  }
  
  return { questionDesignerAgent };
}

export async function POST(request: NextRequest) {
  try {
    logger.info('Questionnaire generation request received');
    
    // 解析请求体
    const body = await request.json();
    
    // 验证请求数据
    const validatedData = generateQuestionnaireSchema.parse(body);
    
    logger.info('Request validated', {
      version: validatedData.version,
      organizationType: validatedData.organizationType,
      hasExternalData: !!validatedData.externalData?.length
    });
    
    // 初始化服务
    const { questionDesignerAgent } = initializeServices();
    
    // 生成问卷
    const questionnaire = await questionDesignerAgent.designQuestionnaire({
      version: validatedData.version,
      organizationType: validatedData.organizationType,
      industryContext: validatedData.industryContext,
      targetAudience: validatedData.targetAudience,
      customRequirements: validatedData.customRequirements,
      questionCount: validatedData.questionCount,
      focusDimensions: validatedData.focusDimensions,
      externalData: validatedData.externalData ? validatedData.externalData.map(data => ({
        sourceId: data.source,
        content: data.content,
        contentType: data.type,
        timestamp: new Date(),
        metadata: { weight: data.weight || 1.0 }
      })) : undefined,
      dataFusion: validatedData.dataFusion ? {
         enabled: validatedData.dataFusion.enabled,
         fusionStrategy: validatedData.dataFusion.strategy,
         maxDataLength: validatedData.dataFusion.maxDataLength,
         dataSources: validatedData.dataFusion.dataSources.map(ds => ({
           type: ds.type as 'file' | 'url' | 'database',
           source: ds.source,
           weight: ds.weight,
           processingMethod: ds.processingMethod as 'summary' | 'extraction' | 'analysis'
         }))
       } : undefined
    });
    
    logger.info('Questionnaire generated successfully', {
      questionnaireId: questionnaire.id,
      questionCount: questionnaire.questions.length,
      version: questionnaire.version
    });
    
    // 返回成功响应
    return NextResponse.json({
      success: true,
      data: {
        questionnaire: {
          id: questionnaire.id,
          title: questionnaire.title,
          description: questionnaire.description,
          version: questionnaire.version,
          questions: questionnaire.questions.map(q => ({
            id: q.id,
            text: q.text,
            type: q.type,
            dimension: q.dimension,
            subdimension: q.subdimension,
            options: q.options,
            required: true,
            weight: q.weight
          })),
          metadata: {
            createdAt: new Date().toISOString(),
            estimatedTime: questionnaire.questions.length * 2, // 估算每题2分钟
            difficulty: questionnaire.version === 'professional' ? 'high' : 'medium',
            tags: [questionnaire.version, 'octi-framework']
          }
        },
        statistics: {
          totalQuestions: questionnaire.questions.length,
          dimensionDistribution: questionnaire.questions.reduce((acc, q) => {
            acc[q.dimension] = (acc[q.dimension] || 0) + 1;
            return acc;
          }, {} as Record<string, number>),
          typeDistribution: questionnaire.questions.reduce((acc, q) => {
            acc[q.type] = (acc[q.type] || 0) + 1;
            return acc;
          }, {} as Record<string, number>)
        }
      },
      metadata: {
        timestamp: new Date().toISOString(),
        version: validatedData.version,
        processingTime: Date.now() - Date.now() // 这里应该记录实际处理时间
      }
    });
    
  } catch (error) {
    logger.error('Questionnaire generation failed', { error });
    
    // 处理验证错误
    if (error instanceof z.ZodError) {
      return NextResponse.json({
        success: false,
        error: {
          type: 'validation_error',
          message: 'Invalid request data',
          details: error.errors.map(err => ({
            field: err.path.join('.'),
            message: err.message,
            code: err.code
          }))
        }
      }, { status: 400 });
    }
    
    // 处理业务逻辑错误
    if (error instanceof Error) {
      return NextResponse.json({
        success: false,
        error: {
          type: 'generation_error',
          message: error.message
        }
      }, { status: 500 });
    }
    
    // 处理未知错误
    return NextResponse.json({
      success: false,
      error: {
        type: 'unknown_error',
        message: 'An unexpected error occurred'
      }
    }, { status: 500 });
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const questionnaireId = searchParams.get('id');
    
    if (!questionnaireId) {
      return NextResponse.json({
        success: false,
        error: {
          type: 'validation_error',
          message: 'Questionnaire ID is required'
        }
      }, { status: 400 });
    }
    
    // 初始化服务
    const { questionDesignerAgent } = initializeServices();
    
    // 获取问卷（从缓存查找）
     const result = await questionDesignerAgent.getQuestionnairePreview(questionnaireId);
    
    if (!result) {
      return NextResponse.json({
        success: false,
        error: {
          type: 'not_found',
          message: 'Questionnaire not found'
        }
      }, { status: 404 });
    }
    
    return NextResponse.json({
       success: true,
       data: {
         questionnaire: {
           id: questionnaireId,
           title: result.title,
           description: result.description,
           questionCount: result.questionCount,
           estimatedTime: result.estimatedTime,
           dimensions: result.dimensions,
         },
         metadata: {
           createdAt: new Date().toISOString(),
           estimatedTime: result.estimatedTime,
           difficulty: 'medium',
           tags: ['octi-framework']
         },
       },
     });
    
  } catch (error) {
    logger.error('Failed to retrieve questionnaire', { error });
    
    return NextResponse.json({
      success: false,
      error: {
        type: 'retrieval_error',
        message: error instanceof Error ? error.message : 'Failed to retrieve questionnaire'
      }
    }, { status: 500 });
  }
}

// 获取问卷统计信息
export async function OPTIONS(request: NextRequest) {
  try {
    // 初始化服务
    const { questionDesignerAgent } = initializeServices();
    
    // 获取缓存统计
     const cacheStats = questionDesignerAgent.getStats();
    
    return NextResponse.json({
      success: true,
      data: {
        cacheStats,
        supportedVersions: ['standard', 'professional'],
        supportedQuestionTypes: ['single_choice', 'multiple_choice', 'scale', 'text'],
        maxQuestionCount: 100,
        minQuestionCount: 10,
        supportedDimensions: [
          'organizational_culture',
          'leadership_effectiveness', 
          'team_dynamics',
          'innovation_capability'
        ]
      }
    });
    
  } catch (error) {
    logger.error('Failed to get questionnaire options', { error });
    
    return NextResponse.json({
      success: false,
      error: {
        type: 'options_error',
        message: 'Failed to retrieve options'
      }
    }, { status: 500 });
  }
}
