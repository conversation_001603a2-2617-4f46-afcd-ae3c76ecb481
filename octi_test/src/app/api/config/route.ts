import { NextRequest, NextResponse } from 'next/server'
import { ApiService } from '@/services/api/ApiService'
import { ConfigApiRequest } from '@/services/api/ApiService'

let apiService: ApiService | null = null

/**
 * 获取API服务实例
 */
async function getApiService(): Promise<ApiService> {
  if (!apiService) {
    apiService = ApiService.getInstance()
    await apiService.initialize()
  }
  return apiService
}

/**
 * GET /api/config - 获取配置
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const key = searchParams.get('key')
    const batch = searchParams.get('batch')

    const service = await getApiService()
    
    let apiRequest: ConfigApiRequest
    
    if (batch) {
      // 批量获取
      const keys = batch.split(',')
      apiRequest = {
        action: 'batch_get',
        batch: keys
      }
    } else {
      // 单个获取或获取所有
      apiRequest = {
        action: 'get',
        key: key || undefined
      }
    }

    const result = await service.handleConfigRequest(apiRequest)
    
    if (result.success) {
      return NextResponse.json(result, { status: 200 })
    } else {
      return NextResponse.json(result, { status: 400 })
    }
  } catch (error) {
    console.error('配置API GET请求失败:', error)
    return NextResponse.json(
      { success: false, error: '服务器内部错误' },
      { status: 500 }
    )
  }
}

/**
 * POST /api/config - 设置配置
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { action, key, value, batch } = body

    if (!action) {
      return NextResponse.json(
        { success: false, error: '缺少必要参数: action' },
        { status: 400 }
      )
    }

    const service = await getApiService()
    
    const apiRequest: ConfigApiRequest = {
      action,
      key,
      value,
      batch
    }

    const result = await service.handleConfigRequest(apiRequest)
    
    if (result.success) {
      return NextResponse.json(result, { status: 200 })
    } else {
      return NextResponse.json(result, { status: 400 })
    }
  } catch (error) {
    console.error('配置API POST请求失败:', error)
    return NextResponse.json(
      { success: false, error: '服务器内部错误' },
      { status: 500 }
    )
  }
}

/**
 * PUT /api/config/reload - 重新加载配置
 */
export async function PUT(request: NextRequest) {
  try {
    const service = await getApiService()
    
    const apiRequest: ConfigApiRequest = {
      action: 'reload'
    }

    const result = await service.handleConfigRequest(apiRequest)
    
    if (result.success) {
      return NextResponse.json(result, { status: 200 })
    } else {
      return NextResponse.json(result, { status: 400 })
    }
  } catch (error) {
    console.error('配置重新加载失败:', error)
    return NextResponse.json(
      { success: false, error: '服务器内部错误' },
      { status: 500 }
    )
  }
}