'use client'

import React, { useState, useEffect } from 'react'
import { useSearchParams } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { AssessmentReport } from '@/components/reports/AssessmentReport'
import { type AssessmentReport as AssessmentReportType } from '@/types'

interface ReportPageState {
  report: AssessmentReportType | null
  loading: boolean
  error: string | null
}

export default function ReportPage() {
  const searchParams = useSearchParams()
  const reportId = searchParams.get('id')
  
  const [state, setState] = useState<ReportPageState>({
    report: null,
    loading: true,
    error: null
  })

  useEffect(() => {
    if (!reportId) {
      setState(prev => ({
        ...prev,
        loading: false,
        error: '缺少报告ID参数'
      }))
      return
    }

    loadReport(reportId)
  }, [reportId])

  const loadReport = async (id: string) => {
    try {
      setState(prev => ({ ...prev, loading: true, error: null }))
      
      const response = await fetch(`/api/reports/${id}`)
      
      if (!response.ok) {
        throw new Error(`获取报告失败: ${response.status}`)
      }
      
      const data = await response.json()
      
      setState(prev => ({
        ...prev,
        report: data.report,
        loading: false
      }))
    } catch (error) {
      console.error('加载报告失败:', error)
      setState(prev => ({
        ...prev,
        loading: false,
        error: error instanceof Error ? error.message : '加载报告时发生未知错误'
      }))
    }
  }

  const handleExport = async () => {
    if (!state.report) return
    
    try {
      const response = await fetch(`/api/reports/${reportId}/export`, {
        method: 'POST'
      })
      
      if (!response.ok) {
        throw new Error('导出失败')
      }
      
      const blob = await response.blob()
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.style.display = 'none'
      a.href = url
      a.download = `OCTI评估报告_${state.report.organizationName}_${new Date().toISOString().split('T')[0]}.pdf`
      document.body.appendChild(a)
      a.click()
      window.URL.revokeObjectURL(url)
      document.body.removeChild(a)
    } catch (error) {
      console.error('导出报告失败:', error)
      alert('导出报告失败，请稍后重试')
    }
  }

  const handleShare = async () => {
    if (!state.report) return
    
    const shareUrl = `${window.location.origin}/report?id=${reportId}`
    
    if (navigator.share) {
      try {
        await navigator.share({
          title: `OCTI评估报告 - ${state.report.organizationName}`,
          text: '查看我们的组织文化评估报告',
          url: shareUrl
        })
      } catch (error) {
        console.error('分享失败:', error)
      }
    } else {
      // 降级到复制链接
      try {
        await navigator.clipboard.writeText(shareUrl)
        alert('报告链接已复制到剪贴板')
      } catch (error) {
        console.error('复制链接失败:', error)
        alert('分享功能不可用')
      }
    }
  }

  const handleReturnToQuestionnaire = () => {
    window.location.href = '/questionnaire'
  }

  if (state.loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardContent className="pt-6">
            <div className="flex flex-col items-center space-y-4">
              <div className="w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
              <p className="text-gray-600">正在加载报告...</p>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  if (state.error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle className="text-red-600">加载失败</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-600 mb-4">{state.error}</p>
            <div className="flex space-x-2">
              <Button 
                onClick={() => reportId && loadReport(reportId)}
                variant="outline"
                className="flex-1"
              >
                重试
              </Button>
              <Button 
                onClick={handleReturnToQuestionnaire}
                className="flex-1"
              >
                返回问卷
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  if (!state.report) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle>报告不存在</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-600 mb-4">未找到指定的评估报告</p>
            <Button 
              onClick={handleReturnToQuestionnaire}
              className="w-full"
            >
              返回问卷
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          {/* 页面头部 */}
          <div className="mb-6">
            <Button 
              variant="outline" 
              onClick={handleReturnToQuestionnaire}
              className="mb-4"
            >
              ← 返回问卷
            </Button>
          </div>

          {/* 报告内容 */}
          <AssessmentReport 
            report={state.report}
            onExport={handleExport}
            onShare={handleShare}
          />

          {/* 底部操作 */}
          <div className="mt-8 flex justify-center space-x-4">
            <Button 
              variant="outline"
              onClick={handleReturnToQuestionnaire}
            >
              重新评估
            </Button>
            <Button onClick={handleExport}>
              下载报告
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}