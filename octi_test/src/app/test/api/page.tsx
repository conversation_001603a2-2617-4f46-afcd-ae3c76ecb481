'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/Button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card';

interface TestResult {
  success: boolean;
  timestamp: string;
  environment: {
    minimax_key_exists: boolean;
    deepseek_key_exists: boolean;
    minimax_key_length: number;
    deepseek_key_length: number;
  };
  results: {
    minimax: {
      status: string;
      error: string | null;
      response?: string;
    };
    deepseek: {
      status: string;
      error: string | null;
      response?: string;
    };
  };
}

interface EnvCheckResult {
  success: boolean;
  message: string;
  environment: {
    minimax_key_exists: boolean;
    deepseek_key_exists: boolean;
    minimax_key_preview: string;
    deepseek_key_preview: string;
  };
  timestamp: string;
}

/**
 * API测试页面组件
 * @description 用于测试LLM API连接状态的页面
 */
export default function ApiTestPage() {
  const [envResult, setEnvResult] = useState<EnvCheckResult | null>(null);
  const [testResult, setTestResult] = useState<TestResult | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  /**
   * 测试环境变量配置
   */
  const testEnvironment = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await fetch('/api/test/llm', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        }
      });
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
      const data = await response.json();
      setEnvResult(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : '未知错误');
    } finally {
      setLoading(false);
    }
  };

  /**
   * 测试LLM API连接
   */
  const testLLMConnection = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await fetch('/api/test/llm', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        }
      });
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
      const data = await response.json();
      setTestResult(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : '未知错误');
    } finally {
      setLoading(false);
    }
  };

  /**
   * 获取状态文本
   */
  const getStatusText = (status: string) => {
    switch (status) {
      case 'success':
        return '✅ 成功';
      case 'error':
        return '❌ 失败';
      default:
        return '⏳ 未知';
    }
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="text-center space-y-2">
        <h1 className="text-3xl font-bold">OCTI API 测试中心</h1>
        <p className="text-muted-foreground">
          测试LLM API密钥配置和连接状态
        </p>
      </div>

      {/* 操作按钮 */}
      <div className="flex gap-4 justify-center">
        <Button 
          onClick={testEnvironment} 
          disabled={loading}
          variant="outline"
        >
          {loading ? '🔄 ' : ''}测试环境变量
        </Button>
        
        <Button 
          onClick={testLLMConnection} 
          disabled={loading}
        >
          {loading ? '🔄 ' : ''}测试LLM连接
        </Button>
      </div>

      {/* 错误信息 */}
      {error && (
        <Card className="border-red-200 bg-red-50">
          <CardHeader>
            <CardTitle className="text-red-700">
              ❌ 测试失败
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-red-600">{error}</p>
          </CardContent>
        </Card>
      )}

      {/* 环境变量测试结果 */}
      {envResult && (
        <Card>
          <CardHeader>
            <CardTitle>
              ✅ 环境变量检查结果
            </CardTitle>
            <CardDescription>
              检查时间: {new Date(envResult.timestamp).toLocaleString()}
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <h4 className="font-medium">MiniMax API</h4>
                <div className="text-sm">
                  {envResult.environment.minimax_key_exists ? '✅ 已配置' : '❌ 未配置'}
                </div>
                <p className="text-xs text-muted-foreground">
                  预览: {envResult.environment.minimax_key_preview}
                </p>
              </div>
              
              <div className="space-y-2">
                <h4 className="font-medium">DeepSeek API</h4>
                <div className="text-sm">
                  {envResult.environment.deepseek_key_exists ? '✅ 已配置' : '❌ 未配置'}
                </div>
                <p className="text-xs text-muted-foreground">
                  预览: {envResult.environment.deepseek_key_preview}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* LLM连接测试结果 */}
      {testResult && (
        <Card>
          <CardHeader>
            <CardTitle>
              {testResult.success ? '✅' : '❌'} LLM API 连接测试结果
            </CardTitle>
            <CardDescription>
              测试时间: {new Date(testResult.timestamp).toLocaleString()}
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* 环境信息 */}
            <div className="grid grid-cols-2 gap-4 p-4 bg-gray-50 rounded-lg">
              <div>
                <h4 className="font-medium text-sm">MiniMax密钥长度</h4>
                <p className="text-lg font-mono">{testResult.environment.minimax_key_length}</p>
              </div>
              <div>
                <h4 className="font-medium text-sm">DeepSeek密钥长度</h4>
                <p className="text-lg font-mono">{testResult.environment.deepseek_key_length}</p>
              </div>
            </div>

            {/* API测试结果 */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* MiniMax结果 */}
              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-lg">
                    MiniMax API - {getStatusText(testResult.results.minimax.status)}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {testResult.results.minimax.error ? (
                    <div className="text-red-600 text-sm">
                      <strong>错误:</strong> {testResult.results.minimax.error}
                    </div>
                  ) : (
                    <div className="text-green-600 text-sm">
                      <strong>响应:</strong> {testResult.results.minimax.response || '连接成功'}
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* DeepSeek结果 */}
              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-lg">
                    DeepSeek API - {getStatusText(testResult.results.deepseek.status)}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {testResult.results.deepseek.error ? (
                    <div className="text-red-600 text-sm">
                      <strong>错误:</strong> {testResult.results.deepseek.error}
                    </div>
                  ) : (
                    <div className="text-green-600 text-sm">
                      <strong>响应:</strong> {testResult.results.deepseek.response || '连接成功'}
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
          </CardContent>
        </Card>
      )}

      {/* 使用说明 */}
      <Card>
        <CardHeader>
          <CardTitle>使用说明</CardTitle>
        </CardHeader>
        <CardContent className="space-y-2 text-sm text-muted-foreground">
          <p>• <strong>测试环境变量</strong>: 检查API密钥是否正确配置在环境变量中</p>
          <p>• <strong>测试LLM连接</strong>: 实际调用LLM API验证连接和响应</p>
          <p>• 确保.env文件中的MINIMAX_API_KEY和DEEPSEEK_API_KEY已正确配置</p>
          <p>• 测试成功后即可开始使用OCTI智能评估系统的LLM功能</p>
        </CardContent>
      </Card>
    </div>
  );
}