'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/Button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card';
import { Input } from '@/components/ui/Input';
// 移除lucide-react图标导入

interface ApiResponse {
  success: boolean;
  data?: any;
  error?: any;
}

interface QuestionnaireRequest {
  organizationType: string;
  industryContext: string;
  focusDimensions: string[];
  questionCount: number;
  version: string;
}

interface AssessmentRequest {
  organizationName: string;
  organizationType: string;
  industryContext: string;
  responses: Array<{
    questionId: string;
    score: number;
    dimension: string;
    subdimension: string;
  }>;
}

export default function TestPage() {
  const [activeTab, setActiveTab] = useState('questionnaire');
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<ApiResponse | null>(null);
  
  // 问卷生成表单状态
  const [questionnaireForm, setQuestionnaireForm] = useState<QuestionnaireRequest>({
    organizationType: 'startup',
    industryContext: 'technology',
    focusDimensions: ['leadership', 'innovation'],
    questionCount: 20,
    version: 'standard'
  });
  
  // 评估表单状态
  const [assessmentForm, setAssessmentForm] = useState<AssessmentRequest>({
    organizationName: '测试公司',
    organizationType: 'startup',
    industryContext: 'technology',
    responses: [
      { questionId: 'q1', score: 4, dimension: 'leadership', subdimension: 'vision' },
      { questionId: 'q2', score: 3, dimension: 'innovation', subdimension: 'creativity' },
      { questionId: 'q3', score: 5, dimension: 'execution', subdimension: 'efficiency' }
    ]
  });

  const handleQuestionnaireGenerate = async () => {
    setLoading(true);
    setResult(null);
    
    try {
      const response = await fetch('/api/questionnaire/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(questionnaireForm),
      });
      
      const data = await response.json();
      setResult(data);
    } catch (error) {
      setResult({
        success: false,
        error: { message: error instanceof Error ? error.message : '未知错误' }
      });
    } finally {
      setLoading(false);
    }
  };

  const handleAssessmentEvaluate = async () => {
    setLoading(true);
    setResult(null);
    
    try {
      const response = await fetch('/api/assessment/evaluate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...assessmentForm,
          evaluationOptions: {
            version: 'standard',
            includeRecommendations: true,
            includeBenchmarking: false,
            customFocus: [],
            dataFusion: {
              enabled: true,
              fusionStrategy: 'weighted',
              maxDataLength: 10000,
              dataSources: []
            }
          }
        }),
      });
      
      const data = await response.json();
      setResult(data);
    } catch (error) {
      setResult({
        success: false,
        error: { message: error instanceof Error ? error.message : '未知错误' }
      });
    } finally {
      setLoading(false);
    }
  };

  const handleGetStats = async (endpoint: string) => {
    setLoading(true);
    setResult(null);
    
    try {
      const response = await fetch(endpoint, {
        method: 'OPTIONS',
      });
      
      const data = await response.json();
      setResult(data);
    } catch (error) {
      setResult({
        success: false,
        error: { message: error instanceof Error ? error.message : '未知错误' }
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container mx-auto p-6 max-w-6xl">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">OCTI API 测试页面</h1>
        <p className="text-muted-foreground">
          测试问卷生成和组织评估API的功能
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 左侧：API测试表单 */}
        <div className="space-y-6">
          <div className="w-full">
            <div className="grid w-full grid-cols-2 mb-4">
              <button 
                className={`px-4 py-2 text-sm font-medium rounded-l-md border ${activeTab === 'questionnaire' ? 'bg-primary text-primary-foreground' : 'bg-background text-foreground border-input'}`}
                onClick={() => setActiveTab('questionnaire')}
              >
                问卷生成
              </button>
              <button 
                className={`px-4 py-2 text-sm font-medium rounded-r-md border ${activeTab === 'assessment' ? 'bg-primary text-primary-foreground' : 'bg-background text-foreground border-input'}`}
                onClick={() => setActiveTab('assessment')}
              >
                组织评估
              </button>
            </div>
            
            {activeTab === 'questionnaire' && (
              <div className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>问卷生成 API</CardTitle>
                  <CardDescription>
                    测试 /api/questionnaire/generate 端点
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label htmlFor="orgType" className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">组织类型</label>
                      <select
                        value={questionnaireForm.organizationType}
                        onChange={(e) => 
                          setQuestionnaireForm(prev => ({ ...prev, organizationType: e.target.value }))
                        }
                        className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                      >
                        <option value="startup">初创公司</option>
                        <option value="sme">中小企业</option>
                        <option value="enterprise">大型企业</option>
                        <option value="nonprofit">非营利组织</option>
                      </select>
                    </div>
                    
                    <div>
                      <label htmlFor="industry" className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">行业背景</label>
                      <select
                        value={questionnaireForm.industryContext}
                        onChange={(e) => 
                          setQuestionnaireForm(prev => ({ ...prev, industryContext: e.target.value }))
                        }
                        className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                      >
                        <option value="technology">科技</option>
                        <option value="finance">金融</option>
                        <option value="healthcare">医疗</option>
                        <option value="education">教育</option>
                        <option value="manufacturing">制造业</option>
                      </select>
                    </div>
                  </div>
                  
                  <div>
                    <label htmlFor="questionCount" className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">问题数量</label>
                    <Input
                      id="questionCount"
                      type="number"
                      min="10"
                      max="50"
                      value={questionnaireForm.questionCount}
                      onChange={(e) => 
                        setQuestionnaireForm(prev => ({ 
                          ...prev, 
                          questionCount: parseInt(e.target.value) || 20 
                        }))
                      }
                    />
                  </div>
                  
                  <div>
                    <label className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">关注维度</label>
                    <div className="flex flex-wrap gap-2 mt-2">
                      {['leadership', 'innovation', 'execution', 'culture', 'strategy'].map(dim => (
                        <span
                          key={dim}
                          className={`inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors cursor-pointer ${
                            questionnaireForm.focusDimensions.includes(dim)
                              ? 'border-transparent bg-primary text-primary-foreground hover:bg-primary/80'
                              : 'text-foreground border-input bg-background hover:bg-accent hover:text-accent-foreground'
                          }`}
                          onClick={() => {
                            setQuestionnaireForm(prev => ({
                              ...prev,
                              focusDimensions: prev.focusDimensions.includes(dim)
                                ? prev.focusDimensions.filter(d => d !== dim)
                                : [...prev.focusDimensions, dim]
                            }));
                          }}
                        >
                          {dim}
                        </span>
                      ))}
                    </div>
                  </div>
                  
                  <div className="flex gap-2">
                    <Button 
                      onClick={handleQuestionnaireGenerate} 
                      disabled={loading}
                      className="flex-1"
                    >
                      {loading && <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />}
                      生成问卷
                    </Button>
                    <Button 
                      variant="outline" 
                      onClick={() => handleGetStats('/api/questionnaire/generate')}
                      disabled={loading}
                    >
                      获取统计
                    </Button>
                  </div>
                </CardContent>
              </Card>
              </div>
            )}
            
            {activeTab === 'assessment' && (
              <div className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>组织评估 API</CardTitle>
                  <CardDescription>
                    测试 /api/assessment/evaluate 端点
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <label htmlFor="orgName" className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">组织名称</label>
                    <Input
                      id="orgName"
                      value={assessmentForm.organizationName}
                      onChange={(e) => 
                        setAssessmentForm(prev => ({ ...prev, organizationName: e.target.value }))
                      }
                    />
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">组织类型</label>
                      <select
                        value={assessmentForm.organizationType}
                        onChange={(e) => 
                          setAssessmentForm(prev => ({ ...prev, organizationType: e.target.value }))
                        }
                        className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                      >
                        <option value="startup">初创公司</option>
                        <option value="sme">中小企业</option>
                        <option value="enterprise">大型企业</option>
                      </select>
                    </div>
                    
                    <div>
                      <label className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">行业背景</label>
                      <select
                        value={assessmentForm.industryContext}
                        onChange={(e) => 
                          setAssessmentForm(prev => ({ ...prev, industryContext: e.target.value }))
                        }
                        className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                      >
                        <option value="technology">科技</option>
                        <option value="finance">金融</option>
                        <option value="healthcare">医疗</option>
                      </select>
                    </div>
                  </div>
                  
                  <div>
                    <label className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">评估响应数据 (JSON)</label>
                    <textarea
                      value={JSON.stringify(assessmentForm.responses, null, 2)}
                      onChange={(e) => {
                        try {
                          const responses = JSON.parse(e.target.value);
                          setAssessmentForm(prev => ({ ...prev, responses }));
                        } catch {}
                      }}
                      rows={6}
                      className="flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 font-mono"
                    />
                  </div>
                  
                  <div className="flex gap-2">
                    <Button 
                      onClick={handleAssessmentEvaluate} 
                      disabled={loading}
                      className="flex-1"
                    >
                      {loading && <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />}
                      生成评估
                    </Button>
                    <Button 
                      variant="outline" 
                      onClick={() => handleGetStats('/api/assessment/evaluate')}
                      disabled={loading}
                    >
                      获取统计
                    </Button>
                  </div>
                </CardContent>
              </Card>
              </div>
            )}
          </div>
        </div>
        
        {/* 右侧：API响应结果 */}
        <div>
          <Card className="h-fit">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                API 响应结果
                {result && (
                  result.success ? (
                    <svg className="h-5 w-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  ) : (
                    <svg className="h-5 w-5 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  )
                )}
              </CardTitle>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="flex items-center justify-center py-8">
                  <div className="h-8 w-8 animate-spin rounded-full border-2 border-current border-t-transparent" />
                  <span className="ml-2">处理中...</span>
                </div>
              ) : result ? (
                <div className="space-y-4">
                  {result.success ? (
                    <div className="rounded-lg border border-green-200 bg-green-50 p-4">
                       <div className="flex items-center">
                         <svg className="h-4 w-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                           <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                         </svg>
                         <span className="ml-2 text-sm text-green-800">
                           API 调用成功！
                         </span>
                       </div>
                     </div>
                  ) : (
                    <div className="rounded-lg border border-red-200 bg-red-50 p-4">
                       <div className="flex items-center">
                         <svg className="h-4 w-4 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                           <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
                         </svg>
                         <span className="ml-2 text-sm text-red-800">
                           API 调用失败：{result.error?.message || '未知错误'}
                         </span>
                       </div>
                     </div>
                  )}
                  
                  <div>
                    <label className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">响应数据</label>
                    <pre className="mt-2 p-4 bg-muted rounded-lg text-sm overflow-auto max-h-96">
                      {JSON.stringify(result, null, 2)}
                    </pre>
                  </div>
                </div>
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  选择一个API端点并点击按钮来测试
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}