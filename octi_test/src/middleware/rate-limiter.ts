/**
 * API限流中间件
 * 基于Redis或内存的请求频率限制
 */

// 内存存储（开发环境使用）
const memoryStore = new Map<string, { count: number; resetTime: number }>();

// Redis客户端接口（为将来扩展预留）
interface RedisClient {
  incr(key: string): Promise<number>;
  expire(key: string, seconds: number): Promise<number>;
  get(key: string): Promise<string | null>;
  keys(pattern: string): Promise<string[]>;
  del(...keys: string[]): Promise<number>;
  pipeline(): any;
}

// Redis客户端（生产环境使用）
let redisClient: RedisClient | null = null;

// 初始化Redis连接（暂时禁用，使用内存存储）
// if (process.env.REDIS_URL) {
//   try {
//     redisClient = new Redis(process.env.REDIS_URL);
//   } catch (error) {
//     console.warn('Redis connection failed, falling back to memory store:', error);
//   }
// }

export interface RateLimitConfig {
  key: string;           // 限流键（通常是IP+路径）
  limit: number;         // 限制次数
  window: number;        // 时间窗口（秒）
  skipSuccessfulRequests?: boolean; // 是否跳过成功请求
  skipFailedRequests?: boolean;     // 是否跳过失败请求
}

export interface RateLimitResult {
  allowed: boolean;      // 是否允许请求
  remaining: number;     // 剩余请求次数
  resetTime: number;     // 重置时间戳
  totalHits: number;     // 总请求次数
}

/**
 * 限流检查函数
 */
export async function rateLimit(config: RateLimitConfig): Promise<boolean> {
  const result = await checkRateLimit(config);
  return result.allowed;
}

/**
 * 详细限流检查
 */
export async function checkRateLimit(config: RateLimitConfig): Promise<RateLimitResult> {
  const { key, limit, window } = config;
  const now = Date.now();
  const windowStart = Math.floor(now / (window * 1000)) * (window * 1000);
  const resetTime = windowStart + (window * 1000);
  
  try {
    if (redisClient) {
      return await checkRateLimitRedis(key, limit, window, now, resetTime);
    } else {
      return checkRateLimitMemory(key, limit, window, now, resetTime);
    }
  } catch (error) {
    console.error('Rate limit check failed:', error);
    // 限流检查失败时允许请求通过
    return {
      allowed: true,
      remaining: limit - 1,
      resetTime,
      totalHits: 1
    };
  }
}

/**
 * Redis限流检查
 */
async function checkRateLimitRedis(
  key: string,
  limit: number,
  window: number,
  now: number,
  resetTime: number
): Promise<RateLimitResult> {
  const redisKey = `rate_limit:${key}:${Math.floor(now / (window * 1000))}`;
  
  // 使用Redis管道执行原子操作
  const pipeline = redisClient!.pipeline();
  pipeline.incr(redisKey);
  pipeline.expire(redisKey, window);
  
  const results = await pipeline.exec();
  const count = results?.[0]?.[1] as number || 0;
  
  const allowed = count <= limit;
  const remaining = Math.max(0, limit - count);
  
  return {
    allowed,
    remaining,
    resetTime,
    totalHits: count
  };
}

/**
 * 内存限流检查
 */
function checkRateLimitMemory(
  key: string,
  limit: number,
  window: number,
  now: number,
  resetTime: number
): RateLimitResult {
  const windowKey = `${key}:${Math.floor(now / (window * 1000))}`;
  
  // 清理过期的键
  cleanupExpiredKeys(now);
  
  // 获取或创建计数器
  let record = memoryStore.get(windowKey);
  if (!record) {
    record = { count: 0, resetTime };
    memoryStore.set(windowKey, record);
  }
  
  // 增加计数
  record.count++;
  
  const allowed = record.count <= limit;
  const remaining = Math.max(0, limit - record.count);
  
  return {
    allowed,
    remaining,
    resetTime: record.resetTime,
    totalHits: record.count
  };
}

/**
 * 清理过期的内存键
 */
function cleanupExpiredKeys(now: number): void {
  const entries = Array.from(memoryStore.entries());
  for (const [key, record] of entries) {
    if (now > record.resetTime) {
      memoryStore.delete(key);
    }
  }
}

/**
 * 预设限流配置
 */
export const RATE_LIMIT_PRESETS = {
  // 严格限制（登录、注册等敏感操作）
  STRICT: {
    limit: 5,
    window: 60 // 1分钟5次
  },
  
  // 标准限制（一般API）
  STANDARD: {
    limit: 30,
    window: 60 // 1分钟30次
  },
  
  // 宽松限制（读取操作）
  RELAXED: {
    limit: 100,
    window: 60 // 1分钟100次
  },
  
  // 评估API专用
  ASSESSMENT: {
    limit: 10,
    window: 60 // 1分钟10次
  },
  
  // 管理API专用
  ADMIN: {
    limit: 50,
    window: 60 // 1分钟50次
  }
};

/**
 * 获取客户端标识符
 */
export function getClientIdentifier(
  ip: string,
  userAgent?: string,
  userId?: string
): string {
  if (userId) {
    return `user:${userId}`;
  }
  
  // 使用IP和User-Agent的组合作为标识符
  const uaHash = userAgent ? 
    Buffer.from(userAgent).toString('base64').slice(0, 8) : 'unknown';
  
  return `ip:${ip}:${uaHash}`;
}

/**
 * 限流中间件工厂函数
 */
export function createRateLimitMiddleware(
  defaultConfig: Partial<RateLimitConfig> = {}
) {
  return async function rateLimitMiddleware(
    key: string,
    customConfig: Partial<RateLimitConfig> = {}
  ): Promise<RateLimitResult> {
    const config = {
      key,
      limit: 30,
      window: 60,
      ...defaultConfig,
      ...customConfig
    };
    
    return await checkRateLimit(config);
  };
}

/**
 * 重置限流计数器
 */
export async function resetRateLimit(key: string): Promise<void> {
  try {
    if (redisClient) {
      const pattern = `rate_limit:${key}:*`;
      const keys = await redisClient.keys(pattern);
      if (keys.length > 0) {
        await redisClient.del(...keys);
      }
    } else {
      // 清理内存中的相关键
      const keys = Array.from(memoryStore.keys());
      for (const memKey of keys) {
        if (memKey.startsWith(key)) {
          memoryStore.delete(memKey);
        }
      }
    }
  } catch (error) {
    console.error('Failed to reset rate limit:', error);
  }
}

/**
 * 获取限流状态
 */
export async function getRateLimitStatus(
  key: string,
  window: number
): Promise<{ count: number; resetTime: number } | null> {
  const now = Date.now();
  const windowStart = Math.floor(now / (window * 1000)) * (window * 1000);
  const resetTime = windowStart + (window * 1000);
  
  try {
    if (redisClient) {
      const redisKey = `rate_limit:${key}:${Math.floor(now / (window * 1000))}`;
      const count = await redisClient.get(redisKey);
      return {
        count: parseInt(count || '0', 10),
        resetTime
      };
    } else {
      const windowKey = `${key}:${Math.floor(now / (window * 1000))}`;
      const record = memoryStore.get(windowKey);
      return record ? {
        count: record.count,
        resetTime: record.resetTime
      } : { count: 0, resetTime };
    }
  } catch (error) {
    console.error('Failed to get rate limit status:', error);
    return null;
  }
}

export default rateLimit;