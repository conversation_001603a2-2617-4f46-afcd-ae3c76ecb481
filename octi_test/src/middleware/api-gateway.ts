/**
 * API网关中间件
 * 提供统一的请求路由、错误处理、日志记录和安全控制
 */

import { NextRequest, NextResponse } from 'next/server';
import { rateLimit } from './rate-limiter';
import { logger } from '@/lib/logger';
import { validateApiKey } from '@/lib/auth/api-key';

// JWT token interface (simplified)
interface JWTToken {
  sub?: string;
  email?: string;
  role?: string;
  iat?: number;
  exp?: number;
}

// Simplified token getter (replace with actual NextAuth implementation)
async function getToken(options: { req: NextRequest }): Promise<JWTToken | null> {
  try {
    const authHeader = options.req.headers.get('authorization');
    if (!authHeader?.startsWith('Bearer ')) {
      return null;
    }
    
    // In a real implementation, you would verify the JWT token here
    // For now, return a mock token for development
    if (process.env.NODE_ENV === 'development') {
      return {
        sub: 'dev-user',
        email: '<EMAIL>',
        role: 'USER'
      };
    }
    
    return null;
  } catch (error) {
    logger.error('Token validation failed', { error });
    return null;
  }
}

// API版本配置
const API_VERSIONS = {
  v1: '/api/v1',
  v2: '/api/v2'
};

// 公开路由（无需认证）
const PUBLIC_ROUTES = [
  '/api/auth',
  '/api/health',
  '/api/public'
];

// API密钥路由（需要API密钥认证）
const API_KEY_ROUTES = [
  '/api/v1/webhook',
  '/api/v1/external'
];

// 管理员路由（需要管理员权限）
const ADMIN_ROUTES = [
  '/api/v1/admin',
  '/api/v1/config'
];

export interface ApiGatewayConfig {
  enableRateLimit: boolean;
  enableLogging: boolean;
  enableCors: boolean;
  corsOrigins: string[];
  defaultVersion: string;
}

const defaultConfig: ApiGatewayConfig = {
  enableRateLimit: true,
  enableLogging: true,
  enableCors: true,
  corsOrigins: [
    'http://localhost:3000',
    'https://octi-assessment.vercel.app'
  ],
  defaultVersion: 'v1'
};

/**
 * API网关中间件主函数
 */
export async function apiGateway(
  request: NextRequest,
  config: Partial<ApiGatewayConfig> = {}
): Promise<NextResponse> {
  const finalConfig = { ...defaultConfig, ...config };
  const startTime = Date.now();
  const requestId = generateRequestId();
  
  try {
    // 1. 请求预处理
    const preprocessedRequest = await preprocessRequest(request, requestId);
    
    // 2. CORS处理
    if (finalConfig.enableCors) {
      const corsResponse = handleCors(request, finalConfig.corsOrigins);
      if (corsResponse) return corsResponse;
    }
    
    // 3. API版本处理
    const versionedRequest = handleApiVersioning(preprocessedRequest);
    
    // 4. 限流检查
    if (finalConfig.enableRateLimit) {
      const rateLimitResponse = await handleRateLimit(versionedRequest);
      if (rateLimitResponse) return rateLimitResponse;
    }
    
    // 5. 认证和授权
    const authResponse = await handleAuthentication(versionedRequest);
    if (authResponse) return authResponse;
    
    // 6. 请求日志记录
    if (finalConfig.enableLogging) {
      logRequest(versionedRequest, requestId);
    }
    
    // 7. 继续处理请求
    const response = NextResponse.next();
    
    // 8. 响应后处理
    return await postprocessResponse(response, request, requestId, startTime);
    
  } catch (error) {
    // 错误处理
    return handleError(error, request, requestId, startTime);
  }
}

/**
 * 请求预处理
 */
async function preprocessRequest(
  request: NextRequest,
  requestId: string
): Promise<NextRequest> {
  // 添加请求ID到头部
  const headers = new Headers(request.headers);
  headers.set('x-request-id', requestId);
  headers.set('x-forwarded-for', getClientIp(request));
  
  return new NextRequest(request.url, {
    method: request.method,
    headers,
    body: request.body
  });
}

/**
 * CORS处理
 */
function handleCors(
  request: NextRequest,
  allowedOrigins: string[]
): NextResponse | null {
  const origin = request.headers.get('origin');
  
  // 预检请求处理
  if (request.method === 'OPTIONS') {
    const response = new NextResponse(null, { status: 200 });
    
    if (origin && allowedOrigins.includes(origin)) {
      response.headers.set('Access-Control-Allow-Origin', origin);
    }
    
    response.headers.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    response.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-API-Key');
    response.headers.set('Access-Control-Max-Age', '86400');
    
    return response;
  }
  
  return null;
}

/**
 * API版本处理
 */
function handleApiVersioning(request: NextRequest): NextRequest {
  const url = new URL(request.url);
  const pathname = url.pathname;
  
  // 如果已经包含版本号，直接返回
  if (pathname.startsWith('/api/v')) {
    return request;
  }
  
  // 如果是基础API路径，添加默认版本
  if (pathname.startsWith('/api/') && !pathname.startsWith('/api/auth') && !pathname.startsWith('/api/health')) {
    const versionedPath = pathname.replace('/api/', `/api/${defaultConfig.defaultVersion}/`);
    url.pathname = versionedPath;
    
    return new NextRequest(url.toString(), {
      method: request.method,
      headers: request.headers,
      body: request.body
    });
  }
  
  return request;
}

/**
 * 限流处理
 */
async function handleRateLimit(request: NextRequest): Promise<NextResponse | null> {
  try {
    const clientIp = getClientIp(request);
    const pathname = new URL(request.url).pathname;
    
    // 根据路径确定限流策略
    const limitConfig = getRateLimitConfig(pathname);
    
    const isAllowed = await rateLimit({
      key: `${clientIp}:${pathname}`,
      limit: limitConfig.limit,
      window: limitConfig.window
    });
    
    if (!isAllowed) {
      return new NextResponse(
        JSON.stringify({
          error: 'Rate limit exceeded',
          message: 'Too many requests, please try again later'
        }),
        {
          status: 429,
          headers: {
            'Content-Type': 'application/json',
            'Retry-After': limitConfig.window.toString()
          }
        }
      );
    }
    
    return null;
  } catch (error) {
    logger.error('Rate limit check failed', { error, url: request.url });
    // 限流检查失败时允许请求通过，但记录错误
    return null;
  }
}

/**
 * 认证和授权处理
 */
async function handleAuthentication(request: NextRequest): Promise<NextResponse | null> {
  const pathname = new URL(request.url).pathname;
  
  // 公开路由跳过认证
  if (isPublicRoute(pathname)) {
    return null;
  }
  
  // API密钥路由
  if (isApiKeyRoute(pathname)) {
    const apiKey = request.headers.get('x-api-key');
    if (!apiKey || !await validateApiKey(apiKey)) {
      return new NextResponse(
        JSON.stringify({
          error: 'Invalid API key',
          message: 'Valid API key required'
        }),
        {
          status: 401,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }
    return null;
  }
  
  // JWT认证
  const token = await getToken({ req: request });
  if (!token) {
    return new NextResponse(
      JSON.stringify({
        error: 'Unauthorized',
        message: 'Authentication required'
      }),
      {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }
  
  // 管理员路由权限检查
  if (isAdminRoute(pathname) && token.role !== 'ADMIN') {
    return new NextResponse(
      JSON.stringify({
        error: 'Forbidden',
        message: 'Admin access required'
      }),
      {
        status: 403,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }
  
  return null;
}

/**
 * 响应后处理
 */
async function postprocessResponse(
  response: NextResponse,
  request: NextRequest,
  requestId: string,
  startTime: number
): Promise<NextResponse> {
  const duration = Date.now() - startTime;
  const origin = request.headers.get('origin');
  
  // 添加安全头
  response.headers.set('X-Request-ID', requestId);
  response.headers.set('X-Response-Time', `${duration}ms`);
  response.headers.set('X-Content-Type-Options', 'nosniff');
  response.headers.set('X-Frame-Options', 'DENY');
  response.headers.set('X-XSS-Protection', '1; mode=block');
  
  // 添加CORS头
  if (origin && defaultConfig.corsOrigins.includes(origin)) {
    response.headers.set('Access-Control-Allow-Origin', origin);
    response.headers.set('Access-Control-Allow-Credentials', 'true');
  }
  
  // 记录响应日志
  logResponse(request, response, requestId, duration);
  
  return response;
}

/**
 * 错误处理
 */
function handleError(
  error: any,
  request: NextRequest,
  requestId: string,
  startTime: number
): NextResponse {
  const duration = Date.now() - startTime;
  
  logger.error('API Gateway error', {
    error: error.message,
    stack: error.stack,
    url: request.url,
    method: request.method,
    requestId,
    duration
  });
  
  return new NextResponse(
    JSON.stringify({
      error: 'Internal Server Error',
      message: 'An unexpected error occurred',
      requestId
    }),
    {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
        'X-Request-ID': requestId
      }
    }
  );
}

/**
 * 工具函数
 */
function generateRequestId(): string {
  return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

function getClientIp(request: NextRequest): string {
  return (
    request.headers.get('x-forwarded-for')?.split(',')[0] ||
    request.headers.get('x-real-ip') ||
    request.ip ||
    'unknown'
  );
}

function isPublicRoute(pathname: string): boolean {
  return PUBLIC_ROUTES.some(route => pathname.startsWith(route));
}

function isApiKeyRoute(pathname: string): boolean {
  return API_KEY_ROUTES.some(route => pathname.startsWith(route));
}

function isAdminRoute(pathname: string): boolean {
  return ADMIN_ROUTES.some(route => pathname.startsWith(route));
}

function getRateLimitConfig(pathname: string): { limit: number; window: number } {
  // 根据不同路径返回不同的限流配置
  if (pathname.startsWith('/api/v1/assessment')) {
    return { limit: 10, window: 60 }; // 评估API：每分钟10次
  }
  
  if (pathname.startsWith('/api/v1/admin')) {
    return { limit: 20, window: 60 }; // 管理API：每分钟20次
  }
  
  if (pathname.startsWith('/api/auth')) {
    return { limit: 5, window: 60 }; // 认证API：每分钟5次
  }
  
  // 默认限流配置
  return { limit: 30, window: 60 }; // 每分钟30次
}

function logRequest(request: NextRequest, requestId: string): void {
  const url = new URL(request.url);
  
  logger.info('API Request', {
    requestId,
    method: request.method,
    pathname: url.pathname,
    query: Object.fromEntries(url.searchParams),
    userAgent: request.headers.get('user-agent'),
    ip: getClientIp(request),
    timestamp: new Date().toISOString()
  });
}

function logResponse(
  request: NextRequest,
  response: NextResponse,
  requestId: string,
  duration: number
): void {
  const url = new URL(request.url);
  
  logger.info('API Response', {
    requestId,
    method: request.method,
    pathname: url.pathname,
    status: response.status,
    duration,
    timestamp: new Date().toISOString()
  });
}

export default apiGateway;