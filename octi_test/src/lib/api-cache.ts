import { NextRequest, NextResponse } from 'next/server'

interface CacheEntry<T> {
  data: T
  timestamp: number
  ttl: number
}

/**
 * API缓存管理器
 */
export class ApiCache {
  private cache = new Map<string, CacheEntry<any>>()
  private defaultTTL = 5 * 60 * 1000 // 5分钟

  /**
   * 生成缓存键
   */
  private generateKey(request: NextRequest): string {
    const url = new URL(request.url)
    const method = request.method
    const pathname = url.pathname
    const searchParams = url.searchParams.toString()
    
    return `${method}:${pathname}${searchParams ? `?${searchParams}` : ''}`
  }

  /**
   * 获取缓存
   */
  get<T>(request: NextRequest): T | null {
    const key = this.generateKey(request)
    const entry = this.cache.get(key)
    
    if (!entry) return null
    
    // 检查是否过期
    if (Date.now() - entry.timestamp > entry.ttl) {
      this.cache.delete(key)
      return null
    }
    
    return entry.data
  }

  /**
   * 设置缓存
   */
  set<T>(request: NextRequest, data: T, ttl?: number): void {
    const key = this.generateKey(request)
    const entry: CacheEntry<T> = {
      data,
      timestamp: Date.now(),
      ttl: ttl || this.defaultTTL
    }
    
    this.cache.set(key, entry)
  }

  /**
   * 删除缓存
   */
  delete(request: NextRequest): boolean {
    const key = this.generateKey(request)
    return this.cache.delete(key)
  }

  /**
   * 清空缓存
   */
  clear(): void {
    this.cache.clear()
  }

  /**
   * 获取缓存统计
   */
  getStats() {
    return {
      size: this.cache.size,
      keys: Array.from(this.cache.keys())
    }
  }
}

// 全局缓存实例
export const apiCache = new ApiCache()

/**
 * 缓存中间件
 */
export function withCache(ttl?: number) {
  return function(handler: Function) {
    return async function(request: NextRequest, ...args: any[]) {
      // 只缓存GET请求
      if (request.method !== 'GET') {
        return handler(request, ...args)
      }

      // 检查缓存
      const cached = apiCache.get(request)
      if (cached) {
        return NextResponse.json(cached)
      }

      // 执行处理器
      const response = await handler(request, ...args)
      
      // 缓存响应
      if (response.ok) {
        const data = await response.json()
        apiCache.set(request, data, ttl)
        return NextResponse.json(data)
      }

      return response
    }
  }
}