/**
 * 日志服务
 */
export class Logger {
  private context: string

  constructor(context: string = 'App') {
    this.context = context
  }

  private formatMessage(level: string, message: string, meta?: any): string {
    const timestamp = new Date().toISOString()
    const metaStr = meta ? ` ${JSON.stringify(meta)}` : ''
    return `[${timestamp}] [${level.toUpperCase()}] [${this.context}] ${message}${metaStr}`
  }

  debug(message: string, meta?: any): void {
    if (process.env.LOG_LEVEL === 'debug') {
      console.debug(this.formatMessage('debug', message, meta))
    }
  }

  info(message: string, meta?: any): void {
    console.info(this.formatMessage('info', message, meta))
  }

  warn(message: string, meta?: any): void {
    console.warn(this.formatMessage('warn', message, meta))
  }

  error(message: string, meta?: any): void {
    console.error(this.formatMessage('error', message, meta))
  }

  setContext(context: string): void {
    this.context = context
  }
}

// 创建默认日志器实例
export const logger = new Logger('OCTI');

export default logger;