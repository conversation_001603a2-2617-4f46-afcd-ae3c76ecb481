/**
 * API密钥验证模块
 * 支持API密钥的生成、验证和管理
 */

import { createHash, randomBytes } from 'crypto';
import { logger } from '@/lib/logger';

// API密钥配置
interface ApiKeyConfig {
  id: string;
  key: string;
  name: string;
  permissions: string[];
  rateLimit?: {
    limit: number;
    window: number;
  };
  isActive: boolean;
  createdAt: Date;
  lastUsedAt?: Date;
  expiresAt?: Date;
}

// 内存存储（生产环境应使用数据库）
const apiKeys = new Map<string, ApiKeyConfig>();

// 预定义的API密钥（开发环境）
if (process.env.NODE_ENV === 'development') {
  const devApiKey: ApiKeyConfig = {
    id: 'dev-key-1',
    key: hashApiKey('dev-api-key-12345'),
    name: 'Development Key',
    permissions: ['read', 'write', 'admin'],
    isActive: true,
    createdAt: new Date(),
    rateLimit: {
      limit: 100,
      window: 60
    }
  };
  apiKeys.set(devApiKey.key, devApiKey);
}

/**
 * 生成API密钥哈希
 */
function hashApiKey(key: string): string {
  return createHash('sha256').update(key).digest('hex');
}

/**
 * 生成新的API密钥
 */
export function generateApiKey(): { key: string; hashedKey: string } {
  const key = `octi_${randomBytes(32).toString('hex')}`;
  const hashedKey = hashApiKey(key);
  return { key, hashedKey };
}

/**
 * 验证API密钥
 */
export async function validateApiKey(apiKey: string): Promise<boolean> {
  try {
    const hashedKey = hashApiKey(apiKey);
    const keyConfig = apiKeys.get(hashedKey);
    
    if (!keyConfig) {
      logger.warn('Invalid API key attempted', { keyPrefix: apiKey.substring(0, 8) });
      return false;
    }
    
    // 检查密钥是否激活
    if (!keyConfig.isActive) {
      logger.warn('Inactive API key attempted', { keyId: keyConfig.id });
      return false;
    }
    
    // 检查是否过期
    if (keyConfig.expiresAt && keyConfig.expiresAt < new Date()) {
      logger.warn('Expired API key attempted', { keyId: keyConfig.id });
      return false;
    }
    
    // 更新最后使用时间
    keyConfig.lastUsedAt = new Date();
    
    logger.info('API key validated successfully', { keyId: keyConfig.id });
    return true;
    
  } catch (error) {
    logger.error('API key validation error', { error });
    return false;
  }
}

/**
 * 获取API密钥配置
 */
export async function getApiKeyConfig(apiKey: string): Promise<ApiKeyConfig | null> {
  try {
    const hashedKey = hashApiKey(apiKey);
    const keyConfig = apiKeys.get(hashedKey);
    
    if (!keyConfig || !keyConfig.isActive) {
      return null;
    }
    
    return keyConfig;
  } catch (error) {
    logger.error('Failed to get API key config', { error });
    return null;
  }
}

/**
 * 检查API密钥权限
 */
export async function checkApiKeyPermission(
  apiKey: string,
  permission: string
): Promise<boolean> {
  try {
    const config = await getApiKeyConfig(apiKey);
    
    if (!config) {
      return false;
    }
    
    return config.permissions.includes(permission) || config.permissions.includes('admin');
  } catch (error) {
    logger.error('Failed to check API key permission', { error, permission });
    return false;
  }
}

/**
 * 创建新的API密钥
 */
export async function createApiKey(
  name: string,
  permissions: string[],
  options: {
    rateLimit?: { limit: number; window: number };
    expiresAt?: Date;
  } = {}
): Promise<{ id: string; key: string } | null> {
  try {
    const { key, hashedKey } = generateApiKey();
    const id = `key_${randomBytes(8).toString('hex')}`;
    
    const config: ApiKeyConfig = {
      id,
      key: hashedKey,
      name,
      permissions,
      isActive: true,
      createdAt: new Date(),
      rateLimit: options.rateLimit,
      expiresAt: options.expiresAt
    };
    
    apiKeys.set(hashedKey, config);
    
    logger.info('API key created', { keyId: id, name, permissions });
    
    return { id, key };
  } catch (error) {
    logger.error('Failed to create API key', { error, name });
    return null;
  }
}

/**
 * 撤销API密钥
 */
export async function revokeApiKey(keyId: string): Promise<boolean> {
  try {
    const entries = Array.from(apiKeys.entries());
    for (const [hashedKey, config] of entries) {
      if (config.id === keyId) {
        config.isActive = false;
        logger.info('API key revoked', { keyId });
        return true;
      }
    }
    
    logger.warn('API key not found for revocation', { keyId });
    return false;
  } catch (error) {
    logger.error('Failed to revoke API key', { error, keyId });
    return false;
  }
}

/**
 * 列出所有API密钥（不包含实际密钥）
 */
export async function listApiKeys(): Promise<Omit<ApiKeyConfig, 'key'>[]> {
  try {
    const keys: Omit<ApiKeyConfig, 'key'>[] = [];
    const values = Array.from(apiKeys.values());
    
    for (const config of values) {
      const { key, ...keyInfo } = config;
      keys.push(keyInfo);
    }
    
    return keys;
  } catch (error) {
    logger.error('Failed to list API keys', { error });
    return [];
  }
}

/**
 * 获取API密钥使用统计
 */
export async function getApiKeyStats(keyId: string): Promise<{
  id: string;
  name: string;
  isActive: boolean;
  createdAt: Date;
  lastUsedAt?: Date;
  usageCount: number;
} | null> {
  try {
    const values = Array.from(apiKeys.values());
    for (const config of values) {
      if (config.id === keyId) {
        return {
          id: config.id,
          name: config.name,
          isActive: config.isActive,
          createdAt: config.createdAt,
          lastUsedAt: config.lastUsedAt,
          usageCount: 0 // 在实际项目中，这应该从使用日志中计算
        };
      }
    }
    
    return null;
  } catch (error) {
    logger.error('Failed to get API key stats', { error, keyId });
    return null;
  }
}

/**
 * 清理过期的API密钥
 */
export async function cleanupExpiredApiKeys(): Promise<number> {
  try {
    let cleanedCount = 0;
    const now = new Date();
    const entries = Array.from(apiKeys.entries());
    
    for (const [hashedKey, config] of entries) {
      if (config.expiresAt && config.expiresAt < now) {
        apiKeys.delete(hashedKey);
        cleanedCount++;
        logger.info('Expired API key cleaned up', { keyId: config.id });
      }
    }
    
    if (cleanedCount > 0) {
      logger.info('API key cleanup completed', { cleanedCount });
    }
    
    return cleanedCount;
  } catch (error) {
    logger.error('Failed to cleanup expired API keys', { error });
    return 0;
  }
}

// 定期清理过期密钥（每小时执行一次）
if (typeof window === 'undefined') { // 仅在服务器端运行
  setInterval(cleanupExpiredApiKeys, 60 * 60 * 1000);
}

export default {
  validateApiKey,
  getApiKeyConfig,
  checkApiKeyPermission,
  createApiKey,
  revokeApiKey,
  listApiKeys,
  getApiKeyStats,
  cleanupExpiredApiKeys
};