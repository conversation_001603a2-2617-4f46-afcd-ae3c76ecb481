/**
 * 用户认证系统
 * 支持多种认证方式：邮箱密码、OAuth、SSO、MFA
 */

import { createHash, randomBytes } from 'crypto';
import { logger } from '@/lib/logger';

// 用户接口
export interface User {
  id: string;
  email: string;
  name: string;
  role: 'USER' | 'ADMIN' | 'MODERATOR';
  isActive: boolean;
  emailVerified: boolean;
  mfaEnabled: boolean;
  createdAt: Date;
  lastLoginAt?: Date;
  loginAttempts: number;
  lockedUntil?: Date;
}

// 认证会话
export interface AuthSession {
  id: string;
  userId: string;
  token: string;
  refreshToken: string;
  expiresAt: Date;
  createdAt: Date;
  ipAddress: string;
  userAgent: string;
  isActive: boolean;
}

// 登录凭据
export interface LoginCredentials {
  email: string;
  password: string;
  mfaCode?: string;
}

// OAuth提供商
export interface OAuthProvider {
  id: string;
  name: string;
  clientId: string;
  clientSecret: string;
  authUrl: string;
  tokenUrl: string;
  userInfoUrl: string;
  scope: string[];
}

// 内存存储（生产环境应使用数据库）
const users = new Map<string, User>();
const sessions = new Map<string, AuthSession>();
const passwordHashes = new Map<string, string>();

// 预定义用户（开发环境）
if (process.env.NODE_ENV === 'development') {
  const devUser: User = {
    id: 'dev-user-1',
    email: '<EMAIL>',
    name: 'Development Admin',
    role: 'ADMIN',
    isActive: true,
    emailVerified: true,
    mfaEnabled: false,
    createdAt: new Date(),
    loginAttempts: 0
  };
  
  users.set(devUser.id, devUser);
  passwordHashes.set(devUser.id, hashPassword('admin123'));
}

/**
 * 密码哈希
 */
function hashPassword(password: string, salt?: string): string {
  const actualSalt = salt || randomBytes(16).toString('hex');
  const hash = createHash('pbkdf2')
    .update(password + actualSalt)
    .digest('hex');
  return `${actualSalt}:${hash}`;
}

/**
 * 验证密码
 */
function verifyPassword(password: string, hashedPassword: string): boolean {
  const [salt, hash] = hashedPassword.split(':');
  const testHash = createHash('pbkdf2')
    .update(password + salt)
    .digest('hex');
  return hash === testHash;
}

/**
 * 生成JWT令牌（简化版）
 */
function generateToken(userId: string): { token: string; refreshToken: string } {
  const token = `jwt_${randomBytes(32).toString('hex')}`;
  const refreshToken = `refresh_${randomBytes(32).toString('hex')}`;
  return { token, refreshToken };
}

/**
 * 用户登录
 */
export async function loginUser(
  credentials: LoginCredentials,
  clientInfo: { ipAddress: string; userAgent: string }
): Promise<{ user: User; session: AuthSession } | null> {
  try {
    // 查找用户
    const user = Array.from(users.values()).find(u => u.email === credentials.email);
    if (!user) {
      logger.warn('Login attempt with non-existent email', { email: credentials.email });
      return null;
    }
    
    // 检查账户状态
    if (!user.isActive) {
      logger.warn('Login attempt with inactive account', { userId: user.id });
      return null;
    }
    
    // 检查账户锁定
    if (user.lockedUntil && user.lockedUntil > new Date()) {
      logger.warn('Login attempt with locked account', { userId: user.id });
      return null;
    }
    
    // 验证密码
    const hashedPassword = passwordHashes.get(user.id);
    if (!hashedPassword || !verifyPassword(credentials.password, hashedPassword)) {
      // 增加登录失败次数
      user.loginAttempts++;
      if (user.loginAttempts >= 5) {
        user.lockedUntil = new Date(Date.now() + 30 * 60 * 1000); // 锁定30分钟
        logger.warn('Account locked due to too many failed attempts', { userId: user.id });
      }
      logger.warn('Login attempt with invalid password', { userId: user.id });
      return null;
    }
    
    // MFA验证
    if (user.mfaEnabled && !credentials.mfaCode) {
      logger.info('MFA code required for login', { userId: user.id });
      return null;
    }
    
    if (user.mfaEnabled && credentials.mfaCode) {
      const isValidMfa = await verifyMfaCode(user.id, credentials.mfaCode);
      if (!isValidMfa) {
        logger.warn('Login attempt with invalid MFA code', { userId: user.id });
        return null;
      }
    }
    
    // 重置登录失败次数
    user.loginAttempts = 0;
    user.lockedUntil = undefined;
    user.lastLoginAt = new Date();
    
    // 创建会话
    const { token, refreshToken } = generateToken(user.id);
    const session: AuthSession = {
      id: `session_${randomBytes(16).toString('hex')}`,
      userId: user.id,
      token,
      refreshToken,
      expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24小时
      createdAt: new Date(),
      ipAddress: clientInfo.ipAddress,
      userAgent: clientInfo.userAgent,
      isActive: true
    };
    
    sessions.set(session.token, session);
    
    logger.info('User logged in successfully', { userId: user.id });
    
    return { user, session };
    
  } catch (error) {
    logger.error('Login error', { error, email: credentials.email });
    return null;
  }
}

/**
 * 验证会话令牌
 */
export async function validateSession(token: string): Promise<{ user: User; session: AuthSession } | null> {
  try {
    const session = sessions.get(token);
    if (!session || !session.isActive) {
      return null;
    }
    
    // 检查过期时间
    if (session.expiresAt < new Date()) {
      session.isActive = false;
      logger.info('Session expired', { sessionId: session.id });
      return null;
    }
    
    // 获取用户信息
    const user = users.get(session.userId);
    if (!user || !user.isActive) {
      session.isActive = false;
      return null;
    }
    
    return { user, session };
    
  } catch (error) {
    logger.error('Session validation error', { error, token: token.substring(0, 8) });
    return null;
  }
}

/**
 * 刷新令牌
 */
export async function refreshSession(refreshToken: string): Promise<AuthSession | null> {
  try {
    // 查找会话
    const session = Array.from(sessions.values()).find(s => s.refreshToken === refreshToken);
    if (!session || !session.isActive) {
      return null;
    }
    
    // 生成新令牌
    const { token: newToken, refreshToken: newRefreshToken } = generateToken(session.userId);
    
    // 更新会话
    sessions.delete(session.token);
    session.token = newToken;
    session.refreshToken = newRefreshToken;
    session.expiresAt = new Date(Date.now() + 24 * 60 * 60 * 1000);
    
    sessions.set(newToken, session);
    
    logger.info('Session refreshed', { sessionId: session.id });
    
    return session;
    
  } catch (error) {
    logger.error('Session refresh error', { error });
    return null;
  }
}

/**
 * 用户登出
 */
export async function logoutUser(token: string): Promise<boolean> {
  try {
    const session = sessions.get(token);
    if (session) {
      session.isActive = false;
      sessions.delete(token);
      logger.info('User logged out', { sessionId: session.id });
      return true;
    }
    return false;
  } catch (error) {
    logger.error('Logout error', { error });
    return false;
  }
}

/**
 * 创建用户
 */
export async function createUser(
  userData: {
    email: string;
    password: string;
    name: string;
    role?: 'USER' | 'ADMIN' | 'MODERATOR';
  }
): Promise<User | null> {
  try {
    // 检查邮箱是否已存在
    const existingUser = Array.from(users.values()).find(u => u.email === userData.email);
    if (existingUser) {
      logger.warn('Attempt to create user with existing email', { email: userData.email });
      return null;
    }
    
    const userId = `user_${randomBytes(16).toString('hex')}`;
    const user: User = {
      id: userId,
      email: userData.email,
      name: userData.name,
      role: userData.role || 'USER',
      isActive: true,
      emailVerified: false,
      mfaEnabled: false,
      createdAt: new Date(),
      loginAttempts: 0
    };
    
    // 存储用户和密码哈希
    users.set(userId, user);
    passwordHashes.set(userId, hashPassword(userData.password));
    
    logger.info('User created successfully', { userId, email: userData.email });
    
    return user;
    
  } catch (error) {
    logger.error('User creation error', { error, email: userData.email });
    return null;
  }
}

/**
 * 验证MFA代码（占位符实现）
 */
async function verifyMfaCode(userId: string, code: string): Promise<boolean> {
  // 在实际项目中，这里应该验证TOTP或SMS代码
  // 目前返回简单的验证逻辑
  return code === '123456';
}

/**
 * 获取用户信息
 */
export async function getUserById(userId: string): Promise<User | null> {
  return users.get(userId) || null;
}

/**
 * 更新用户信息
 */
export async function updateUser(
  userId: string,
  updates: Partial<Pick<User, 'name' | 'role' | 'isActive' | 'emailVerified' | 'mfaEnabled'>>
): Promise<User | null> {
  try {
    const user = users.get(userId);
    if (!user) {
      return null;
    }
    
    Object.assign(user, updates);
    
    logger.info('User updated', { userId, updates });
    
    return user;
    
  } catch (error) {
    logger.error('User update error', { error, userId });
    return null;
  }
}

/**
 * 更改密码
 */
export async function changePassword(
  userId: string,
  currentPassword: string,
  newPassword: string
): Promise<boolean> {
  try {
    const user = users.get(userId);
    if (!user) {
      return false;
    }
    
    const currentHash = passwordHashes.get(userId);
    if (!currentHash || !verifyPassword(currentPassword, currentHash)) {
      logger.warn('Password change attempt with invalid current password', { userId });
      return false;
    }
    
    // 更新密码哈希
    passwordHashes.set(userId, hashPassword(newPassword));
    
    logger.info('Password changed successfully', { userId });
    
    return true;
    
  } catch (error) {
    logger.error('Password change error', { error, userId });
    return false;
  }
}

/**
 * 清理过期会话
 */
export async function cleanupExpiredSessions(): Promise<number> {
  try {
    let cleanedCount = 0;
    const now = new Date();
    const entries = Array.from(sessions.entries());
    
    for (const [token, session] of entries) {
      if (session.expiresAt < now || !session.isActive) {
        sessions.delete(token);
        cleanedCount++;
      }
    }
    
    if (cleanedCount > 0) {
      logger.info('Expired sessions cleaned up', { cleanedCount });
    }
    
    return cleanedCount;
    
  } catch (error) {
    logger.error('Session cleanup error', { error });
    return 0;
  }
}

// 定期清理过期会话（每小时执行一次）
if (typeof window === 'undefined') { // 仅在服务器端运行
  setInterval(cleanupExpiredSessions, 60 * 60 * 1000);
}

export default {
  loginUser,
  validateSession,
  refreshSession,
  logoutUser,
  createUser,
  getUserById,
  updateUser,
  changePassword,
  cleanupExpiredSessions
};