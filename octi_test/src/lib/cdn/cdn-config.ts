/**
 * CDN配置和管理模块
 * 提供静态资源CDN加速、缓存策略和性能优化
 */

import { logger } from '@/lib/logger';
import { recordPerformanceMetric } from '@/lib/monitoring/performance';

// CDN配置接口
export interface CDNConfig {
  provider: 'cloudflare' | 'aws' | 'azure' | 'custom';
  baseUrl: string;
  regions: string[];
  cacheRules: CacheRule[];
  compression: CompressionConfig;
  security: SecurityConfig;
  analytics: AnalyticsConfig;
}

// 缓存规则
export interface CacheRule {
  pattern: string;
  ttl: number;
  browserTtl: number;
  edgeTtl: number;
  bypassCache: boolean;
  cacheLevel: 'bypass' | 'basic' | 'simplified' | 'aggressive';
}

// 压缩配置
export interface CompressionConfig {
  enabled: boolean;
  algorithms: ('gzip' | 'brotli' | 'deflate')[];
  minSize: number;
  fileTypes: string[];
}

// 安全配置
export interface SecurityConfig {
  hotlinkProtection: boolean;
  allowedReferrers: string[];
  blockedCountries: string[];
  rateLimiting: {
    enabled: boolean;
    requestsPerMinute: number;
  };
}

// 分析配置
export interface AnalyticsConfig {
  enabled: boolean;
  trackingId?: string;
  customMetrics: string[];
}

// CDN统计
export interface CDNStats {
  requests: number;
  bandwidth: number;
  cacheHitRate: number;
  responseTime: number;
  errors: number;
  regions: Record<string, {
    requests: number;
    bandwidth: number;
    responseTime: number;
  }>;
}

// 资源类型
export type ResourceType = 'image' | 'video' | 'audio' | 'document' | 'script' | 'style' | 'font' | 'other';

/**
 * CDN管理器
 */
export class CDNManager {
  private config: CDNConfig;
  private stats: CDNStats;
  private resourceCache = new Map<string, {
    url: string;
    type: ResourceType;
    size: number;
    lastAccessed: Date;
    accessCount: number;
  }>();

  constructor(config: CDNConfig) {
    this.config = config;
    this.stats = {
      requests: 0,
      bandwidth: 0,
      cacheHitRate: 0,
      responseTime: 0,
      errors: 0,
      regions: {}
    };
  }

  /**
   * 获取CDN URL
   */
  getCDNUrl(path: string, options: {
    type?: ResourceType;
    version?: string;
    quality?: 'low' | 'medium' | 'high' | 'auto';
    format?: string;
    resize?: { width?: number; height?: number; fit?: 'cover' | 'contain' | 'fill' };
  } = {}): string {
    const { type = 'other', version, quality, format, resize } = options;
    
    // 构建基础URL
    let url = `${this.config.baseUrl}${path.startsWith('/') ? '' : '/'}${path}`;
    
    // 添加查询参数
    const params = new URLSearchParams();
    
    if (version) {
      params.set('v', version);
    }
    
    if (quality && type === 'image') {
      params.set('q', quality);
    }
    
    if (format && ['image', 'video'].includes(type)) {
      params.set('f', format);
    }
    
    if (resize && type === 'image') {
      if (resize.width) params.set('w', resize.width.toString());
      if (resize.height) params.set('h', resize.height.toString());
      if (resize.fit) params.set('fit', resize.fit);
    }
    
    if (params.toString()) {
      url += `?${params.toString()}`;
    }
    
    // 记录资源访问
    this.recordResourceAccess(path, type, url);
    
    return url;
  }

  /**
   * 预加载资源
   */
  preloadResources(resources: Array<{
    path: string;
    type: ResourceType;
    priority?: 'high' | 'medium' | 'low';
  }>): void {
    resources.forEach(resource => {
      const url = this.getCDNUrl(resource.path, { type: resource.type });
      
      if (typeof window !== 'undefined') {
        const link = document.createElement('link');
        link.rel = 'preload';
        link.href = url;
        
        // 设置资源类型
        switch (resource.type) {
          case 'image':
            link.as = 'image';
            break;
          case 'script':
            link.as = 'script';
            break;
          case 'style':
            link.as = 'style';
            break;
          case 'font':
            link.as = 'font';
            link.crossOrigin = 'anonymous';
            break;
          default:
            link.as = 'fetch';
            link.crossOrigin = 'anonymous';
        }
        
        // 设置优先级
        if (resource.priority) {
          link.setAttribute('importance', resource.priority);
        }
        
        document.head.appendChild(link);
      }
    });
    
    logger.info('Resources preloaded', { count: resources.length });
  }

  /**
   * 清除缓存
   */
  async purgeCache(paths: string[] | 'all'): Promise<boolean> {
    try {
      // 这里应该调用CDN提供商的API
      // 目前只是模拟实现
      
      if (paths === 'all') {
        logger.info('Purging all CDN cache');
      } else {
        logger.info('Purging CDN cache for paths', { paths });
      }
      
      // 模拟API调用延迟
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      recordPerformanceMetric('cdn_cache_purge', Array.isArray(paths) ? paths.length : 1, 'count', {
        provider: this.config.provider
      });
      
      return true;
    } catch (error) {
      logger.error('CDN cache purge failed', { error, paths });
      return false;
    }
  }

  /**
   * 获取缓存规则
   */
  getCacheRule(path: string): CacheRule | null {
    for (const rule of this.config.cacheRules) {
      const regex = new RegExp(rule.pattern);
      if (regex.test(path)) {
        return rule;
      }
    }
    return null;
  }

  /**
   * 优化图片URL
   */
  optimizeImageUrl(path: string, options: {
    width?: number;
    height?: number;
    quality?: number;
    format?: 'webp' | 'avif' | 'jpeg' | 'png' | 'auto';
    dpr?: number;
  } = {}): string {
    const params = new URLSearchParams();
    
    if (options.width) params.set('w', options.width.toString());
    if (options.height) params.set('h', options.height.toString());
    if (options.quality) params.set('q', options.quality.toString());
    if (options.format) params.set('f', options.format);
    if (options.dpr) params.set('dpr', options.dpr.toString());
    
    const baseUrl = this.getCDNUrl(path, { type: 'image' });
    const separator = baseUrl.includes('?') ? '&' : '?';
    
    return params.toString() ? `${baseUrl}${separator}${params.toString()}` : baseUrl;
  }

  /**
   * 生成响应式图片源集
   */
  generateSrcSet(path: string, sizes: number[], options: {
    quality?: number;
    format?: 'webp' | 'avif' | 'jpeg' | 'png' | 'auto';
  } = {}): string {
    return sizes
      .map(size => {
        const url = this.optimizeImageUrl(path, {
          width: size,
          ...options
        });
        return `${url} ${size}w`;
      })
      .join(', ');
  }

  /**
   * 获取CDN统计
   */
  getStats(): CDNStats {
    return { ...this.stats };
  }

  /**
   * 获取热门资源
   */
  getPopularResources(limit: number = 10): Array<{
    path: string;
    type: ResourceType;
    accessCount: number;
    lastAccessed: Date;
  }> {
    const resources = Array.from(this.resourceCache.entries())
      .map(([path, data]) => ({
        path,
        type: data.type,
        accessCount: data.accessCount,
        lastAccessed: data.lastAccessed
      }))
      .sort((a, b) => b.accessCount - a.accessCount)
      .slice(0, limit);
    
    return resources;
  }

  /**
   * 检查资源健康状态
   */
  async checkResourceHealth(paths: string[]): Promise<Array<{
    path: string;
    status: 'ok' | 'error' | 'slow';
    responseTime: number;
    statusCode?: number;
    error?: string;
  }>> {
    const results = await Promise.all(
      paths.map(async (path) => {
        const url = this.getCDNUrl(path);
        const startTime = Date.now();
        
        try {
          const response = await fetch(url, { method: 'HEAD' });
          const responseTime = Date.now() - startTime;
          
          const status: 'ok' | 'error' | 'slow' = response.ok 
            ? (responseTime > 2000 ? 'slow' : 'ok') 
            : 'error';
          
          return {
            path,
            status,
            responseTime,
            statusCode: response.status
          };
        } catch (error) {
          return {
            path,
            status: 'error' as const,
            responseTime: Date.now() - startTime,
            error: error instanceof Error ? error.message : 'Unknown error'
          };
        }
      })
    );
    
    return results;
  }

  /**
   * 更新配置
   */
  updateConfig(newConfig: Partial<CDNConfig>): void {
    this.config = { ...this.config, ...newConfig };
    logger.info('CDN configuration updated', { provider: this.config.provider });
  }

  // 私有方法
  private recordResourceAccess(path: string, type: ResourceType, url: string): void {
    const existing = this.resourceCache.get(path);
    
    if (existing) {
      existing.lastAccessed = new Date();
      existing.accessCount++;
    } else {
      this.resourceCache.set(path, {
        url,
        type,
        size: 0, // 实际应用中应该获取真实大小
        lastAccessed: new Date(),
        accessCount: 1
      });
    }
    
    this.stats.requests++;
    
    recordPerformanceMetric('cdn_request', 1, 'count', {
      provider: this.config.provider,
      type,
      path
    });
  }
}

/**
 * 默认CDN配置
 */
export const defaultCDNConfig: CDNConfig = {
  provider: 'cloudflare',
  baseUrl: process.env.CDN_BASE_URL || 'https://cdn.example.com',
  regions: ['us-east-1', 'eu-west-1', 'ap-southeast-1'],
  cacheRules: [
    {
      pattern: '\\.(jpg|jpeg|png|gif|webp|avif)$',
      ttl: 86400 * 30, // 30天
      browserTtl: 86400 * 7, // 7天
      edgeTtl: 86400 * 30,
      bypassCache: false,
      cacheLevel: 'aggressive'
    },
    {
      pattern: '\\.(css|js)$',
      ttl: 86400 * 7, // 7天
      browserTtl: 86400, // 1天
      edgeTtl: 86400 * 7,
      bypassCache: false,
      cacheLevel: 'basic'
    },
    {
      pattern: '\\.(woff|woff2|ttf|eot)$',
      ttl: 86400 * 365, // 1年
      browserTtl: 86400 * 30, // 30天
      edgeTtl: 86400 * 365,
      bypassCache: false,
      cacheLevel: 'aggressive'
    },
    {
      pattern: '/api/',
      ttl: 0,
      browserTtl: 0,
      edgeTtl: 0,
      bypassCache: true,
      cacheLevel: 'bypass'
    }
  ],
  compression: {
    enabled: true,
    algorithms: ['brotli', 'gzip'],
    minSize: 1024, // 1KB
    fileTypes: ['text/html', 'text/css', 'application/javascript', 'application/json']
  },
  security: {
    hotlinkProtection: true,
    allowedReferrers: [],
    blockedCountries: [],
    rateLimiting: {
      enabled: true,
      requestsPerMinute: 1000
    }
  },
  analytics: {
    enabled: true,
    customMetrics: ['bandwidth', 'cache_hit_rate', 'response_time']
  }
};

// 默认CDN实例
export const defaultCDN = new CDNManager(defaultCDNConfig);

/**
 * CDN工具函数
 */
export const CDNUtils = {
  /**
   * 检测浏览器支持的图片格式
   */
  getSupportedImageFormat(): 'avif' | 'webp' | 'jpeg' {
    if (typeof window === 'undefined') return 'jpeg';
    
    const canvas = document.createElement('canvas');
    canvas.width = 1;
    canvas.height = 1;
    
    // 检测AVIF支持
    if (canvas.toDataURL('image/avif').indexOf('data:image/avif') === 0) {
      return 'avif';
    }
    
    // 检测WebP支持
    if (canvas.toDataURL('image/webp').indexOf('data:image/webp') === 0) {
      return 'webp';
    }
    
    return 'jpeg';
  },
  
  /**
   * 获取设备像素比
   */
  getDevicePixelRatio(): number {
    return typeof window !== 'undefined' ? window.devicePixelRatio || 1 : 1;
  },
  
  /**
   * 计算最优图片尺寸
   */
  calculateOptimalSize(containerWidth: number, containerHeight: number): {
    width: number;
    height: number;
  } {
    const dpr = CDNUtils.getDevicePixelRatio();
    return {
      width: Math.ceil(containerWidth * dpr),
      height: Math.ceil(containerHeight * dpr)
    };
  }
};

export default {
  CDNManager,
  defaultCDN,
  defaultCDNConfig,
  CDNUtils
};