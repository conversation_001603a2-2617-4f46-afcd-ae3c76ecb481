/**
 * 缓存管理模块
 * 提供多层缓存策略、缓存失效和性能优化
 */

import { logger } from '@/lib/logger';
import { recordPerformanceMetric } from '@/lib/monitoring/performance';

// 缓存项接口
export interface CacheItem<T = any> {
  key: string;
  value: T;
  ttl: number; // 生存时间（秒）
  createdAt: Date;
  lastAccessed: Date;
  accessCount: number;
  tags: string[];
  metadata?: any;
}

// 缓存配置
export interface CacheConfig {
  maxSize: number;
  defaultTtl: number;
  cleanupInterval: number;
  enableMetrics: boolean;
}

// 缓存策略
export type CacheStrategy = 'LRU' | 'LFU' | 'FIFO' | 'TTL';

// 缓存层级
export type CacheLayer = 'memory' | 'redis' | 'database';

// 缓存统计
export interface CacheStats {
  hits: number;
  misses: number;
  hitRate: number;
  totalKeys: number;
  memoryUsage: number;
  evictions: number;
}

/**
 * 内存缓存管理器
 */
export class MemoryCacheManager {
  private cache = new Map<string, CacheItem>();
  private config: CacheConfig;
  private stats: CacheStats;
  private cleanupTimer?: NodeJS.Timeout;

  constructor(config: Partial<CacheConfig> = {}) {
    this.config = {
      maxSize: 1000,
      defaultTtl: 3600, // 1小时
      cleanupInterval: 300, // 5分钟
      enableMetrics: true,
      ...config
    };

    this.stats = {
      hits: 0,
      misses: 0,
      hitRate: 0,
      totalKeys: 0,
      memoryUsage: 0,
      evictions: 0
    };

    this.startCleanupTimer();
  }

  /**
   * 设置缓存项
   */
  set<T>(
    key: string,
    value: T,
    ttl: number = this.config.defaultTtl,
    tags: string[] = [],
    metadata?: any
  ): void {
    // 检查缓存大小限制
    if (this.cache.size >= this.config.maxSize && !this.cache.has(key)) {
      this.evictItems(1);
    }

    const item: CacheItem<T> = {
      key,
      value,
      ttl,
      createdAt: new Date(),
      lastAccessed: new Date(),
      accessCount: 0,
      tags,
      metadata
    };

    this.cache.set(key, item);
    this.updateStats();

    if (this.config.enableMetrics) {
      recordPerformanceMetric('cache_set', 1, 'count', { key, layer: 'memory' });
    }

    logger.debug('Cache item set', { key, ttl, tags });
  }

  /**
   * 获取缓存项
   */
  get<T>(key: string): T | null {
    const item = this.cache.get(key) as CacheItem<T> | undefined;

    if (!item) {
      this.stats.misses++;
      this.updateHitRate();
      
      if (this.config.enableMetrics) {
        recordPerformanceMetric('cache_miss', 1, 'count', { key, layer: 'memory' });
      }
      
      return null;
    }

    // 检查是否过期
    if (this.isExpired(item)) {
      this.cache.delete(key);
      this.stats.misses++;
      this.updateHitRate();
      
      if (this.config.enableMetrics) {
        recordPerformanceMetric('cache_expired', 1, 'count', { key, layer: 'memory' });
      }
      
      return null;
    }

    // 更新访问信息
    item.lastAccessed = new Date();
    item.accessCount++;
    
    this.stats.hits++;
    this.updateHitRate();
    
    if (this.config.enableMetrics) {
      recordPerformanceMetric('cache_hit', 1, 'count', { key, layer: 'memory' });
    }

    return item.value;
  }

  /**
   * 删除缓存项
   */
  delete(key: string): boolean {
    const deleted = this.cache.delete(key);
    this.updateStats();
    
    if (this.config.enableMetrics && deleted) {
      recordPerformanceMetric('cache_delete', 1, 'count', { key, layer: 'memory' });
    }
    
    return deleted;
  }

  /**
   * 检查缓存项是否存在
   */
  has(key: string): boolean {
    const item = this.cache.get(key);
    if (!item) return false;
    
    if (this.isExpired(item)) {
      this.cache.delete(key);
      return false;
    }
    
    return true;
  }

  /**
   * 清空缓存
   */
  clear(): void {
    const size = this.cache.size;
    this.cache.clear();
    this.updateStats();
    
    if (this.config.enableMetrics) {
      recordPerformanceMetric('cache_clear', size, 'count', { layer: 'memory' });
    }
    
    logger.info('Cache cleared', { itemsCleared: size });
  }

  /**
   * 根据标签删除缓存项
   */
  deleteByTags(tags: string[]): number {
    let deletedCount = 0;
    
    for (const [key, item] of Array.from(this.cache.entries())) {
      if (tags.some(tag => item.tags.includes(tag))) {
        this.cache.delete(key);
        deletedCount++;
      }
    }
    
    this.updateStats();
    
    if (this.config.enableMetrics && deletedCount > 0) {
      recordPerformanceMetric('cache_delete_by_tags', deletedCount, 'count', { 
        tags: tags.join(','), 
        layer: 'memory' 
      });
    }
    
    logger.info('Cache items deleted by tags', { tags, deletedCount });
    return deletedCount;
  }

  /**
   * 获取缓存统计
   */
  getStats(): CacheStats {
    this.updateStats();
    return { ...this.stats };
  }

  /**
   * 获取所有缓存键
   */
  getKeys(): string[] {
    return Array.from(this.cache.keys());
  }

  /**
   * 获取缓存项详情
   */
  getItemInfo(key: string): Omit<CacheItem, 'value'> | null {
    const item = this.cache.get(key);
    if (!item) return null;
    
    const { value, ...info } = item;
    return info;
  }

  /**
   * 预热缓存
   */
  async warmup<T>(
    keys: string[],
    loader: (key: string) => Promise<T>,
    ttl?: number
  ): Promise<void> {
    const promises = keys.map(async (key) => {
      try {
        if (!this.has(key)) {
          const value = await loader(key);
          this.set(key, value, ttl);
        }
      } catch (error) {
        logger.error('Cache warmup failed for key', { key, error });
      }
    });
    
    await Promise.all(promises);
    logger.info('Cache warmup completed', { keys: keys.length });
  }

  /**
   * 批量获取
   */
  mget<T>(keys: string[]): Map<string, T> {
    const result = new Map<string, T>();
    
    for (const key of keys) {
      const value = this.get<T>(key);
      if (value !== null) {
        result.set(key, value);
      }
    }
    
    return result;
  }

  /**
   * 批量设置
   */
  mset<T>(items: Array<{ key: string; value: T; ttl?: number; tags?: string[] }>): void {
    for (const item of items) {
      this.set(item.key, item.value, item.ttl, item.tags);
    }
  }

  /**
   * 销毁缓存管理器
   */
  destroy(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
    }
    this.clear();
  }

  // 私有方法
  private isExpired(item: CacheItem): boolean {
    const now = Date.now();
    const createdAt = item.createdAt.getTime();
    return (now - createdAt) / 1000 > item.ttl;
  }

  private updateStats(): void {
    this.stats.totalKeys = this.cache.size;
    this.stats.memoryUsage = this.estimateMemoryUsage();
  }

  private updateHitRate(): void {
    const total = this.stats.hits + this.stats.misses;
    this.stats.hitRate = total > 0 ? (this.stats.hits / total) * 100 : 0;
  }

  private estimateMemoryUsage(): number {
    // 简单的内存使用估算
    let size = 0;
    for (const [key, item] of this.cache) {
      size += key.length * 2; // 字符串大小估算
      size += JSON.stringify(item.value).length * 2;
      size += 200; // 元数据开销
    }
    return size;
  }

  private evictItems(count: number): void {
    const items = Array.from(this.cache.entries());
    
    // LRU策略：删除最久未访问的项
    items.sort((a, b) => a[1].lastAccessed.getTime() - b[1].lastAccessed.getTime());
    
    for (let i = 0; i < count && i < items.length; i++) {
      this.cache.delete(items[i][0]);
      this.stats.evictions++;
    }
    
    if (this.config.enableMetrics) {
      recordPerformanceMetric('cache_eviction', count, 'count', { layer: 'memory' });
    }
  }

  private startCleanupTimer(): void {
    this.cleanupTimer = setInterval(() => {
      this.cleanup();
    }, this.config.cleanupInterval * 1000);
  }

  private cleanup(): void {
    const now = Date.now();
    let expiredCount = 0;
    
    const entries = Array.from(this.cache.entries());
    for (const [key, item] of entries) {
      if (this.isExpired(item)) {
        this.cache.delete(key);
        expiredCount++;
      }
    }
    
    if (expiredCount > 0) {
      this.updateStats();
      
      if (this.config.enableMetrics) {
        recordPerformanceMetric('cache_cleanup', expiredCount, 'count', { layer: 'memory' });
      }
      
      logger.debug('Cache cleanup completed', { expiredItems: expiredCount });
    }
  }
}

/**
 * 多层缓存管理器
 */
export class MultiLayerCacheManager {
  private layers: Map<CacheLayer, MemoryCacheManager> = new Map();
  private config: Record<CacheLayer, Partial<CacheConfig>>;

  constructor(config: Partial<Record<CacheLayer, Partial<CacheConfig>>> = {}) {
    this.config = {
      memory: { maxSize: 1000, defaultTtl: 300 }, // 5分钟
      redis: { maxSize: 10000, defaultTtl: 3600 }, // 1小时
      database: { maxSize: 100000, defaultTtl: 86400 }, // 24小时
      ...config
    };

    // 初始化内存层
    this.layers.set('memory', new MemoryCacheManager(this.config.memory));
  }

  /**
   * 获取缓存项（多层查找）
   */
  async get<T>(key: string, layers: CacheLayer[] = ['memory', 'redis', 'database']): Promise<T | null> {
    for (const layer of layers) {
      const cache = this.layers.get(layer);
      if (!cache) continue;

      const value = cache.get<T>(key);
      if (value !== null) {
        // 回填到更快的缓存层
        await this.backfill(key, value, layer, layers);
        return value;
      }
    }

    return null;
  }

  /**
   * 设置缓存项（多层设置）
   */
  async set<T>(
    key: string,
    value: T,
    layers: CacheLayer[] = ['memory'],
    ttl?: number,
    tags?: string[]
  ): Promise<void> {
    for (const layer of layers) {
      const cache = this.layers.get(layer);
      if (cache) {
        const layerTtl = ttl || this.config[layer]?.defaultTtl || 3600;
        cache.set(key, value, layerTtl, tags);
      }
    }
  }

  /**
   * 删除缓存项（所有层）
   */
  async delete(key: string): Promise<void> {
    const caches = Array.from(this.layers.values());
    for (const cache of caches) {
      cache.delete(key);
    }
  }

  /**
   * 根据标签删除（所有层）
   */
  async deleteByTags(tags: string[]): Promise<number> {
    let totalDeleted = 0;
    const caches = Array.from(this.layers.values());
    for (const cache of caches) {
      totalDeleted += cache.deleteByTags(tags);
    }
    return totalDeleted;
  }

  /**
   * 获取所有层的统计信息
   */
  getStats(): Record<CacheLayer, CacheStats> {
    const stats: Partial<Record<CacheLayer, CacheStats>> = {};
    
    const entries = Array.from(this.layers.entries());
    for (const [layer, cache] of entries) {
      stats[layer] = cache.getStats();
    }
    
    return stats as Record<CacheLayer, CacheStats>;
  }

  /**
   * 清空所有层
   */
  clear(): void {
    const caches = Array.from(this.layers.values());
    for (const cache of caches) {
      cache.clear();
    }
  }

  /**
   * 销毁缓存管理器
   */
  destroy(): void {
    const caches = Array.from(this.layers.values());
    for (const cache of caches) {
      cache.destroy();
    }
    this.layers.clear();
  }

  // 私有方法
  private async backfill<T>(
    key: string,
    value: T,
    sourceLayer: CacheLayer,
    targetLayers: CacheLayer[]
  ): Promise<void> {
    const sourceIndex = targetLayers.indexOf(sourceLayer);
    if (sourceIndex <= 0) return;

    // 回填到更快的层
    const fasterLayers = targetLayers.slice(0, sourceIndex);
    for (const layer of fasterLayers) {
      const cache = this.layers.get(layer);
      if (cache) {
        const ttl = this.config[layer]?.defaultTtl || 3600;
        cache.set(key, value, ttl);
      }
    }
  }
}

// 默认缓存实例
export const defaultCache = new MemoryCacheManager();
export const multiLayerCache = new MultiLayerCacheManager();

/**
 * 缓存装饰器
 */
export function cached(
  ttl: number = 3600,
  keyGenerator?: (...args: any[]) => string
) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value;
    
    descriptor.value = async function (...args: any[]) {
      const key = keyGenerator 
        ? keyGenerator(...args)
        : `${target.constructor.name}.${propertyName}:${JSON.stringify(args)}`;
      
      // 尝试从缓存获取
      let result = defaultCache.get(key);
      if (result !== null) {
        return result;
      }
      
      // 执行原方法
      result = await method.apply(this, args);
      
      // 缓存结果
      defaultCache.set(key, result, ttl);
      
      return result;
    };
    
    return descriptor;
  };
}

export default {
  MemoryCacheManager,
  MultiLayerCacheManager,
  defaultCache,
  multiLayerCache,
  cached
};