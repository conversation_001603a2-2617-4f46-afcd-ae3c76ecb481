/**
 * 监控告警系统
 * 支持多种告警类型、通知渠道和告警规则
 */

import { logger } from '@/lib/logger';

// 告警级别
export enum AlertLevel {
  INFO = 'info',
  WARNING = 'warning',
  ERROR = 'error',
  CRITICAL = 'critical'
}

// 告警类型
export enum AlertType {
  SYSTEM_ERROR = 'system_error',
  PERFORMANCE = 'performance',
  SECURITY = 'security',
  BUSINESS = 'business',
  INFRASTRUCTURE = 'infrastructure'
}

// 通知渠道
export enum NotificationChannel {
  EMAIL = 'email',
  SLACK = 'slack',
  WEBHOOK = 'webhook',
  SMS = 'sms'
}

// 告警规则
export interface AlertRule {
  id: string;
  name: string;
  description: string;
  type: AlertType;
  level: AlertLevel;
  condition: string; // 告警条件表达式
  threshold: number;
  timeWindow: number; // 时间窗口（秒）
  isActive: boolean;
  channels: NotificationChannel[];
  recipients: string[];
  cooldownPeriod: number; // 冷却期（秒）
  createdAt: Date;
  lastTriggered?: Date;
}

// 告警事件
export interface AlertEvent {
  id: string;
  ruleId: string;
  level: AlertLevel;
  type: AlertType;
  title: string;
  message: string;
  data: any;
  timestamp: Date;
  resolved: boolean;
  resolvedAt?: Date;
  acknowledgedBy?: string;
  acknowledgedAt?: Date;
}

// 系统指标
export interface SystemMetrics {
  timestamp: Date;
  cpu: number;
  memory: number;
  disk: number;
  responseTime: number;
  errorRate: number;
  requestCount: number;
  activeUsers: number;
}

// 内存存储
const alertRules = new Map<string, AlertRule>();
const alertEvents = new Map<string, AlertEvent>();
const systemMetrics: SystemMetrics[] = [];
const metricBuffer = new Map<string, any[]>();

// 预定义告警规则
const defaultRules: Omit<AlertRule, 'id' | 'createdAt'>[] = [
  {
    name: 'High Error Rate',
    description: '错误率超过5%',
    type: AlertType.SYSTEM_ERROR,
    level: AlertLevel.ERROR,
    condition: 'errorRate > threshold',
    threshold: 5,
    timeWindow: 300, // 5分钟
    isActive: true,
    channels: [NotificationChannel.EMAIL, NotificationChannel.SLACK],
    recipients: ['<EMAIL>'],
    cooldownPeriod: 900 // 15分钟
  },
  {
    name: 'High Response Time',
    description: '响应时间超过2秒',
    type: AlertType.PERFORMANCE,
    level: AlertLevel.WARNING,
    condition: 'responseTime > threshold',
    threshold: 2000,
    timeWindow: 180, // 3分钟
    isActive: true,
    channels: [NotificationChannel.EMAIL],
    recipients: ['<EMAIL>'],
    cooldownPeriod: 600 // 10分钟
  },
  {
    name: 'High Memory Usage',
    description: '内存使用率超过85%',
    type: AlertType.INFRASTRUCTURE,
    level: AlertLevel.WARNING,
    condition: 'memory > threshold',
    threshold: 85,
    timeWindow: 300,
    isActive: true,
    channels: [NotificationChannel.EMAIL],
    recipients: ['<EMAIL>'],
    cooldownPeriod: 1800 // 30分钟
  },
  {
    name: 'Security Breach Attempt',
    description: '检测到安全威胁',
    type: AlertType.SECURITY,
    level: AlertLevel.CRITICAL,
    condition: 'securityEvents > threshold',
    threshold: 10,
    timeWindow: 60,
    isActive: true,
    channels: [NotificationChannel.EMAIL, NotificationChannel.SLACK, NotificationChannel.SMS],
    recipients: ['<EMAIL>', '<EMAIL>'],
    cooldownPeriod: 300 // 5分钟
  }
];

// 初始化默认规则
defaultRules.forEach(rule => {
  const id = `rule_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  alertRules.set(id, {
    ...rule,
    id,
    createdAt: new Date()
  });
});

/**
 * 记录系统指标
 */
export function recordMetrics(metrics: Partial<SystemMetrics>): void {
  const fullMetrics: SystemMetrics = {
    timestamp: new Date(),
    cpu: 0,
    memory: 0,
    disk: 0,
    responseTime: 0,
    errorRate: 0,
    requestCount: 0,
    activeUsers: 0,
    ...metrics
  };
  
  systemMetrics.push(fullMetrics);
  
  // 保持最近1小时的数据
  const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);
  const recentMetrics = systemMetrics.filter(m => m.timestamp > oneHourAgo);
  systemMetrics.length = 0;
  systemMetrics.push(...recentMetrics);
  
  // 检查告警规则
  checkAlertRules(fullMetrics);
}

/**
 * 检查告警规则
 */
function checkAlertRules(currentMetrics: SystemMetrics): void {
  const rules = Array.from(alertRules.values());
  
  for (const rule of rules) {
    if (!rule.isActive) continue;
    
    // 检查冷却期
    if (rule.lastTriggered) {
      const timeSinceLastTrigger = Date.now() - rule.lastTriggered.getTime();
      if (timeSinceLastTrigger < rule.cooldownPeriod * 1000) {
        continue;
      }
    }
    
    // 评估告警条件
    const shouldTrigger = evaluateAlertCondition(rule, currentMetrics);
    
    if (shouldTrigger) {
      triggerAlert(rule, currentMetrics);
    }
  }
}

/**
 * 评估告警条件
 */
function evaluateAlertCondition(rule: AlertRule, metrics: SystemMetrics): boolean {
  try {
    // 获取时间窗口内的指标
    const windowStart = new Date(Date.now() - rule.timeWindow * 1000);
    const windowMetrics = systemMetrics.filter(m => m.timestamp >= windowStart);
    
    if (windowMetrics.length === 0) return false;
    
    // 计算平均值
    const avgMetrics = {
      cpu: windowMetrics.reduce((sum, m) => sum + m.cpu, 0) / windowMetrics.length,
      memory: windowMetrics.reduce((sum, m) => sum + m.memory, 0) / windowMetrics.length,
      disk: windowMetrics.reduce((sum, m) => sum + m.disk, 0) / windowMetrics.length,
      responseTime: windowMetrics.reduce((sum, m) => sum + m.responseTime, 0) / windowMetrics.length,
      errorRate: windowMetrics.reduce((sum, m) => sum + m.errorRate, 0) / windowMetrics.length,
      requestCount: windowMetrics.reduce((sum, m) => sum + m.requestCount, 0),
      activeUsers: Math.max(...windowMetrics.map(m => m.activeUsers))
    };
    
    // 简单的条件评估
    switch (rule.condition) {
      case 'errorRate > threshold':
        return avgMetrics.errorRate > rule.threshold;
      case 'responseTime > threshold':
        return avgMetrics.responseTime > rule.threshold;
      case 'memory > threshold':
        return avgMetrics.memory > rule.threshold;
      case 'cpu > threshold':
        return avgMetrics.cpu > rule.threshold;
      case 'disk > threshold':
        return avgMetrics.disk > rule.threshold;
      default:
        return false;
    }
  } catch (error) {
    logger.error('Error evaluating alert condition', { error, ruleId: rule.id });
    return false;
  }
}

/**
 * 触发告警
 */
function triggerAlert(rule: AlertRule, metrics: SystemMetrics): void {
  const alertId = `alert_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  
  const alertEvent: AlertEvent = {
    id: alertId,
    ruleId: rule.id,
    level: rule.level,
    type: rule.type,
    title: rule.name,
    message: `${rule.description} - 当前值: ${getMetricValue(rule, metrics)}`,
    data: {
      rule,
      metrics,
      threshold: rule.threshold
    },
    timestamp: new Date(),
    resolved: false
  };
  
  alertEvents.set(alertId, alertEvent);
  
  // 更新规则的最后触发时间
  rule.lastTriggered = new Date();
  
  // 发送通知
  sendNotifications(alertEvent, rule);
  
  logger.error('Alert triggered', {
    alertId,
    ruleId: rule.id,
    level: rule.level,
    type: rule.type,
    message: alertEvent.message
  });
}

/**
 * 获取指标值
 */
function getMetricValue(rule: AlertRule, metrics: SystemMetrics): number {
  switch (rule.condition) {
    case 'errorRate > threshold':
      return metrics.errorRate;
    case 'responseTime > threshold':
      return metrics.responseTime;
    case 'memory > threshold':
      return metrics.memory;
    case 'cpu > threshold':
      return metrics.cpu;
    case 'disk > threshold':
      return metrics.disk;
    default:
      return 0;
  }
}

/**
 * 发送通知
 */
async function sendNotifications(alert: AlertEvent, rule: AlertRule): Promise<void> {
  for (const channel of rule.channels) {
    for (const recipient of rule.recipients) {
      try {
        await sendNotification(channel, recipient, alert);
      } catch (error) {
        logger.error('Failed to send notification', {
          error,
          channel,
          recipient,
          alertId: alert.id
        });
      }
    }
  }
}

/**
 * 发送单个通知
 */
async function sendNotification(
  channel: NotificationChannel,
  recipient: string,
  alert: AlertEvent
): Promise<void> {
  switch (channel) {
    case NotificationChannel.EMAIL:
      await sendEmailNotification(recipient, alert);
      break;
    case NotificationChannel.SLACK:
      await sendSlackNotification(recipient, alert);
      break;
    case NotificationChannel.WEBHOOK:
      await sendWebhookNotification(recipient, alert);
      break;
    case NotificationChannel.SMS:
      await sendSmsNotification(recipient, alert);
      break;
    default:
      logger.warn('Unknown notification channel', { channel });
  }
}

/**
 * 发送邮件通知（占位符实现）
 */
async function sendEmailNotification(email: string, alert: AlertEvent): Promise<void> {
  logger.info('Sending email notification', {
    email,
    alertId: alert.id,
    level: alert.level,
    title: alert.title
  });
  
  // 在实际项目中，这里应该集成邮件服务
  // 例如 SendGrid, AWS SES, 或 Nodemailer
}

/**
 * 发送Slack通知（占位符实现）
 */
async function sendSlackNotification(webhook: string, alert: AlertEvent): Promise<void> {
  logger.info('Sending Slack notification', {
    webhook: webhook.substring(0, 20) + '...',
    alertId: alert.id,
    level: alert.level,
    title: alert.title
  });
  
  // 在实际项目中，这里应该调用Slack Webhook API
}

/**
 * 发送Webhook通知（占位符实现）
 */
async function sendWebhookNotification(url: string, alert: AlertEvent): Promise<void> {
  logger.info('Sending webhook notification', {
    url: url.substring(0, 20) + '...',
    alertId: alert.id,
    level: alert.level,
    title: alert.title
  });
  
  // 在实际项目中，这里应该发送HTTP POST请求
}

/**
 * 发送短信通知（占位符实现）
 */
async function sendSmsNotification(phone: string, alert: AlertEvent): Promise<void> {
  logger.info('Sending SMS notification', {
    phone: phone.substring(0, 3) + '***',
    alertId: alert.id,
    level: alert.level,
    title: alert.title
  });
  
  // 在实际项目中，这里应该集成短信服务
  // 例如 Twilio, AWS SNS
}

/**
 * 创建告警规则
 */
export function createAlertRule(rule: Omit<AlertRule, 'id' | 'createdAt'>): string {
  const id = `rule_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  
  const newRule: AlertRule = {
    ...rule,
    id,
    createdAt: new Date()
  };
  
  alertRules.set(id, newRule);
  
  logger.info('Alert rule created', { ruleId: id, name: rule.name });
  
  return id;
}

/**
 * 获取告警规则
 */
export function getAlertRule(ruleId: string): AlertRule | null {
  return alertRules.get(ruleId) || null;
}

/**
 * 获取所有告警规则
 */
export function getAllAlertRules(): AlertRule[] {
  return Array.from(alertRules.values());
}

/**
 * 更新告警规则
 */
export function updateAlertRule(
  ruleId: string,
  updates: Partial<Omit<AlertRule, 'id' | 'createdAt'>>
): boolean {
  const rule = alertRules.get(ruleId);
  if (!rule) return false;
  
  Object.assign(rule, updates);
  
  logger.info('Alert rule updated', { ruleId, updates });
  
  return true;
}

/**
 * 删除告警规则
 */
export function deleteAlertRule(ruleId: string): boolean {
  const deleted = alertRules.delete(ruleId);
  
  if (deleted) {
    logger.info('Alert rule deleted', { ruleId });
  }
  
  return deleted;
}

/**
 * 获取告警事件
 */
export function getAlertEvents(
  filters: {
    level?: AlertLevel;
    type?: AlertType;
    resolved?: boolean;
    limit?: number;
  } = {}
): AlertEvent[] {
  let events = Array.from(alertEvents.values());
  
  // 应用过滤器
  if (filters.level) {
    events = events.filter(e => e.level === filters.level);
  }
  
  if (filters.type) {
    events = events.filter(e => e.type === filters.type);
  }
  
  if (filters.resolved !== undefined) {
    events = events.filter(e => e.resolved === filters.resolved);
  }
  
  // 按时间排序（最新的在前）
  events.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());
  
  // 限制数量
  if (filters.limit) {
    events = events.slice(0, filters.limit);
  }
  
  return events;
}

/**
 * 确认告警
 */
export function acknowledgeAlert(
  alertId: string,
  acknowledgedBy: string
): boolean {
  const alert = alertEvents.get(alertId);
  if (!alert) return false;
  
  alert.acknowledgedBy = acknowledgedBy;
  alert.acknowledgedAt = new Date();
  
  logger.info('Alert acknowledged', { alertId, acknowledgedBy });
  
  return true;
}

/**
 * 解决告警
 */
export function resolveAlert(alertId: string): boolean {
  const alert = alertEvents.get(alertId);
  if (!alert) return false;
  
  alert.resolved = true;
  alert.resolvedAt = new Date();
  
  logger.info('Alert resolved', { alertId });
  
  return true;
}

/**
 * 获取系统健康状态
 */
export function getSystemHealth(): {
  status: 'healthy' | 'warning' | 'critical';
  activeAlerts: number;
  criticalAlerts: number;
  lastUpdate: Date;
} {
  const activeAlerts = Array.from(alertEvents.values()).filter(a => !a.resolved);
  const criticalAlerts = activeAlerts.filter(a => a.level === AlertLevel.CRITICAL);
  
  let status: 'healthy' | 'warning' | 'critical' = 'healthy';
  
  if (criticalAlerts.length > 0) {
    status = 'critical';
  } else if (activeAlerts.length > 0) {
    status = 'warning';
  }
  
  return {
    status,
    activeAlerts: activeAlerts.length,
    criticalAlerts: criticalAlerts.length,
    lastUpdate: new Date()
  };
}

export default {
  recordMetrics,
  createAlertRule,
  getAlertRule,
  getAllAlertRules,
  updateAlertRule,
  deleteAlertRule,
  getAlertEvents,
  acknowledgeAlert,
  resolveAlert,
  getSystemHealth
};