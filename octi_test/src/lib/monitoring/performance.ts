/**
 * 性能监控模块
 * 提供应用性能监控(APM)、性能指标收集和分析
 */

import { logger } from '@/lib/logger';
import { recordMetrics } from './alert-system';

// 性能指标类型
export interface PerformanceMetric {
  id: string;
  name: string;
  value: number;
  unit: string;
  timestamp: Date;
  tags: Record<string, string>;
  metadata?: any;
}

// 请求性能数据
export interface RequestPerformance {
  id: string;
  method: string;
  path: string;
  statusCode: number;
  duration: number;
  timestamp: Date;
  userAgent?: string;
  ip?: string;
  userId?: string;
  error?: string;
}

// 数据库查询性能
export interface DatabasePerformance {
  id: string;
  query: string;
  duration: number;
  timestamp: Date;
  database: string;
  table?: string;
  operation: 'SELECT' | 'INSERT' | 'UPDATE' | 'DELETE' | 'OTHER';
  rowsAffected?: number;
  error?: string;
}

// 外部API调用性能
export interface ExternalApiPerformance {
  id: string;
  url: string;
  method: string;
  duration: number;
  statusCode: number;
  timestamp: Date;
  service: string;
  error?: string;
}

// 内存存储
const performanceMetrics: PerformanceMetric[] = [];
const requestPerformance: RequestPerformance[] = [];
const databasePerformance: DatabasePerformance[] = [];
const externalApiPerformance: ExternalApiPerformance[] = [];

// 性能阈值配置
const PERFORMANCE_THRESHOLDS = {
  REQUEST_DURATION_WARNING: 1000, // 1秒
  REQUEST_DURATION_ERROR: 3000,   // 3秒
  DATABASE_QUERY_WARNING: 500,    // 500ms
  DATABASE_QUERY_ERROR: 2000,     // 2秒
  EXTERNAL_API_WARNING: 2000,     // 2秒
  EXTERNAL_API_ERROR: 5000,       // 5秒
  MEMORY_WARNING: 80,             // 80%
  MEMORY_ERROR: 90,               // 90%
  CPU_WARNING: 70,                // 70%
  CPU_ERROR: 85                   // 85%
};

/**
 * 记录性能指标
 */
export function recordPerformanceMetric(
  name: string,
  value: number,
  unit: string,
  tags: Record<string, string> = {},
  metadata?: any
): void {
  const metric: PerformanceMetric = {
    id: generateId(),
    name,
    value,
    unit,
    timestamp: new Date(),
    tags,
    metadata
  };
  
  performanceMetrics.push(metric);
  
  // 保持最近1小时的数据
  cleanupOldMetrics();
  
  logger.debug('Performance metric recorded', {
    name,
    value,
    unit,
    tags
  });
}

/**
 * 记录请求性能
 */
export function recordRequestPerformance(
  method: string,
  path: string,
  statusCode: number,
  duration: number,
  metadata: {
    userAgent?: string;
    ip?: string;
    userId?: string;
    error?: string;
  } = {}
): void {
  const performance: RequestPerformance = {
    id: generateId(),
    method,
    path,
    statusCode,
    duration,
    timestamp: new Date(),
    ...metadata
  };
  
  requestPerformance.push(performance);
  
  // 检查性能阈值
  if (duration > PERFORMANCE_THRESHOLDS.REQUEST_DURATION_ERROR) {
    logger.error('Slow request detected', {
      method,
      path,
      duration,
      statusCode
    });
  } else if (duration > PERFORMANCE_THRESHOLDS.REQUEST_DURATION_WARNING) {
    logger.warn('Slow request detected', {
      method,
      path,
      duration,
      statusCode
    });
  }
  
  // 记录到监控系统
  recordMetrics({
    responseTime: duration,
    errorRate: statusCode >= 400 ? 1 : 0
  });
  
  // 保持最近1小时的数据
  cleanupOldRequestData();
}

/**
 * 记录数据库查询性能
 */
export function recordDatabasePerformance(
  query: string,
  duration: number,
  database: string,
  metadata: {
    table?: string;
    operation?: 'SELECT' | 'INSERT' | 'UPDATE' | 'DELETE' | 'OTHER';
    rowsAffected?: number;
    error?: string;
  } = {}
): void {
  const performance: DatabasePerformance = {
    id: generateId(),
    query: query.substring(0, 200), // 限制查询长度
    duration,
    timestamp: new Date(),
    database,
    operation: metadata.operation || 'OTHER',
    table: metadata.table,
    rowsAffected: metadata.rowsAffected,
    error: metadata.error
  };
  
  databasePerformance.push(performance);
  
  // 检查性能阈值
  if (duration > PERFORMANCE_THRESHOLDS.DATABASE_QUERY_ERROR) {
    logger.error('Slow database query detected', {
      query: performance.query,
      duration,
      database,
      table: metadata.table
    });
  } else if (duration > PERFORMANCE_THRESHOLDS.DATABASE_QUERY_WARNING) {
    logger.warn('Slow database query detected', {
      query: performance.query,
      duration,
      database,
      table: metadata.table
    });
  }
  
  // 保持最近1小时的数据
  cleanupOldDatabaseData();
}

/**
 * 记录外部API调用性能
 */
export function recordExternalApiPerformance(
  url: string,
  method: string,
  duration: number,
  statusCode: number,
  service: string,
  error?: string
): void {
  const performance: ExternalApiPerformance = {
    id: generateId(),
    url,
    method,
    duration,
    statusCode,
    timestamp: new Date(),
    service,
    error
  };
  
  externalApiPerformance.push(performance);
  
  // 检查性能阈值
  if (duration > PERFORMANCE_THRESHOLDS.EXTERNAL_API_ERROR) {
    logger.error('Slow external API call detected', {
      url,
      method,
      duration,
      statusCode,
      service
    });
  } else if (duration > PERFORMANCE_THRESHOLDS.EXTERNAL_API_WARNING) {
    logger.warn('Slow external API call detected', {
      url,
      method,
      duration,
      statusCode,
      service
    });
  }
  
  // 保持最近1小时的数据
  cleanupOldExternalApiData();
}

/**
 * 获取性能统计
 */
export function getPerformanceStats(timeRange: number = 3600): {
  requests: {
    total: number;
    avgDuration: number;
    errorRate: number;
    slowRequests: number;
  };
  database: {
    total: number;
    avgDuration: number;
    slowQueries: number;
  };
  externalApi: {
    total: number;
    avgDuration: number;
    errorRate: number;
    slowCalls: number;
  };
} {
  const now = new Date();
  const startTime = new Date(now.getTime() - timeRange * 1000);
  
  // 请求统计
  const recentRequests = requestPerformance.filter(r => r.timestamp >= startTime);
  const requestStats = {
    total: recentRequests.length,
    avgDuration: recentRequests.length > 0 
      ? recentRequests.reduce((sum, r) => sum + r.duration, 0) / recentRequests.length 
      : 0,
    errorRate: recentRequests.length > 0 
      ? (recentRequests.filter(r => r.statusCode >= 400).length / recentRequests.length) * 100 
      : 0,
    slowRequests: recentRequests.filter(r => r.duration > PERFORMANCE_THRESHOLDS.REQUEST_DURATION_WARNING).length
  };
  
  // 数据库统计
  const recentDbQueries = databasePerformance.filter(d => d.timestamp >= startTime);
  const databaseStats = {
    total: recentDbQueries.length,
    avgDuration: recentDbQueries.length > 0 
      ? recentDbQueries.reduce((sum, d) => sum + d.duration, 0) / recentDbQueries.length 
      : 0,
    slowQueries: recentDbQueries.filter(d => d.duration > PERFORMANCE_THRESHOLDS.DATABASE_QUERY_WARNING).length
  };
  
  // 外部API统计
  const recentApiCalls = externalApiPerformance.filter(a => a.timestamp >= startTime);
  const externalApiStats = {
    total: recentApiCalls.length,
    avgDuration: recentApiCalls.length > 0 
      ? recentApiCalls.reduce((sum, a) => sum + a.duration, 0) / recentApiCalls.length 
      : 0,
    errorRate: recentApiCalls.length > 0 
      ? (recentApiCalls.filter(a => a.statusCode >= 400).length / recentApiCalls.length) * 100 
      : 0,
    slowCalls: recentApiCalls.filter(a => a.duration > PERFORMANCE_THRESHOLDS.EXTERNAL_API_WARNING).length
  };
  
  return {
    requests: requestStats,
    database: databaseStats,
    externalApi: externalApiStats
  };
}

/**
 * 获取性能趋势
 */
export function getPerformanceTrends(timeRange: number = 3600): {
  timestamps: Date[];
  requestDurations: number[];
  databaseDurations: number[];
  errorRates: number[];
} {
  const now = new Date();
  const startTime = new Date(now.getTime() - timeRange * 1000);
  const interval = Math.max(60, timeRange / 60); // 最小1分钟间隔
  
  const timestamps: Date[] = [];
  const requestDurations: number[] = [];
  const databaseDurations: number[] = [];
  const errorRates: number[] = [];
  
  for (let i = 0; i < 60; i++) {
    const bucketStart = new Date(startTime.getTime() + i * interval * 1000);
    const bucketEnd = new Date(bucketStart.getTime() + interval * 1000);
    
    timestamps.push(bucketStart);
    
    // 请求性能
    const bucketRequests = requestPerformance.filter(
      r => r.timestamp >= bucketStart && r.timestamp < bucketEnd
    );
    const avgRequestDuration = bucketRequests.length > 0
      ? bucketRequests.reduce((sum, r) => sum + r.duration, 0) / bucketRequests.length
      : 0;
    requestDurations.push(avgRequestDuration);
    
    // 数据库性能
    const bucketDbQueries = databasePerformance.filter(
      d => d.timestamp >= bucketStart && d.timestamp < bucketEnd
    );
    const avgDbDuration = bucketDbQueries.length > 0
      ? bucketDbQueries.reduce((sum, d) => sum + d.duration, 0) / bucketDbQueries.length
      : 0;
    databaseDurations.push(avgDbDuration);
    
    // 错误率
    const errorCount = bucketRequests.filter(r => r.statusCode >= 400).length;
    const errorRate = bucketRequests.length > 0 ? (errorCount / bucketRequests.length) * 100 : 0;
    errorRates.push(errorRate);
  }
  
  return {
    timestamps,
    requestDurations,
    databaseDurations,
    errorRates
  };
}

/**
 * 获取慢请求列表
 */
export function getSlowRequests(limit: number = 10): RequestPerformance[] {
  return requestPerformance
    .filter(r => r.duration > PERFORMANCE_THRESHOLDS.REQUEST_DURATION_WARNING)
    .sort((a, b) => b.duration - a.duration)
    .slice(0, limit);
}

/**
 * 获取慢查询列表
 */
export function getSlowQueries(limit: number = 10): DatabasePerformance[] {
  return databasePerformance
    .filter(d => d.duration > PERFORMANCE_THRESHOLDS.DATABASE_QUERY_WARNING)
    .sort((a, b) => b.duration - a.duration)
    .slice(0, limit);
}

/**
 * 性能监控中间件
 */
export function createPerformanceMiddleware() {
  return {
    // 请求开始时间记录
    startRequest: (requestId: string) => {
      const startTime = Date.now();
      return {
        requestId,
        startTime,
        end: (method: string, path: string, statusCode: number, metadata?: any) => {
          const duration = Date.now() - startTime;
          recordRequestPerformance(method, path, statusCode, duration, metadata);
        }
      };
    },
    
    // 数据库查询监控
    wrapDatabaseQuery: <T>(queryFn: () => Promise<T>, query: string, database: string, metadata?: any) => {
      return async (): Promise<T> => {
        const startTime = Date.now();
        try {
          const result = await queryFn();
          const duration = Date.now() - startTime;
          recordDatabasePerformance(query, duration, database, metadata);
          return result;
        } catch (error) {
          const duration = Date.now() - startTime;
          recordDatabasePerformance(query, duration, database, {
            ...metadata,
            error: error instanceof Error ? error.message : 'Unknown error'
          });
          throw error;
        }
      };
    },
    
    // 外部API调用监控
    wrapExternalApiCall: <T>(apiFn: () => Promise<T>, url: string, method: string, service: string) => {
      return async (): Promise<T> => {
        const startTime = Date.now();
        try {
          const result = await apiFn();
          const duration = Date.now() - startTime;
          recordExternalApiPerformance(url, method, duration, 200, service);
          return result;
        } catch (error) {
          const duration = Date.now() - startTime;
          const statusCode = error instanceof Error && 'status' in error ? (error as any).status : 500;
          recordExternalApiPerformance(
            url, 
            method, 
            duration, 
            statusCode, 
            service, 
            error instanceof Error ? error.message : 'Unknown error'
          );
          throw error;
        }
      };
    }
  };
}

/**
 * 工具函数
 */
function generateId(): string {
  return `perf_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

function cleanupOldMetrics(): void {
  const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);
  const recentMetrics = performanceMetrics.filter(m => m.timestamp > oneHourAgo);
  performanceMetrics.length = 0;
  performanceMetrics.push(...recentMetrics);
}

function cleanupOldRequestData(): void {
  const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);
  const recentRequests = requestPerformance.filter(r => r.timestamp > oneHourAgo);
  requestPerformance.length = 0;
  requestPerformance.push(...recentRequests);
}

function cleanupOldDatabaseData(): void {
  const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);
  const recentQueries = databasePerformance.filter(d => d.timestamp > oneHourAgo);
  databasePerformance.length = 0;
  databasePerformance.push(...recentQueries);
}

function cleanupOldExternalApiData(): void {
  const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);
  const recentCalls = externalApiPerformance.filter(a => a.timestamp > oneHourAgo);
  externalApiPerformance.length = 0;
  externalApiPerformance.push(...recentCalls);
}

// 定期清理旧数据（每10分钟执行一次）
if (typeof window === 'undefined') { // 仅在服务器端运行
  setInterval(() => {
    cleanupOldMetrics();
    cleanupOldRequestData();
    cleanupOldDatabaseData();
    cleanupOldExternalApiData();
  }, 10 * 60 * 1000);
}

export default {
  recordPerformanceMetric,
  recordRequestPerformance,
  recordDatabasePerformance,
  recordExternalApiPerformance,
  getPerformanceStats,
  getPerformanceTrends,
  getSlowRequests,
  getSlowQueries,
  createPerformanceMiddleware
};