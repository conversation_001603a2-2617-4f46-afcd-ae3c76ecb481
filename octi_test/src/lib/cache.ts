import { CacheConfig, CacheEntry } from '@/types'

/**
 * Redis缓存服务
 * 在开发阶段使用内存缓存模拟Redis功能
 */
export class RedisCache {
  private cache: Map<string, CacheEntry> = new Map()
  private config: CacheConfig & { keyPrefix?: string }
  private cleanupInterval: NodeJS.Timeout | null = null

  constructor(config: CacheConfig & { keyPrefix?: string }) {
    const defaultConfig = {
      ttl: 3600,
      strategy: 'ttl' as const
    }
    this.config = {
      ...defaultConfig,
      ...config
    }

    // 启动清理定时器
    this.startCleanup()
  }

  /**
   * 获取缓存值
   */
  async get<T = any>(key: string): Promise<T | null> {
    const fullKey = this.getFullKey(key)
    const entry = this.cache.get(fullKey)

    if (!entry) {
      return null
    }

    // 检查是否过期
    if (this.isExpired(entry)) {
      this.cache.delete(fullKey)
      return null
    }

    // 更新访问时间
    entry.accessedAt = new Date()
    return entry.value as T
  }

  /**
   * 设置缓存值
   */
  async set<T = any>(key: string, value: T, ttl?: number): Promise<void> {
    const fullKey = this.getFullKey(key)
    const now = new Date()
    const entry: CacheEntry<T> = {
      key: fullKey,
      value,
      ttl: ttl || this.config.ttl,
      createdAt: now,
      accessedAt: now
    }

    this.cache.set(fullKey, entry)

    // 如果超过最大大小，清理旧条目
    if (this.config.maxSize && this.cache.size > this.config.maxSize) {
      this.evictEntries()
    }
  }

  /**
   * 删除缓存
   */
  async delete(key: string): Promise<boolean> {
    const fullKey = this.getFullKey(key)
    return this.cache.delete(fullKey)
  }

  /**
   * 检查键是否存在
   */
  async has(key: string): Promise<boolean> {
    const fullKey = this.getFullKey(key)
    const entry = this.cache.get(fullKey)
    
    if (!entry) {
      return false
    }

    if (this.isExpired(entry)) {
      this.cache.delete(fullKey)
      return false
    }

    return true
  }

  /**
   * 清空所有缓存
   */
  async clear(): Promise<void> {
    this.cache.clear()
  }

  /**
   * 获取缓存大小
   */
  async size(): Promise<number> {
    return this.cache.size
  }

  /**
   * 获取所有键
   */
  async keys(): Promise<string[]> {
    const keys: string[] = []
    const entries = Array.from(this.cache.entries())
    for (const [key, entry] of entries) {
      if (!this.isExpired(entry)) {
        keys.push(key.replace(this.config.keyPrefix || '', ''))
      }
    }
    return keys
  }

  /**
   * 批量获取
   */
  async mget<T = any>(keys: string[]): Promise<(T | null)[]> {
    const results: (T | null)[] = []
    for (const key of keys) {
      results.push(await this.get<T>(key))
    }
    return results
  }

  /**
   * 批量设置
   */
  async mset(entries: Array<{ key: string; value: any; ttl?: number }>): Promise<void> {
    for (const entry of entries) {
      await this.set(entry.key, entry.value, entry.ttl)
    }
  }

  /**
   * 设置过期时间
   */
  async expire(key: string, ttl: number): Promise<boolean> {
    const fullKey = this.getFullKey(key)
    const entry = this.cache.get(fullKey)
    
    if (!entry) {
      return false
    }

    entry.ttl = ttl
    entry.createdAt = new Date()
    return true
  }

  /**
   * 获取剩余过期时间
   */
  async ttl(key: string): Promise<number> {
    const fullKey = this.getFullKey(key)
    const entry = this.cache.get(fullKey)
    
    if (!entry) {
      return -2 // 键不存在
    }

    const elapsed = Date.now() - entry.createdAt.getTime()
    const remaining = entry.ttl * 1000 - elapsed
    
    if (remaining <= 0) {
      return -1 // 已过期
    }

    return Math.ceil(remaining / 1000)
  }

  /**
   * 销毁缓存服务
   */
  destroy(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval)
      this.cleanupInterval = null
    }
    this.cache.clear()
  }

  /**
   * 获取完整键名
   */
  private getFullKey(key: string): string {
    return this.config.keyPrefix ? `${this.config.keyPrefix}${key}` : key
  }

  /**
   * 检查条目是否过期
   */
  private isExpired(entry: CacheEntry): boolean {
    const elapsed = Date.now() - entry.createdAt.getTime()
    return elapsed > entry.ttl * 1000
  }

  /**
   * 清理过期条目
   */
  private cleanup(): void {
    const now = Date.now()
    const keysToDelete: string[] = []
    const entries = Array.from(this.cache.entries())

    for (const [key, entry] of entries) {
      if (this.isExpired(entry)) {
        keysToDelete.push(key)
      }
    }

    for (const key of keysToDelete) {
      this.cache.delete(key)
    }
  }

  /**
   * 启动清理定时器
   */
  private startCleanup(): void {
    // 每分钟清理一次过期条目
    this.cleanupInterval = setInterval(() => {
      this.cleanup()
    }, 60000)
  }

  /**
   * 根据策略清理条目
   */
  private evictEntries(): void {
    if (!this.config.maxSize) {
      return
    }

    const entriesToRemove = this.cache.size - this.config.maxSize
    if (entriesToRemove <= 0) {
      return
    }

    const entries = Array.from(this.cache.entries())
    
    switch (this.config.strategy) {
      case 'lru':
        // 按访问时间排序，删除最久未访问的
        entries.sort((a, b) => a[1].accessedAt.getTime() - b[1].accessedAt.getTime())
        break
      case 'fifo':
        // 按创建时间排序，删除最早创建的
        entries.sort((a, b) => a[1].createdAt.getTime() - b[1].createdAt.getTime())
        break
      case 'ttl':
      default:
        // 按剩余TTL排序，删除最快过期的
        entries.sort((a, b) => {
          const aTtl = a[1].ttl * 1000 - (Date.now() - a[1].createdAt.getTime())
          const bTtl = b[1].ttl * 1000 - (Date.now() - b[1].createdAt.getTime())
          return aTtl - bTtl
        })
        break
    }

    for (let i = 0; i < entriesToRemove; i++) {
      this.cache.delete(entries[i][0])
    }
  }
}

/**
 * 内存缓存服务（用于开发和测试）
 */
export class MemoryCache extends RedisCache {
  constructor(config?: Partial<CacheConfig>) {
    super({
      ttl: 3600,
      strategy: 'lru',
      maxSize: 1000,
      ...config
    })
  }
}