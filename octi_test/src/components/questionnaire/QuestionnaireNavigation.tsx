'use client'

import React from 'react'
import { Button } from '@/components/ui/Button'

interface QuestionnaireNavigationProps {
  onNext: () => void
  onPrevious: () => void
  isFirst: boolean
  isLast: boolean
  disabled?: boolean
}

/**
 * 问卷导航组件
 * 处理问卷的前进后退逻辑
 */
export const QuestionnaireNavigation: React.FC<QuestionnaireNavigationProps> = ({
  onNext,
  onPrevious,
  isFirst,
  isLast,
  disabled = false
}) => {
  return (
    <div className="flex justify-between pt-6">
      <Button
        variant="outline"
        onClick={onPrevious}
        disabled={isFirst || disabled}
      >
        上一题
      </Button>
      
      <Button
        onClick={onNext}
        disabled={disabled}
        className={isLast ? 'bg-green-600 hover:bg-green-700' : ''}
      >
        {isLast ? '完成问卷' : '下一题'}
      </Button>
    </div>
  )
}