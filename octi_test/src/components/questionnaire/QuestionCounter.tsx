'use client'

import React from 'react'

interface QuestionCounterProps {
  current: number
  total: number
  dimension: 'SF' | 'IT' | 'MV' | 'AD'
  className?: string
}

const dimensionNames = {
  SF: 'S/F维度：战略聚焦度',
  IT: 'I/T维度：团队协同度', 
  MV: 'M/V维度：价值导向度',
  AD: 'A/D维度：能力发展度'
}

const dimensionColors = {
  SF: 'bg-blue-100 text-blue-800 border-blue-200',
  IT: 'bg-green-100 text-green-800 border-green-200',
  MV: 'bg-purple-100 text-purple-800 border-purple-200',
  AD: 'bg-orange-100 text-orange-800 border-orange-200'
}

export const QuestionCounter: React.FC<QuestionCounterProps> = ({
  current,
  total,
  dimension,
  className = ''
}) => {
  return (
    <div className={`flex items-center justify-between ${className}`}>
      <div className="flex items-center space-x-3">
        <div className={`px-3 py-1 rounded-full text-sm font-medium border ${dimensionColors[dimension]}`}>
          {dimensionNames[dimension]}
        </div>
        <div className="text-sm text-gray-500">
          第 {current} 题，共 {total} 题
        </div>
      </div>
      
      <div className="flex items-center space-x-2">
        <div className="text-lg font-semibold text-gray-900">
          {current}/{total}
        </div>
        <div className="w-16 bg-gray-200 rounded-full h-1.5">
          <div 
            className="bg-blue-600 h-1.5 rounded-full transition-all duration-300"
            style={{ width: `${(current / total) * 100}%` }}
          />
        </div>
      </div>
    </div>
  )
}

export default QuestionCounter