'use client'

import React from 'react'
import { ProgressBar } from './ProgressBar'
import { QuestionCounter } from './QuestionCounter'

interface QuestionnaireProgressProps {
  current: number
  total: number
  progress: number
}

/**
 * 问卷进度组件
 * 显示当前进度和问题计数
 */
export const QuestionnaireProgress: React.FC<QuestionnaireProgressProps> = ({
  current,
  total,
  progress
}) => {
  return (
    <div className="space-y-4">
      <QuestionCounter current={current} total={total} />
      <ProgressBar progress={progress} />
    </div>
  )
}