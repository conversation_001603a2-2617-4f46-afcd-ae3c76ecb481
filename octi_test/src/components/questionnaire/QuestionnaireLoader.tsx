'use client'

import React, { useState, useEffect } from 'react'
import { QuestionnaireConfig, QuestionnaireResponse } from '@/types'
import { Loading } from '@/components/ui/Loading'
import { Card } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import QuestionnaireRenderer from './QuestionnaireRenderer'

interface QuestionnaireLoaderProps {
  version?: 'standard' | 'professional'
  organizationId?: string
  onComplete: (responses: QuestionnaireResponse[]) => void
  onSave?: (responses: QuestionnaireResponse[]) => void
  className?: string
}

export const QuestionnaireLoader: React.FC<QuestionnaireLoaderProps> = ({
  version = 'standard',
  organizationId,
  onComplete,
  onSave,
  className = ''
}) => {
  const [config, setConfig] = useState<QuestionnaireConfig | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [savedResponses, setSavedResponses] = useState<QuestionnaireResponse[]>([])
  const [currentIndex, setCurrentIndex] = useState(0)
  const [questionBuffer, setQuestionBuffer] = useState<Question[]>([])
  const [isLoadingNext, setIsLoadingNext] = useState(false)

  // 加载问卷配置
  const loadQuestionnaireConfig = async () => {
    try {
      setLoading(true)
      setError(null)

      // 调用API生成问卷
      const response = await fetch('/api/questionnaire/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          version,
          organizationId
        })
      })

      if (!response.ok) {
        throw new Error(`生成问卷失败: ${response.statusText}`)
      }

      const data = await response.json()
      
      if (!data.success) {
        throw new Error(data.error || '生成问卷失败')
      }

      setConfig(data.data)
    } catch (err) {
      console.error('加载问卷配置失败:', err)
      setError(err instanceof Error ? err.message : '加载问卷配置失败')
    } finally {
      setLoading(false)
    }
  }

  // 加载已保存的回答
  const loadSavedResponses = async () => {
    if (!organizationId) return

    try {
      const response = await fetch(`/api/questionnaire/responses?organizationId=${organizationId}&version=${version}`)
      
      if (response.ok) {
        const data = await response.json()
        if (data.success && data.data) {
          setSavedResponses(data.data)
        }
      }
    } catch (err) {
      console.error('加载已保存回答失败:', err)
    }
  }

  // 保存回答
  const handleSave = async (responses: QuestionnaireResponse[]) => {
    if (!organizationId) return

    try {
      const response = await fetch('/api/questionnaire/responses', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          organizationId,
          version,
          responses
        })
      })

      if (response.ok) {
        setSavedResponses(responses)
        onSave?.(responses)
      }
    } catch (err) {
      console.error('保存回答失败:', err)
    }
  }

  // 完成问卷
  const handleComplete = async (responses: QuestionnaireResponse[]) => {
    // 先保存回答
    await handleSave(responses)
    
    // 调用完成回调
    onComplete(responses)
  }

  // 重新生成问卷
  const handleRegenerate = () => {
    setConfig(null)
    setSavedResponses([])
    loadQuestionnaireConfig()
  }

  useEffect(() => {
    loadQuestionnaireConfig()
    loadSavedResponses()
  }, [version, organizationId])

  if (loading) {
    return (
      <Card className={`p-8 ${className}`}>
        <div className="flex flex-col items-center justify-center space-y-4">
          <Loading size="lg" />
          <div className="text-center">
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              正在生成问卷...
            </h3>
            <p className="text-gray-600">
              AI正在根据OCTI模型为您生成个性化问卷，请稍候
            </p>
          </div>
        </div>
      </Card>
    )
  }

  if (error) {
    return (
      <Card className={`p-8 ${className}`}>
        <div className="text-center space-y-4">
          <div className="w-16 h-16 mx-auto bg-red-100 rounded-full flex items-center justify-center">
            <svg className="w-8 h-8 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              加载失败
            </h3>
            <p className="text-gray-600 mb-4">{error}</p>
            <Button onClick={handleRegenerate}>
              重新生成问卷
            </Button>
          </div>
        </div>
      </Card>
    )
  }

  if (!config) {
    return (
      <Card className={`p-8 ${className}`}>
        <div className="text-center text-gray-500">
          问卷配置为空
        </div>
      </Card>
    )
  }

  // 预加载策略：始终保持前方有10题的缓冲
  useEffect(() => {
    if (currentIndex + 10 >= questionBuffer.length && !isLoadingNext) {
      loadNextBatch()
    }
  }, [currentIndex])

  const loadNextBatch = async () => {
    setIsLoadingNext(true)
    // 加载下一批题目
    const nextQuestions = await fetchNextQuestions()
    setQuestionBuffer(prev => [...prev, ...nextQuestions])
    setIsLoadingNext(false)
  }

  return (
    <div className={className}>
      {/* 问卷信息 */}
      <Card className="p-6 mb-6">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-xl font-semibold text-gray-900">
              OCTI组织能力评估问卷
            </h2>
            <p className="text-gray-600 mt-1">
              {version === 'professional' ? '专业版' : '标准版'} • 共 {config.total_questions} 题
            </p>
          </div>
          <div className="flex items-center space-x-2">
            {savedResponses.length > 0 && (
              <div className="text-sm text-green-600 bg-green-50 px-3 py-1 rounded-full">
                已保存 {savedResponses.length} 题
              </div>
            )}
            <Button
              variant="outline"
              size="sm"
              onClick={handleRegenerate}
            >
              重新生成
            </Button>
          </div>
        </div>
      </Card>

      {/* 问卷渲染器 */}
      <QuestionnaireRenderer
        config={config}
        onComplete={handleComplete}
        onSave={handleSave}
        initialResponses={savedResponses}
        autoSave={true}
      />
    </div>
  )
}

export default QuestionnaireLoader
