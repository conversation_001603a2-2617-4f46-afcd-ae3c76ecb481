'use client'

import React from 'react'
import { QuestionComponentProps } from '@/types'
import { Card } from '@/components/ui/Card'

export const ScenarioQuestion: React.FC<QuestionComponentProps> = ({
  question,
  value,
  onChange,
  disabled = false
}) => {
  const handleOptionSelect = (optionValue: string | number) => {
    onChange(optionValue)
  }

  return (
    <div className="space-y-6">
      {/* 问题标题 */}
      <div className="space-y-3">
        <h3 className="text-lg font-semibold text-gray-900 leading-relaxed">
          {question.text}
        </h3>
        <div className="text-sm text-gray-500">
          {question.sub_dimension} • 情境题
        </div>
      </div>

      {/* 情境说明 */}
      <div className="bg-blue-50 border-l-4 border-blue-400 p-4 rounded-r-lg">
        <div className="flex items-start">
          <div className="flex-shrink-0">
            <svg className="w-5 h-5 text-blue-400 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
            </svg>
          </div>
          <div className="ml-3">
            <p className="text-sm text-blue-700">
              <strong>情境描述：</strong>请仔细阅读以下情境，并选择您认为最合适的应对方式。
            </p>
          </div>
        </div>
      </div>

      {/* 选项列表 */}
      <div className="space-y-4">
        {question.options.map((option, index) => {
          const isSelected = value === option.value
          
          return (
            <Card
              key={option.id}
              className={`p-5 cursor-pointer transition-all duration-200 hover:shadow-md ${
                isSelected 
                  ? 'border-blue-500 bg-blue-50 shadow-sm' 
                  : 'border-gray-200 hover:border-gray-300'
              } ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}
              onClick={() => !disabled && handleOptionSelect(option.value)}
            >
              <div className="flex items-start space-x-4">
                {/* 选项标记 */}
                <div className={`flex-shrink-0 w-7 h-7 rounded-full border-2 flex items-center justify-center mt-1 ${
                  isSelected 
                    ? 'border-blue-500 bg-blue-500' 
                    : 'border-gray-300'
                }`}>
                  {isSelected ? (
                    <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  ) : (
                    <span className={`text-sm font-semibold ${
                      isSelected ? 'text-white' : 'text-gray-400'
                    }`}>
                      {String.fromCharCode(65 + index)}
                    </span>
                  )}
                </div>
                
                {/* 选项内容 */}
                <div className="flex-1">
                  <div className={`text-base leading-relaxed ${
                    isSelected ? 'text-blue-900 font-medium' : 'text-gray-900'
                  }`}>
                    {option.text}
                  </div>
                </div>
              </div>
            </Card>
          )
        })}
      </div>

      {/* 提示信息 */}
      <div className="text-sm text-gray-500 bg-amber-50 border border-amber-200 p-4 rounded-lg">
        <div className="flex items-start space-x-2">
          <svg className="w-4 h-4 text-amber-500 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
          </svg>
          <div>
            <strong>答题提示：</strong>请基于您组织的实际情况和价值观来选择，没有标准答案，关键是真实反映组织特点。
          </div>
        </div>
      </div>
    </div>
  )
}

export default ScenarioQuestion