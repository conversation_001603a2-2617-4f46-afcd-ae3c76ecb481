'use client'

import React, { useState, useEffect } from 'react'
import { QuestionComponentProps } from '@/types'
import { Card } from '@/components/ui/Card'

interface RankingItem {
  id: string
  text: string
  value: string | number
  rank?: number
}

export const RankingQuestion: React.FC<QuestionComponentProps> = ({
  question,
  value,
  onChange,
  disabled = false
}) => {
  const [rankings, setRankings] = useState<RankingItem[]>(() => {
    // 初始化排序项
    const items = question.options.map(option => ({
      id: option.id,
      text: option.text,
      value: option.value,
      rank: undefined
    }))
    
    // 如果有已保存的值，恢复排序
    if (value && Array.isArray(value)) {
      value.forEach((rankedValue, index) => {
        const item = items.find(item => item.value === rankedValue)
        if (item) {
          item.rank = index + 1
        }
      })
    }
    
    return items
  })

  const [draggedItem, setDraggedItem] = useState<string | null>(null)

  // 更新排序
  const updateRanking = (itemId: string, newRank: number) => {
    const newRankings = [...rankings]
    const item = newRankings.find(item => item.id === itemId)
    
    if (!item) return

    // 清除原有排名
    const oldRank = item.rank
    item.rank = undefined
    
    // 调整其他项目的排名
    newRankings.forEach(otherItem => {
      if (otherItem.id !== itemId && otherItem.rank && otherItem.rank >= newRank) {
        otherItem.rank += 1
      }
    })
    
    // 设置新排名
    item.rank = newRank
    
    setRankings(newRankings)
    
    // 更新父组件
    const sortedValues = newRankings
      .filter(item => item.rank !== undefined)
      .sort((a, b) => (a.rank || 0) - (b.rank || 0))
      .map(item => item.value)
    
    onChange(sortedValues)
  }

  // 移除排名
  const removeRanking = (itemId: string) => {
    const newRankings = [...rankings]
    const item = newRankings.find(item => item.id === itemId)
    
    if (!item || !item.rank) return
    
    const removedRank = item.rank
    item.rank = undefined
    
    // 调整其他项目的排名
    newRankings.forEach(otherItem => {
      if (otherItem.rank && otherItem.rank > removedRank) {
        otherItem.rank -= 1
      }
    })
    
    setRankings(newRankings)
    
    // 更新父组件
    const sortedValues = newRankings
      .filter(item => item.rank !== undefined)
      .sort((a, b) => (a.rank || 0) - (b.rank || 0))
      .map(item => item.value)
    
    onChange(sortedValues)
  }

  // 获取下一个可用排名
  const getNextRank = () => {
    const usedRanks = rankings
      .filter(item => item.rank !== undefined)
      .map(item => item.rank!)
    
    for (let i = 1; i <= rankings.length; i++) {
      if (!usedRanks.includes(i)) {
        return i
      }
    }
    return rankings.length + 1
  }

  // 拖拽处理
  const handleDragStart = (e: React.DragEvent, itemId: string) => {
    setDraggedItem(itemId)
    e.dataTransfer.effectAllowed = 'move'
  }

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
    e.dataTransfer.dropEffect = 'move'
  }

  const handleDrop = (e: React.DragEvent, targetItemId: string) => {
    e.preventDefault()
    
    if (!draggedItem || draggedItem === targetItemId) {
      setDraggedItem(null)
      return
    }
    
    const targetItem = rankings.find(item => item.id === targetItemId)
    if (targetItem && targetItem.rank) {
      updateRanking(draggedItem, targetItem.rank)
    }
    
    setDraggedItem(null)
  }

  const rankedItems = rankings.filter(item => item.rank !== undefined).sort((a, b) => (a.rank || 0) - (b.rank || 0))
  const unrankedItems = rankings.filter(item => item.rank === undefined)

  return (
    <div className="space-y-6">
      {/* 问题标题 */}
      <div className="space-y-2">
        <h3 className="text-lg font-semibold text-gray-900 leading-relaxed">
          {question.text}
        </h3>
        <div className="text-sm text-gray-500">
          {question.sub_dimension} • 排序题
        </div>
      </div>

      {/* 说明 */}
      <div className="bg-purple-50 border-l-4 border-purple-400 p-4 rounded-r-lg">
        <div className="flex items-start">
          <div className="flex-shrink-0">
            <svg className="w-5 h-5 text-purple-400 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd" />
            </svg>
          </div>
          <div className="ml-3">
            <p className="text-sm text-purple-700">
              <strong>排序说明：</strong>请按重要性从高到低对以下选项进行排序，点击选项添加到排序列表，可拖拽调整顺序。
            </p>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 待排序选项 */}
        <div className="space-y-3">
          <h4 className="font-medium text-gray-900">待排序选项</h4>
          <div className="space-y-2">
            {unrankedItems.map((item) => (
              <Card
                key={item.id}
                className={`p-3 cursor-pointer transition-all duration-200 hover:shadow-md border-gray-200 hover:border-gray-300 ${
                  disabled ? 'opacity-50 cursor-not-allowed' : ''
                }`}
                onClick={() => !disabled && updateRanking(item.id, getNextRank())}
              >
                <div className="flex items-center space-x-3">
                  <div className="w-6 h-6 rounded border-2 border-gray-300 flex items-center justify-center">
                    <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                    </svg>
                  </div>
                  <span className="text-sm text-gray-900">{item.text}</span>
                </div>
              </Card>
            ))}
          </div>
        </div>

        {/* 已排序列表 */}
        <div className="space-y-3">
          <h4 className="font-medium text-gray-900">排序结果 ({rankedItems.length}/{rankings.length})</h4>
          <div className="space-y-2">
            {rankedItems.map((item) => (
              <Card
                key={item.id}
                className={`p-3 cursor-move transition-all duration-200 border-blue-200 bg-blue-50 ${
                  draggedItem === item.id ? 'opacity-50' : ''
                } ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}
                draggable={!disabled}
                onDragStart={(e) => handleDragStart(e, item.id)}
                onDragOver={handleDragOver}
                onDrop={(e) => handleDrop(e, item.id)}
              >
                <div className="flex items-center space-x-3">
                  <div className="w-6 h-6 rounded-full bg-blue-500 text-white text-xs font-semibold flex items-center justify-center">
                    {item.rank}
                  </div>
                  <span className="text-sm text-blue-900 flex-1">{item.text}</span>
                  <button
                    onClick={() => !disabled && removeRanking(item.id)}
                    className="text-red-500 hover:text-red-700 p-1"
                    disabled={disabled}
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>
              </Card>
            ))}
            
            {rankedItems.length === 0 && (
              <div className="text-center py-8 text-gray-500 border-2 border-dashed border-gray-300 rounded-lg">
                点击左侧选项开始排序
              </div>
            )}
          </div>
        </div>
      </div>

      {/* 提示信息 */}
      <div className="text-sm text-gray-500 bg-gray-50 p-3 rounded-lg">
        💡 请根据您组织的实际情况，按重要性从高到低进行排序
      </div>
    </div>
  )
}

export default RankingQuestion