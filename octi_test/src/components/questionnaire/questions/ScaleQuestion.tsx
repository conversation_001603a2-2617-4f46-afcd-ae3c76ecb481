'use client'

import React from 'react'
import { QuestionComponentProps } from '@/types'
import { Card } from '@/components/ui/Card'

export const ScaleQuestion: React.FC<QuestionComponentProps> = ({
  question,
  value,
  onChange,
  disabled = false
}) => {
  // 假设量表是1-5分
  const scaleOptions = question.options.length > 0 
    ? question.options 
    : [
        { id: '1', text: '完全不符合', value: 1, score: 1 },
        { id: '2', text: '不太符合', value: 2, score: 2 },
        { id: '3', text: '一般', value: 3, score: 3 },
        { id: '4', text: '比较符合', value: 4, score: 4 },
        { id: '5', text: '完全符合', value: 5, score: 5 }
      ]

  const handleScaleSelect = (scaleValue: string | number) => {
    onChange(scaleValue)
  }

  return (
    <div className="space-y-6">
      {/* 问题标题 */}
      <div className="space-y-2">
        <h3 className="text-lg font-semibold text-gray-900 leading-relaxed">
          {question.text}
        </h3>
        <div className="text-sm text-gray-500">
          {question.sub_dimension} • 量表题
        </div>
      </div>

      {/* 量表说明 */}
      <div className="bg-green-50 border-l-4 border-green-400 p-4 rounded-r-lg">
        <div className="flex items-start">
          <div className="flex-shrink-0">
            <svg className="w-5 h-5 text-green-400 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
            </svg>
          </div>
          <div className="ml-3">
            <p className="text-sm text-green-700">
              <strong>评分说明：</strong>请根据以下描述与您组织实际情况的符合程度进行评分。
            </p>
          </div>
        </div>
      </div>

      {/* 量表选项 */}
      <Card className="p-6">
        <div className="space-y-6">
          {/* 量表刻度 */}
          <div className="flex justify-between items-center">
            {scaleOptions.map((option, index) => {
              const isSelected = value === option.value
              
              return (
                <div key={option.id} className="flex flex-col items-center space-y-2">
                  {/* 刻度点 */}
                  <button
                    onClick={() => !disabled && handleScaleSelect(option.value)}
                    disabled={disabled}
                    className={`w-12 h-12 rounded-full border-2 transition-all duration-200 flex items-center justify-center font-semibold ${
                      isSelected
                        ? 'border-blue-500 bg-blue-500 text-white shadow-lg transform scale-110'
                        : 'border-gray-300 bg-white text-gray-600 hover:border-blue-300 hover:bg-blue-50'
                    } ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer hover:shadow-md'}`}
                  >
                    {option.value}
                  </button>
                  
                  {/* 刻度标签 */}
                  <div className={`text-xs text-center max-w-16 leading-tight ${
                    isSelected ? 'text-blue-600 font-medium' : 'text-gray-500'
                  }`}>
                    {option.text}
                  </div>
                </div>
              )
            })}
          </div>

          {/* 连接线 */}
          <div className="relative">
            <div className="absolute top-0 left-6 right-6 h-0.5 bg-gray-200" style={{ top: '-3rem' }} />
            {scaleOptions.map((option, index) => {
              if (index === scaleOptions.length - 1) return null
              
              const leftPercent = (index / (scaleOptions.length - 1)) * 100
              const rightPercent = ((index + 1) / (scaleOptions.length - 1)) * 100
              
              return (
                <div
                  key={`line-${index}`}
                  className="absolute h-0.5 bg-gray-200"
                  style={{
                    left: `${leftPercent}%`,
                    right: `${100 - rightPercent}%`,
                    top: '-3rem'
                  }}
                />
              )
            })}
          </div>

          {/* 极值标签 */}
          <div className="flex justify-between text-sm text-gray-600 pt-4">
            <div className="text-left">
              <div className="font-medium">不符合</div>
              <div className="text-xs text-gray-500">1分</div>
            </div>
            <div className="text-center">
              <div className="font-medium">中等</div>
              <div className="text-xs text-gray-500">3分</div>
            </div>
            <div className="text-right">
              <div className="font-medium">非常符合</div>
              <div className="text-xs text-gray-500">5分</div>
            </div>
          </div>
        </div>
      </Card>

      {/* 当前选择显示 */}
      {value !== undefined && (
        <div className="bg-blue-50 border border-blue-200 p-4 rounded-lg">
          <div className="flex items-center space-x-2">
            <svg className="w-5 h-5 text-blue-500" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
            </svg>
            <span className="text-sm text-blue-700">
              您的评分：<strong>{value}分</strong> - {scaleOptions.find(opt => opt.value === value)?.text}
            </span>
          </div>
        </div>
      )}

      {/* 提示信息 */}
      <div className="text-sm text-gray-500 bg-gray-50 p-3 rounded-lg">
        💡 请根据您组织的真实情况进行评分，没有对错之分，关键是客观准确
      </div>
    </div>
  )
}

export default ScaleQuestion