'use client'

import React from 'react'
import { ConfigQuestion } from '@/types'
import { ChoiceQuestion } from './questions/ChoiceQuestion'
import { ScenarioQuestion } from './questions/ScenarioQuestion'
import { RankingQuestion } from './questions/RankingQuestion'
import { ScaleQuestion } from './questions/ScaleQuestion'

interface QuestionDisplayProps {
  question: ConfigQuestion | undefined
  value: any
  onChange: (value: any) => void
  error?: string
}

/**
 * 问题显示组件
 * 根据问题类型渲染对应的问题组件
 */
export const QuestionDisplay: React.FC<QuestionDisplayProps> = ({
  question,
  value,
  onChange,
  error
}) => {
  if (!question) return null

  const commonProps = {
    question,
    value,
    onChange,
    disabled: false
  }

  const renderQuestion = () => {
    switch (question.type) {
      case 'choice':
        return <ChoiceQuestion {...commonProps} />
      case 'scenario':
        return <ScenarioQuestion {...commonProps} />
      case 'ranking':
        return <RankingQuestion {...commonProps} />
      case 'scale':
        return <ScaleQuestion {...commonProps} />
      default:
        return (
          <div className="text-red-500">
            不支持的问题类型: {question.type}
          </div>
        )
    }
  }

  return (
    <div className="space-y-4">
      {renderQuestion()}
      {error && (
        <div className="text-red-500 text-sm">
          {error}
        </div>
      )}
    </div>
  )
}