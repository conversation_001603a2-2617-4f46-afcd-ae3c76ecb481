'use client'

import React from 'react'
import { QuestionnaireConfig, QuestionnaireResponse } from '@/types'
import { useQuestionnaire } from '@/hooks/useQuestionnaire'
import { QuestionDisplay } from './QuestionDisplay'
import { QuestionnaireNavigation } from './QuestionnaireNavigation'
import { QuestionnaireProgress } from './QuestionnaireProgress'
import { Card } from '@/components/ui/Card'
import { Loading } from '@/components/ui/Loading'

interface QuestionnaireRendererProps {
  config: QuestionnaireConfig
  initialResponses?: QuestionnaireResponse[]
  onComplete: (responses: QuestionnaireResponse[]) => Promise<void>
  onSave?: (responses: QuestionnaireResponse[]) => Promise<void>
  className?: string
}

/**
 * 问卷渲染器主组件
 * 负责协调各个子组件，保持简洁
 */
export const QuestionnaireRenderer: React.FC<QuestionnaireRendererProps> = ({
  config,
  initialResponses = [],
  onComplete,
  onSave,
  className
}) => {
  const {
    currentQuestion,
    currentQuestionIndex,
    responses,
    errors,
    isLoading,
    isFirst,
    isLast,
    handleNext,
    handlePrevious,
    updateResponse,
    progress
  } = useQuestionnaire({
    config,
    initialResponses,
    onComplete,
    onSave
  })

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <Loading size="lg" text="正在提交问卷..." />
      </div>
    )
  }

  return (
    <div className={className}>
      <QuestionnaireProgress 
        current={currentQuestionIndex + 1}
        total={config.questions.length}
        progress={progress}
      />
      
      <Card className="p-6 mt-4">
        <QuestionDisplay
          question={currentQuestion}
          value={responses.find(r => r.questionId === currentQuestion?.id)?.answer}
          onChange={(value) => currentQuestion && updateResponse(currentQuestion.id, value)}
          error={currentQuestion ? errors[currentQuestion.id] : undefined}
        />
        
        <QuestionnaireNavigation
          onNext={handleNext}
          onPrevious={handlePrevious}
          isFirst={isFirst}
          isLast={isLast}
          disabled={isLoading}
        />
      </Card>
    </div>
  )
}
