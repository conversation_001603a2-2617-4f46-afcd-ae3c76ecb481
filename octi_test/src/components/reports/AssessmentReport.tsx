'use client'

import React from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { type AssessmentReport as AssessmentReportType, RadarChartData } from '@/types'

interface AssessmentReportProps {
  report: AssessmentReportType
  onExport?: () => void
  onShare?: () => void
  className?: string
}

export const AssessmentReport: React.FC<AssessmentReportProps> = ({
  report,
  onExport,
  onShare,
  className = ''
}) => {
  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('zh-CN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(date)
  }

  const getOverallScore = () => {
    if (!report.radarChart) return 0
    const { SF, IT, MV, AD } = report.radarChart.dimensions
    return Math.round((SF + IT + MV + AD) / 4 * 10) / 10
  }

  const getScoreLevel = (score: number) => {
    if (score >= 4) return { level: '优秀', color: 'text-green-600', bgColor: 'bg-green-50' }
    if (score >= 3) return { level: '良好', color: 'text-blue-600', bgColor: 'bg-blue-50' }
    if (score >= 2) return { level: '一般', color: 'text-yellow-600', bgColor: 'bg-yellow-50' }
    return { level: '待改进', color: 'text-red-600', bgColor: 'bg-red-50' }
  }

  const overallScore = getOverallScore()
  const scoreLevel = getScoreLevel(overallScore)

  return (
    <div className={`space-y-6 ${className}`}>
      {/* 报告头部 */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-2xl font-bold">
                OCTI 组织文化评估报告
              </CardTitle>
              <CardDescription className="text-lg mt-2">
                {report.organizationName} • {report.version === 'professional' ? '专业版' : '标准版'}
              </CardDescription>
            </div>
            <div className="text-right">
              <div className="text-sm text-gray-500 mb-2">
                生成时间: {formatDate(report.generatedAt)}
              </div>
              <div className="flex space-x-2">
                {onExport && (
                  <Button variant="outline" size="sm" onClick={onExport}>
                    导出报告
                  </Button>
                )}
                {onShare && (
                  <Button variant="outline" size="sm" onClick={onShare}>
                    分享报告
                  </Button>
                )}
              </div>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* 总体评分 */}
      <Card>
        <CardHeader>
          <CardTitle>总体评估</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between">
            <div>
              <div className="text-3xl font-bold text-gray-900 mb-2">
                {overallScore} / 5.0
              </div>
              <div className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${scoreLevel.color} ${scoreLevel.bgColor}`}>
                {scoreLevel.level}
              </div>
            </div>
            <div className="text-right">
              <div className="text-sm text-gray-600 mb-1">
                组织文化成熟度
              </div>
              <div className="w-32 bg-gray-200 rounded-full h-2">
                <div 
                  className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${(overallScore / 5) * 100}%` }}
                ></div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 雷达图 */}
      {report.radarChart && (
        <Card>
          <CardHeader>
            <CardTitle>四维八极分析</CardTitle>
            <CardDescription>
              基于OCTI模型的四个核心维度评估结果
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-64 flex items-center justify-center bg-gray-50 rounded-lg">
              <span className="text-gray-500">雷达图组件待实现</span>
            </div>
          </CardContent>
        </Card>
      )}

      {/* 维度分析 */}
      {report.radarChart && (
        <Card>
          <CardHeader>
            <CardTitle>维度详细分析</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {Object.entries(report.radarChart.dimensions).map(([key, value]) => {
                const dimensionNames: Record<string, string> = {
                  SF: '战略聚焦度',
                  IT: '团队协同度', 
                  MV: '激励价值观',
                  AD: '适应性发展'
                }
                return (
                  <div key={key} className="p-4 border rounded-lg">
                    <h4 className="font-medium mb-2">{dimensionNames[key]}</h4>
                    <div className="flex items-center space-x-2">
                      <span className="text-2xl font-bold">{value}</span>
                      <span className="text-gray-500">/ {report.radarChart?.maxScore}</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
                      <div 
                        className="bg-blue-600 h-2 rounded-full"
                        style={{ width: `${(value / (report.radarChart?.maxScore || 5)) * 100}%` }}
                      ></div>
                    </div>
                  </div>
                )
              })}
            </div>
          </CardContent>
        </Card>
      )}

      {/* 报告内容 */}
      <div className="space-y-4">
        {report.sections
          .sort((a, b) => a.order - b.order)
          .map((section) => (
            <Card key={section.id}>
              <CardHeader>
                <CardTitle>{section.title}</CardTitle>
              </CardHeader>
              <CardContent>
                {section.type === 'text' && (
                  <div className="prose max-w-none">
                    <p className="text-gray-700 leading-relaxed">
                      {section.content}
                    </p>
                  </div>
                )}
                
                {section.type === 'list' && section.data && (
                  <ul className="space-y-2">
                    {section.data.map((item: string, index: number) => (
                      <li key={index} className="flex items-start space-x-2">
                        <span className="w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0"></span>
                        <span className="text-gray-700">{item}</span>
                      </li>
                    ))}
                  </ul>
                )}
                
                {section.type === 'table' && section.data && (
                  <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50">
                        <tr>
                          {section.data.headers?.map((header: string, index: number) => (
                            <th key={index} className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              {header}
                            </th>
                          ))}
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {section.data.rows?.map((row: string[], rowIndex: number) => (
                          <tr key={rowIndex}>
                            {row.map((cell, cellIndex) => (
                              <td key={cellIndex} className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {cell}
                              </td>
                            ))}
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                )}
              </CardContent>
            </Card>
          ))
        }
      </div>

      {/* 改进建议 */}
      <Card>
        <CardHeader>
          <CardTitle>改进建议</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {[
              '加强战略聚焦度，明确组织发展方向和核心目标',
              '优化团队协同机制，提升跨部门沟通效率', 
              '建立更完善的激励体系，激发员工内在动力',
              '增强组织适应性，提升应对变化的能力'
            ].map((recommendation, index) => (
              <div key={index} className="flex items-start space-x-3 p-3 bg-blue-50 rounded-lg">
                <div className="w-6 h-6 bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-medium flex-shrink-0">
                  {index + 1}
                </div>
                <p className="text-gray-700">{recommendation}</p>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* 报告说明 */}
      <Card>
        <CardHeader>
          <CardTitle>报告说明</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-sm text-gray-600 space-y-2">
            <p>
              • 本报告基于OCTI（Organizational Culture Transformation Index）模型生成
            </p>
            <p>
              • 评估结果反映了当前组织文化的特征和发展水平
            </p>
            <p>
              • 建议结合组织实际情况，制定针对性的改进措施
            </p>
            <p>
              • 如需更详细的分析和指导，请联系专业顾问
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

export default AssessmentReport