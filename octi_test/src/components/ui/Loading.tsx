'use client'

import React from 'react'
import { cn } from '@/lib/utils'

/**
 * 加载组件尺寸类型
 */
export type LoadingSize = 'sm' | 'md' | 'lg'

/**
 * 加载组件属性接口
 */
export interface LoadingProps {
  size?: LoadingSize
  className?: string
  text?: string
  overlay?: boolean
}

/**
 * 加载组件尺寸样式映射
 */
const loadingSizes = {
  sm: 'h-4 w-4',
  md: 'h-6 w-6',
  lg: 'h-8 w-8'
}

/**
 * 旋转加载图标组件
 */
const SpinnerIcon: React.FC<{ size: LoadingSize; className?: string }> = ({ size, className }) => (
  <svg
    className={cn(
      'animate-spin',
      loadingSizes[size],
      className
    )}
    xmlns="http://www.w3.org/2000/svg"
    fill="none"
    viewBox="0 0 24 24"
  >
    <circle
      className="opacity-25"
      cx="12"
      cy="12"
      r="10"
      stroke="currentColor"
      strokeWidth="4"
    ></circle>
    <path
      className="opacity-75"
      fill="currentColor"
      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
    ></path>
  </svg>
)

/**
 * 加载组件
 * 提供旋转加载指示器
 */
export const Loading: React.FC<LoadingProps> = ({
  size = 'md',
  className,
  text,
  overlay = false
}) => {
  const content = (
    <div className={cn(
      'flex items-center justify-center',
      text && 'flex-col space-y-2',
      !text && 'space-x-2',
      className
    )}>
      <SpinnerIcon size={size} className="text-primary" />
      {text && (
        <span className="text-sm text-muted-foreground">
          {text}
        </span>
      )}
    </div>
  )

  if (overlay) {
    return (
      <div className="fixed inset-0 z-50 flex items-center justify-center bg-background/80 backdrop-blur-sm">
        {content}
      </div>
    )
  }

  return content
}

/**
 * 骨架屏组件属性接口
 */
export interface SkeletonProps extends React.HTMLAttributes<HTMLDivElement> {
  lines?: number
  avatar?: boolean
}

/**
 * 骨架屏组件
 * 提供内容加载时的占位符
 */
export const Skeleton: React.FC<SkeletonProps> = ({
  className,
  lines = 3,
  avatar = false,
  ...props
}) => {
  return (
    <div className={cn('animate-pulse', className)} {...props}>
      {avatar && (
        <div className="flex items-center space-x-4 mb-4">
          <div className="rounded-full bg-gray-300 h-10 w-10"></div>
          <div className="flex-1 space-y-2">
            <div className="h-4 bg-gray-300 rounded w-3/4"></div>
            <div className="h-3 bg-gray-300 rounded w-1/2"></div>
          </div>
        </div>
      )}
      <div className="space-y-3">
        {Array.from({ length: lines }).map((_, index) => (
          <div
            key={index}
            className={cn(
              'h-4 bg-gray-300 rounded',
              index === lines - 1 ? 'w-2/3' : 'w-full'
            )}
          ></div>
        ))}
      </div>
    </div>
  )
}

/**
 * 点状加载指示器组件
 */
export const DotLoading: React.FC<{ className?: string }> = ({ className }) => {
  return (
    <div className={cn('flex space-x-1', className)}>
      {[0, 1, 2].map((index) => (
        <div
          key={index}
          className="w-2 h-2 bg-current rounded-full animate-bounce"
          style={{
            animationDelay: `${index * 0.1}s`,
            animationDuration: '0.6s'
          }}
        ></div>
      ))}
    </div>
  )
}

/**
 * 进度条组件属性接口
 */
export interface ProgressProps {
  value: number
  max?: number
  className?: string
  showLabel?: boolean
  size?: 'sm' | 'md' | 'lg'
}

/**
 * 进度条组件
 */
export const Progress: React.FC<ProgressProps> = ({
  value,
  max = 100,
  className,
  showLabel = false,
  size = 'md'
}) => {
  const percentage = Math.min(Math.max((value / max) * 100, 0), 100)
  
  const sizeClasses = {
    sm: 'h-1',
    md: 'h-2',
    lg: 'h-3'
  }

  return (
    <div className={cn('w-full', className)}>
      {showLabel && (
        <div className="flex justify-between text-sm text-muted-foreground mb-1">
          <span>进度</span>
          <span>{Math.round(percentage)}%</span>
        </div>
      )}
      <div className={cn(
        'w-full bg-gray-200 rounded-full overflow-hidden',
        sizeClasses[size]
      )}>
        <div
          className="h-full bg-primary transition-all duration-300 ease-out"
          style={{ width: `${percentage}%` }}
        ></div>
      </div>
    </div>
  )
}