'use client'

import React, { createContext, useContext, useEffect, useState } from 'react'
import { cn } from '@/lib/utils'
import { Button } from './Button'

/**
 * 对话框上下文接口
 */
interface DialogContextType {
  open: boolean
  setOpen: (open: boolean) => void
}

/**
 * 对话框上下文
 */
const DialogContext = createContext<DialogContextType | undefined>(undefined)

/**
 * 使用对话框上下文的Hook
 */
const useDialog = () => {
  const context = useContext(DialogContext)
  if (!context) {
    throw new Error('useDialog must be used within a Dialog')
  }
  return context
}

/**
 * 对话框根组件属性接口
 */
export interface DialogProps {
  children: React.ReactNode
  open?: boolean
  onOpenChange?: (open: boolean) => void
  defaultOpen?: boolean
}

/**
 * 对话框根组件
 */
export const Dialog: React.FC<DialogProps> = ({
  children,
  open: controlledOpen,
  onOpenChange,
  defaultOpen = false
}) => {
  const [internalOpen, setInternalOpen] = useState(defaultOpen)
  
  const open = controlledOpen !== undefined ? controlledOpen : internalOpen
  const setOpen = (newOpen: boolean) => {
    if (controlledOpen === undefined) {
      setInternalOpen(newOpen)
    }
    onOpenChange?.(newOpen)
  }

  return (
    <DialogContext.Provider value={{ open, setOpen }}>
      {children}
    </DialogContext.Provider>
  )
}

/**
 * 对话框触发器组件属性接口
 */
export interface DialogTriggerProps {
  children: React.ReactNode
  asChild?: boolean
}

/**
 * 对话框触发器组件
 */
export const DialogTrigger: React.FC<DialogTriggerProps> = ({
  children,
  asChild = false
}) => {
  const { setOpen } = useDialog()

  if (asChild && React.isValidElement(children)) {
    return React.cloneElement(children as React.ReactElement<any>, {
      onClick: (e: React.MouseEvent) => {
        const originalOnClick = (children as React.ReactElement<any>).props.onClick
        originalOnClick?.(e)
        setOpen(true)
      }
    })
  }

  return (
    <Button onClick={() => setOpen(true)}>
      {children}
    </Button>
  )
}

/**
 * 对话框内容组件属性接口
 */
export interface DialogContentProps {
  children: React.ReactNode
  className?: string
  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full'
  showClose?: boolean
  onEscapeKeyDown?: (event: KeyboardEvent) => void
  onPointerDownOutside?: (event: PointerEvent) => void
}

/**
 * 对话框尺寸样式映射
 */
const dialogSizes = {
  sm: 'max-w-sm',
  md: 'max-w-md',
  lg: 'max-w-lg',
  xl: 'max-w-xl',
  full: 'max-w-full mx-4'
}

/**
 * 对话框内容组件
 */
export const DialogContent: React.FC<DialogContentProps> = ({
  children,
  className,
  size = 'md',
  showClose = true,
  onEscapeKeyDown,
  onPointerDownOutside
}) => {
  const { open, setOpen } = useDialog()

  // 处理ESC键关闭
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onEscapeKeyDown?.(event)
        if (!event.defaultPrevented) {
          setOpen(false)
        }
      }
    }

    if (open) {
      document.addEventListener('keydown', handleKeyDown)
      // 防止背景滚动
      document.body.style.overflow = 'hidden'
    }

    return () => {
      document.removeEventListener('keydown', handleKeyDown)
      document.body.style.overflow = 'unset'
    }
  }, [open, onEscapeKeyDown, setOpen])

  // 处理点击外部关闭
  const handleBackdropClick = (event: React.MouseEvent) => {
    if (event.target === event.currentTarget) {
      const pointerEvent = new PointerEvent('pointerdown', {
        bubbles: true,
        cancelable: true
      })
      onPointerDownOutside?.(pointerEvent)
      if (!pointerEvent.defaultPrevented) {
        setOpen(false)
      }
    }
  }

  if (!open) return null

  return (
    <div className="fixed inset-0 z-50">
      {/* 背景遮罩 */}
      <div 
        className="fixed inset-0 bg-black/50 backdrop-blur-sm"
        onClick={handleBackdropClick}
      />
      
      {/* 对话框内容 */}
      <div className="fixed inset-0 flex items-center justify-center p-4">
        <div
          className={cn(
            'relative w-full bg-background rounded-lg shadow-lg border',
            dialogSizes[size],
            className
          )}
          onClick={(e) => e.stopPropagation()}
        >
          {showClose && (
            <button
              className="absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none"
              onClick={() => setOpen(false)}
            >
              <svg
                className="h-4 w-4"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
              <span className="sr-only">关闭</span>
            </button>
          )}
          {children}
        </div>
      </div>
    </div>
  )
}

/**
 * 对话框头部组件属性接口
 */
export interface DialogHeaderProps {
  children: React.ReactNode
  className?: string
}

/**
 * 对话框头部组件
 */
export const DialogHeader: React.FC<DialogHeaderProps> = ({
  children,
  className
}) => {
  return (
    <div className={cn('flex flex-col space-y-1.5 text-center sm:text-left p-6 pb-0', className)}>
      {children}
    </div>
  )
}

/**
 * 对话框标题组件属性接口
 */
export interface DialogTitleProps {
  children: React.ReactNode
  className?: string
}

/**
 * 对话框标题组件
 */
export const DialogTitle: React.FC<DialogTitleProps> = ({
  children,
  className
}) => {
  return (
    <h2 className={cn('text-lg font-semibold leading-none tracking-tight', className)}>
      {children}
    </h2>
  )
}

/**
 * 对话框描述组件属性接口
 */
export interface DialogDescriptionProps {
  children: React.ReactNode
  className?: string
}

/**
 * 对话框描述组件
 */
export const DialogDescription: React.FC<DialogDescriptionProps> = ({
  children,
  className
}) => {
  return (
    <p className={cn('text-sm text-muted-foreground', className)}>
      {children}
    </p>
  )
}

/**
 * 对话框主体组件属性接口
 */
export interface DialogBodyProps {
  children: React.ReactNode
  className?: string
}

/**
 * 对话框主体组件
 */
export const DialogBody: React.FC<DialogBodyProps> = ({
  children,
  className
}) => {
  return (
    <div className={cn('p-6', className)}>
      {children}
    </div>
  )
}

/**
 * 对话框底部组件属性接口
 */
export interface DialogFooterProps {
  children: React.ReactNode
  className?: string
}

/**
 * 对话框底部组件
 */
export const DialogFooter: React.FC<DialogFooterProps> = ({
  children,
  className
}) => {
  return (
    <div className={cn('flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2 p-6 pt-0', className)}>
      {children}
    </div>
  )
}

/**
 * 确认对话框组件属性接口
 */
export interface ConfirmDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  title: string
  description?: string
  confirmText?: string
  cancelText?: string
  onConfirm: () => void
  onCancel?: () => void
  variant?: 'default' | 'destructive'
  loading?: boolean
}

/**
 * 确认对话框组件
 */
export const ConfirmDialog: React.FC<ConfirmDialogProps> = ({
  open,
  onOpenChange,
  title,
  description,
  confirmText = '确认',
  cancelText = '取消',
  onConfirm,
  onCancel,
  variant = 'default',
  loading = false
}) => {
  const handleCancel = () => {
    onCancel?.()
    onOpenChange(false)
  }

  const handleConfirm = () => {
    onConfirm()
    if (!loading) {
      onOpenChange(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent size="sm">
        <DialogHeader>
          <DialogTitle>{title}</DialogTitle>
          {description && (
            <DialogDescription>{description}</DialogDescription>
          )}
        </DialogHeader>
        <DialogFooter>
          <Button
            variant="outline"
            onClick={handleCancel}
            disabled={loading}
          >
            {cancelText}
          </Button>
          <Button
            variant={variant === 'destructive' ? 'destructive' : 'default'}
            onClick={handleConfirm}
            loading={loading}
          >
            {confirmText}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}