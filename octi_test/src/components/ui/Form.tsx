'use client'

import React, { createContext, useContext, useState, useCallback, memo } from 'react'
import { cn } from '@/lib/utils'
import { Input } from './Input'
import { Button } from './Button'

/**
 * 表单字段值类型
 */
export type FormFieldValue = string | number | boolean | Date | null | undefined

/**
 * 表单数据类型
 */
export type FormData = Record<string, FormFieldValue>

/**
 * 表单验证规则类型
 */
export interface ValidationRule {
  required?: boolean
  minLength?: number
  maxLength?: number
  pattern?: RegExp
  custom?: (value: FormFieldValue) => string | null
  message?: string
}

/**
 * 表单字段配置
 */
export interface FormField {
  name: string
  label?: string
  type?: string
  placeholder?: string
  helperText?: string
  rules?: ValidationRule[]
  disabled?: boolean
  options?: Array<{ label: string; value: string | number }>
}

/**
 * 表单错误类型
 */
export type FormErrors = Record<string, string>

/**
 * 表单上下文接口
 */
interface FormContextType {
  data: FormData
  errors: FormErrors
  isSubmitting: boolean
  setValue: (name: string, value: FormFieldValue) => void
  setError: (name: string, error: string) => void
  clearError: (name: string) => void
  validateField: (name: string, rules?: ValidationRule[]) => boolean
  validateForm: (fields: FormField[]) => boolean
}

/**
 * 表单上下文
 */
const FormContext = createContext<FormContextType | undefined>(undefined)

/**
 * 使用表单上下文的Hook
 */
export const useForm = () => {
  const context = useContext(FormContext)
  if (!context) {
    throw new Error('useForm must be used within a Form')
  }
  return context
}

/**
 * 表单组件属性接口
 */
export interface FormProps {
  children: React.ReactNode
  initialData?: FormData
  onSubmit: (data: FormData) => Promise<void> | void
  className?: string
  fields?: FormField[]
}

/**
 * 验证单个字段
 */
const validateFieldValue = (value: FormFieldValue, rules: ValidationRule[] = []): string | null => {
  for (const rule of rules) {
    // 必填验证
    if (rule.required && (!value || (typeof value === 'string' && value.trim() === ''))) {
      return rule.message || '此字段为必填项'
    }

    // 如果值为空且不是必填，跳过其他验证
    if (!value && !rule.required) {
      continue
    }

    const stringValue = String(value)

    // 最小长度验证
    if (rule.minLength && stringValue.length < rule.minLength) {
      return rule.message || `最少需要${rule.minLength}个字符`
    }

    // 最大长度验证
    if (rule.maxLength && stringValue.length > rule.maxLength) {
      return rule.message || `最多允许${rule.maxLength}个字符`
    }

    // 正则表达式验证
    if (rule.pattern && !rule.pattern.test(stringValue)) {
      return rule.message || '格式不正确'
    }

    // 自定义验证
    if (rule.custom) {
      const customError = rule.custom(value)
      if (customError) {
        return customError
      }
    }
  }

  return null
}

/**
 * 表单组件
 */
export const Form: React.FC<FormProps> = ({
  children,
  initialData = {},
  onSubmit,
  className,
  fields = []
}) => {
  const [data, setData] = useState<FormData>(initialData)
  const [errors, setErrors] = useState<FormErrors>({})
  const [isSubmitting, setIsSubmitting] = useState(false)

  const setValue = (name: string, value: FormFieldValue) => {
    setData(prev => ({ ...prev, [name]: value }))
    // 清除该字段的错误
    if (errors[name]) {
      setErrors(prev => {
        const newErrors = { ...prev }
        delete newErrors[name]
        return newErrors
      })
    }
  }

  const setError = (name: string, error: string) => {
    setErrors(prev => ({ ...prev, [name]: error }))
  }

  const clearError = (name: string) => {
    setErrors(prev => {
      const newErrors = { ...prev }
      delete newErrors[name]
      return newErrors
    })
  }

  const validateField = (name: string, rules?: ValidationRule[]): boolean => {
    const fieldRules = rules || fields.find(f => f.name === name)?.rules || []
    const error = validateFieldValue(data[name], fieldRules)
    
    if (error) {
      setError(name, error)
      return false
    } else {
      clearError(name)
      return true
    }
  }

  const validateForm = (formFields: FormField[]): boolean => {
    let isValid = true
    const newErrors: FormErrors = {}

    for (const field of formFields) {
      const error = validateFieldValue(data[field.name], field.rules)
      if (error) {
        newErrors[field.name] = error
        isValid = false
      }
    }

    setErrors(newErrors)
    return isValid
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm(fields)) {
      return
    }

    setIsSubmitting(true)
    try {
      await onSubmit(data)
    } catch (error) {
      console.error('表单提交失败:', error)
    } finally {
      setIsSubmitting(false)
    }
  }

  const contextValue: FormContextType = {
    data,
    errors,
    isSubmitting,
    setValue,
    setError,
    clearError,
    validateField,
    validateForm
  }

  return (
    <FormContext.Provider value={contextValue}>
      <form onSubmit={handleSubmit} className={cn('space-y-4', className)}>
        {children}
      </form>
    </FormContext.Provider>
  )
}

/**
 * 表单字段组件属性接口
 */
export interface FormFieldProps {
  field: FormField
  className?: string
}

/**
 * 表单字段组件 - 优化版
 */
export const FormField: React.FC<FormFieldProps> = React.memo(({ field, className }) => {
  const { data, errors, setValue, validateField } = useForm()
  
  const value = data[field.name] || ''
  const error = errors[field.name]

  // 使用useCallback优化事件处理
  const handleChange = useCallback((e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const newValue = e.target.type === 'checkbox' 
      ? (e.target as HTMLInputElement).checked
      : e.target.value
    setValue(field.name, newValue)
  }, [field.name, setValue])

  const handleBlur = useCallback(() => {
    validateField(field.name, field.rules)
  }, [field.name, field.rules, validateField])

  if (field.type === 'select' && field.options) {
    return (
      <div className={className}>
        {field.label && (
          <label className="block text-sm font-medium text-gray-700 mb-1">
            {field.label}
          </label>
        )}
        <select
          value={String(value)}
          onChange={handleChange}
          onBlur={handleBlur}
          disabled={field.disabled}
          className={cn(
            'flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50',
            error && 'border-red-500 focus-visible:ring-red-500'
          )}
        >
          <option value="">{field.placeholder || '请选择'}</option>
          {field.options.map(option => (
            <option key={option.value} value={option.value}>
              {option.label}
            </option>
          ))}
        </select>
        {error && (
          <p className="mt-1 text-sm text-red-600">{error}</p>
        )}
        {field.helperText && !error && (
          <p className="mt-1 text-sm text-gray-500">{field.helperText}</p>
        )}
      </div>
    )
  }

  if (field.type === 'checkbox') {
    return (
      <div className={cn('flex items-center space-x-2', className)}>
        <input
          type="checkbox"
          id={field.name}
          checked={Boolean(value)}
          onChange={handleChange}
          onBlur={handleBlur}
          disabled={field.disabled}
          className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
        />
        {field.label && (
          <label htmlFor={field.name} className="text-sm font-medium text-gray-700">
            {field.label}
          </label>
        )}
        {error && (
          <p className="text-sm text-red-600">{error}</p>
        )}
      </div>
    )
  }

  return (
    <div className={className}>
      <Input
        type={field.type || 'text'}
        value={String(value)}
        onChange={handleChange}
        onBlur={handleBlur}
        placeholder={field.placeholder}
        disabled={field.disabled}
        label={field.label}
        error={error}
        helperText={field.helperText}
      />
    </div>
  )
})

// 添加displayName用于调试
FormField.displayName = 'FormField'

/**
 * 表单提交按钮组件属性接口
 */
export interface FormSubmitProps {
  children: React.ReactNode
  className?: string
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link'
  disabled?: boolean
}

/**
 * 表单提交按钮组件
 */
export const FormSubmit: React.FC<FormSubmitProps> = ({
  children,
  className,
  variant = 'default',
  disabled = false
}) => {
  const { isSubmitting } = useForm()

  return (
    <Button
      type="submit"
      variant={variant}
      disabled={disabled || isSubmitting}
      loading={isSubmitting}
      className={className}
    >
      {children}
    </Button>
  )
}

/**
 * 快速表单组件属性接口
 */
export interface QuickFormProps {
  fields: FormField[]
  onSubmit: (data: FormData) => Promise<void> | void
  submitText?: string
  className?: string
  initialData?: FormData
}

/**
 * 快速表单组件
 * 根据字段配置自动生成表单
 */
export const QuickForm: React.FC<QuickFormProps> = ({
  fields,
  onSubmit,
  submitText = '提交',
  className,
  initialData
}) => {
  return (
    <Form
      fields={fields}
      onSubmit={onSubmit}
      initialData={initialData}
      className={className}
    >
      {fields.map(field => (
        <FormField key={field.name} field={field} />
      ))}
      <FormSubmit>{submitText}</FormSubmit>
    </Form>
  )
}
