/**
 * 配置系统类型定义
 */
export interface ConfigSchema {
  id: string;
  name: string;
  version: string;
  description: string;
  schema: Record<string, any>;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface ConfigValue {
  key: string;
  value: any;
  type: 'string' | 'number' | 'boolean' | 'object' | 'array';
  encrypted?: boolean;
  lastModified: Date;
}

export interface CacheEntry<T = any> {
  value: T;
  ttl: number;
  createdAt: Date;
}