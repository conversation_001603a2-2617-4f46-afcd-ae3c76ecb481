/**
 * 统一的智能体类型定义
 */
export interface AgentInput {
  [key: string]: any;
}

/**
 * 评估数据接口
 */
export interface AssessmentData {
  id: string;
  responses: Record<string, any>;
  metadata?: Record<string, any>;
  timestamp?: Date;
  userId?: string;
  organizationId?: string;
}

/**
 * 导师选项接口
 */
export interface TutorOptions {
  language?: string;
  style?: 'formal' | 'casual';
  depth?: 'basic' | 'detailed';
  includeRecommendations?: boolean;
  focusAreas?: string[];
}

export interface AgentOutput {
  success: boolean;
  data?: any;
  error?: string;
  metadata?: {
    model?: string;
    temperature?: number;
    tokensUsed?: number;
    processingTime?: number;
    [key: string]: any;
  };
}

export interface AgentStatus {
  name: string;
  initialized: boolean;
  lastActivity: Date;
  config: Record<string, any>;
}

/**
 * 基础智能体接口
 */
export interface BaseAgent {
  name: string;
  initialize(): Promise<void>;
  execute(input: AgentInput): Promise<AgentOutput>;
  getStatus(): AgentStatus;
}

// 问卷设计师专用接口
export interface QuestionnaireDesignInput extends AgentInput {
  assessmentType: string;
  dimensions: string[];
  requirements?: string;
  version: 'standard' | 'professional';
  organizationType?: string;
  industryContext?: string;
}

// 问卷生成选项
export interface QuestionGenerationOptions {
  version: 'standard' | 'professional';
  dimensions: string[];
  questionCount?: number;
  assessmentType?: string;
  organizationType?: string;
  industryContext?: string;
  requirements?: string;
}

// 提示词构建选项
export interface PromptBuildOptions {
  templateId: string;
  variables: Record<string, any>;
  context?: Record<string, any>;
}

// 数据融合配置
export interface DataFusionConfig {
  strategy: 'weighted' | 'priority' | 'consensus';
  threshold: number;
  maxSources: number;
  weights: Record<string, number>;
}

// 原始数据接口
export interface RawData {
  sourceId: string;
  content: string;
  contentType: string;
  metadata?: Record<string, any>;
}

// 组织评估导师专用接口
export interface AssessmentAnalysisInput extends AgentInput {
  assessmentData: AssessmentData;
  options: TutorOptions;
}