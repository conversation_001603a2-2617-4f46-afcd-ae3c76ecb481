// 基础类型定义
export interface BaseEntity {
  id: string
  createdAt: Date
  updatedAt: Date
}

// 配置相关类型
export interface ConfigSchema {
  id: string
  name: string
  version: string
  description?: string
  schema: Record<string, any>
  isActive: boolean
  createdAt: Date
  updatedAt: Date
}

export interface ConfigValue {
  id: string
  configId: string
  key: string
  value: any
  type: 'string' | 'number' | 'boolean' | 'object' | 'array'
  isEncrypted: boolean
  createdAt: Date
  updatedAt: Date
}

// OCTI评估相关类型
export interface OCTIDimension {
  id: string
  name: string
  code: string
  description: string
  weight: number
  poles: OCTIPole[]
}

export interface OCTIPole {
  id: string
  dimensionId: string
  name: string
  code: string
  description: string
  weight: number
  indicators: OCTIIndicator[]
}

export interface OCTIIndicator {
  id: string
  poleId: string
  name: string
  code: string
  description: string
  weight: number
  questions: Question[]
}

// 问卷相关类型
export interface Questionnaire {
  id: string
  title: string
  description?: string
  version: string
  isActive: boolean
  questions: Question[]
  createdAt: Date
  updatedAt: Date
}

export interface Question {
  id: string
  questionnaireId: string
  indicatorId?: string
  type: QuestionType
  title: string
  description?: string
  required: boolean
  order: number
  options?: QuestionOption[]
  validation?: QuestionValidation
  createdAt: Date
  updatedAt: Date
}

export type QuestionType = 
  | 'single_choice'
  | 'multiple_choice'
  | 'text'
  | 'number'
  | 'scale'
  | 'matrix'
  | 'ranking'

export interface QuestionOption {
  id: string
  questionId: string
  text: string
  value: string | number
  order: number
  score?: number
}

export interface QuestionValidation {
  required?: boolean
  minLength?: number
  maxLength?: number
  min?: number
  max?: number
  pattern?: string
  customMessage?: string
}

// 评估相关类型
export interface Assessment extends BaseEntity {
  title: string
  description?: string
  questionnaireId: string
  organizationId: string
  status: AssessmentStatus
  startDate: Date
  endDate?: Date
  responses: AssessmentResponse[]
  results?: AssessmentResult
}

export type AssessmentStatus = 
  | 'draft'
  | 'active'
  | 'completed'
  | 'cancelled'
  | 'archived'

export interface AssessmentResponse extends BaseEntity {
  assessmentId: string
  questionId: string
  respondentId: string
  answer: any
  dimension: string
  timeSpent?: number
  confidence?: number
  timestamp?: string
  score?: number
}

export interface AssessmentResult extends BaseEntity {
  assessmentId: string
  overallScore: number
  dimensionScores: DimensionScore[]
  poleScores: PoleScore[]
  insights: string[]
  recommendations: string[]
  generatedAt: Date
}

export interface DimensionScore {
  dimensionId: string
  dimensionName: string
  score: number
  maxScore: number
  percentage: number
}

export interface PoleScore {
  poleId: string
  poleName: string
  dimensionId: string
  score: number
  maxScore: number
  percentage: number
}

// 组织相关类型
export interface Organization extends BaseEntity {
  name: string
  description?: string
  industry?: string
  size?: OrganizationSize
  location?: string
  contactEmail: string
  isActive: boolean
}

export type OrganizationSize = 
  | 'startup'
  | 'small'
  | 'medium'
  | 'large'
  | 'enterprise'

// 用户相关类型
export interface User extends BaseEntity {
  email: string
  name: string
  role: UserRole
  organizationId?: string
  isActive: boolean
  lastLoginAt?: Date
}

export type UserRole = 
  | 'admin'
  | 'manager'
  | 'analyst'
  | 'respondent'

// API相关类型
export interface APIResponse<T = any> {
  success: boolean
  data?: T
  error?: string
  message?: string
  timestamp: string
}

export interface PaginatedResponse<T> extends APIResponse<T[]> {
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
}

// 智能体相关类型
export interface AgentConfig {
  id: string
  name: string
  type: AgentType
  model: string
  apiKey: string
  baseUrl?: string
  temperature?: number
  maxTokens?: number
  systemPrompt?: string
  isActive: boolean
  createdAt: Date
  updatedAt: Date
}

export type AgentType = 
  | 'questionnaire_designer'
  | 'assessment_mentor'
  | 'report_generator'

export interface AgentRequest {
  agentType: AgentType
  prompt: string
  context?: Record<string, any>
  options?: {
    temperature?: number
    maxTokens?: number
    stream?: boolean
  }
}

export interface AgentResponse {
  success: boolean
  content: string
  usage?: {
    promptTokens: number
    completionTokens: number
    totalTokens: number
  }
  error?: string
}

// 缓存相关类型
export interface CacheConfig {
  ttl: number
  maxSize?: number
  strategy: 'lru' | 'fifo' | 'ttl'
}

export interface CacheEntry<T = any> {
  key: string
  value: T
  ttl: number
  createdAt: Date
  accessedAt: Date
}

// 日志相关类型
export interface LogEntry {
  level: 'debug' | 'info' | 'warn' | 'error'
  message: string
  timestamp: Date
  context?: Record<string, any>
  userId?: string
  requestId?: string
}

// 表单相关类型
export interface FormField {
  name: string
  label: string
  type: string
  required?: boolean
  placeholder?: string
  validation?: any
  options?: Array<{ label: string; value: any }>
}

export interface FormState {
  values: Record<string, any>
  errors: Record<string, string>
  touched: Record<string, boolean>
  isSubmitting: boolean
  isValid: boolean
}

// 主题相关类型
export interface ThemeConfig {
  mode: 'light' | 'dark' | 'system'
  primaryColor: string
  secondaryColor: string
  fontFamily: string
  borderRadius: string
}

// 问卷配置驱动类型
export interface QuestionnaireConfig {
  version: string
  total_questions: number
  dimensions: {
    SF: { questions: ConfigQuestion[] }
    IT: { questions: ConfigQuestion[] }
    MV: { questions: ConfigQuestion[] }
    AD: { questions: ConfigQuestion[] }
  }
}

export interface ConfigQuestion {
  id: string
  dimension: 'SF' | 'IT' | 'MV' | 'AD'
  sub_dimension: string
  type: 'choice' | 'scenario' | 'ranking' | 'scale'
  text: string
  options: ConfigQuestionOption[]
  scoring: {
    dimension_weight: number
    sub_dimension_weight: number
    option_scores: number[]
    reverse_scoring: boolean
  }
}

export interface ConfigQuestionOption {
  id: string
  text: string
  value: string | number
  score: number
}

// 问卷渲染引擎类型
export interface QuestionnaireRenderer {
  config: QuestionnaireConfig
  currentQuestion: number
  responses: QuestionResponse[]
  isComplete: boolean
  progress: number
}

// 单个问题回答
export interface QuestionResponse {
  questionId: string
  answer: any
  score?: number
  timestamp: Date
}

// 完整问卷回答
export interface QuestionnaireResponse {
  id: string
  questionnaireId: string
  organizationId?: string
  userId?: string
  responses: AssessmentResponse[]
  metadata: {
    startTime: string
    endTime: string
    totalTimeSpent: number
    submittedAt: string
    ipAddress?: string
    version: string
    deviceInfo?: {
      userAgent?: string
      screenResolution?: string
      timezone?: string
    }
    completionRate: number
  }
  status: 'draft' | 'completed' | 'submitted'
  createdAt: string
  updatedAt: string
}

// 问卷组件类型
export interface QuestionComponentProps {
  question: ConfigQuestion
  value?: any
  onChange: (value: any) => void
  onNext?: () => void
  onPrevious?: () => void
  isFirst?: boolean
  isLast?: boolean
  disabled?: boolean
}

// OCTI维度配置类型
export interface OCTIDimensionConfig {
  SF: {
    name: string
    sub_dimensions: {
      positioning_clarity: string
      professional_depth: string
      competitive_positioning: string
      development_direction: string
      resource_focus: string
    }
  }
  IT: {
    name: string
    sub_dimensions: {
      decision_mode: string
      talent_dependency: string
      collaboration_mechanism: string
      knowledge_management: string
      organizational_culture: string
    }
  }
  MV: {
    name: string
    sub_dimensions: {
      motivation_source: string
      success_definition: string
      resource_attitude: string
      communication_strategy: string
      long_term_vision: string
    }
  }
  AD: {
    name: string
    sub_dimensions: {
      adaptation_strategy: string
      standardization_level: string
      learning_mode: string
      capacity_building: string
      innovation_frequency: string
    }
  }
}

// 评估报告类型
export interface AssessmentReport {
  id: string
  assessmentId: string
  version: 'standard' | 'professional'
  sections: ReportSection[]
  generatedAt: Date
  organizationName: string
  radarChart?: RadarChartData
}

export interface ReportSection {
  id: string
  title: string
  content: string
  type: 'text' | 'chart' | 'table' | 'list'
  data?: any
  order: number
}

export interface RadarChartData {
  dimensions: {
    SF: number
    IT: number
    MV: number
    AD: number
  }
  maxScore: number
  benchmarks?: {
    industry?: number[]
    size?: number[]
  }
}

// 导出所有类型
// 注意：这些模块将在后续开发阶段创建
// export * from './api'
// export * from './config'
// export * from './assessment'