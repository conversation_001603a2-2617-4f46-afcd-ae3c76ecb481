{"/_not-found": "app/_not-found.js", "/api/agents/route": "app/api/agents/route.js", "/api/assessment/[assessmentId]/dataSource/scrape/route": "app/api/assessment/[assessmentId]/dataSource/scrape/route.js", "/api/assessment/[assessmentId]/dataSource/upload/route": "app/api/assessment/[assessmentId]/dataSource/upload/route.js", "/api/assessments/[id]/analyze/route": "app/api/assessments/[id]/analyze/route.js", "/api/assessments/route": "app/api/assessments/route.js", "/api/auth/login/route": "app/api/auth/login/route.js", "/api/auth/register/route": "app/api/auth/register/route.js", "/api/config/route": "app/api/config/route.js", "/api/assessment/evaluate/route": "app/api/assessment/evaluate/route.js", "/api/questionnaire/generate/route": "app/api/questionnaire/generate/route.js", "/api/reports/[id]/export/route": "app/api/reports/[id]/export/route.js", "/api/questionnaire/submit/route": "app/api/questionnaire/submit/route.js", "/api/reports/[id]/route": "app/api/reports/[id]/route.js", "/api/system/route": "app/api/system/route.js", "/api/test/llm/route": "app/api/test/llm/route.js", "/questionnaire/page": "app/questionnaire/page.js", "/report/page": "app/report/page.js", "/page": "app/page.js", "/test/page": "app/test/page.js", "/test/api/page": "app/test/api/page.js"}