(()=>{var e={};e.id=931,e.ids=[931],e.modules={7849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},5403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},4749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},2044:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>a.a,__next_app__:()=>m,originalPathname:()=>x,pages:()=>o,routeModule:()=>u,tree:()=>d});var r=t(482),l=t(9108),n=t(2563),a=t.n(n),i=t(8300),c={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>i[e]);t.d(s,c);let d=["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,1136)),"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/page.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,1342)),"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,9361,23)),"next/dist/client/components/not-found-error"]}],o=["/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/page.tsx"],x="/page",m={require:t,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:l.x.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},5534:(e,s,t)=>{Promise.resolve().then(t.t.bind(t,2583,23)),Promise.resolve().then(t.t.bind(t,6840,23)),Promise.resolve().then(t.t.bind(t,8771,23)),Promise.resolve().then(t.t.bind(t,3225,23)),Promise.resolve().then(t.t.bind(t,9295,23)),Promise.resolve().then(t.t.bind(t,3982,23))},3155:()=>{},6891:(e,s,t)=>{Promise.resolve().then(t.bind(t,6906))},6906:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>P});var r=t(2295),l=t(3729),n=t.n(l),a=t(9956),i=t(4034),c=t(8563),d=t(1453);let o=(0,l.createContext)(void 0),x=()=>{let e=(0,l.useContext)(o);if(!e)throw Error("useDialog must be used within a Dialog");return e},m=({children:e,open:s,onOpenChange:t,defaultOpen:n=!1})=>{let[a,i]=(0,l.useState)(n);return r.jsx(o.Provider,{value:{open:void 0!==s?s:a,setOpen:e=>{void 0===s&&i(e),t?.(e)}},children:e})},u=({children:e,asChild:s=!1})=>{let{setOpen:t}=x();return s&&n().isValidElement(e)?n().cloneElement(e,{onClick:s=>{let r=e.props.onClick;r?.(s),t(!0)}}):r.jsx(i.z,{onClick:()=>t(!0),children:e})},h={sm:"max-w-sm",md:"max-w-md",lg:"max-w-lg",xl:"max-w-xl",full:"max-w-full mx-4"},p=({children:e,className:s,size:t="md",showClose:n=!0,onEscapeKeyDown:a,onPointerDownOutside:i})=>{let{open:c,setOpen:o}=x();return((0,l.useEffect)(()=>{let e=e=>{"Escape"!==e.key||(a?.(e),e.defaultPrevented||o(!1))};return c&&(document.addEventListener("keydown",e),document.body.style.overflow="hidden"),()=>{document.removeEventListener("keydown",e),document.body.style.overflow="unset"}},[c,a,o]),c)?(0,r.jsxs)("div",{className:"fixed inset-0 z-50",children:[r.jsx("div",{className:"fixed inset-0 bg-black/50 backdrop-blur-sm",onClick:e=>{if(e.target===e.currentTarget){let e=new PointerEvent("pointerdown",{bubbles:!0,cancelable:!0});i?.(e),e.defaultPrevented||o(!1)}}}),r.jsx("div",{className:"fixed inset-0 flex items-center justify-center p-4",children:(0,r.jsxs)("div",{className:(0,d.cn)("relative w-full bg-background rounded-lg shadow-lg border",h[t],s),onClick:e=>e.stopPropagation(),children:[n&&(0,r.jsxs)("button",{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none",onClick:()=>o(!1),children:[r.jsx("svg",{className:"h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})}),r.jsx("span",{className:"sr-only",children:"关闭"})]}),e]})})]}):null},f=({children:e,className:s})=>r.jsx("div",{className:(0,d.cn)("flex flex-col space-y-1.5 text-center sm:text-left p-6 pb-0",s),children:e}),j=({children:e,className:s})=>r.jsx("h2",{className:(0,d.cn)("text-lg font-semibold leading-none tracking-tight",s),children:e});var g=t(3337);let b=(0,l.createContext)(void 0),v=()=>{let e=(0,l.useContext)(b);if(!e)throw Error("useForm must be used within a Form");return e},y=(e,s=[])=>{for(let t of s){if(t.required&&(!e||"string"==typeof e&&""===e.trim()))return t.message||"此字段为必填项";if(!e&&!t.required)continue;let s=String(e);if(t.minLength&&s.length<t.minLength)return t.message||`最少需要${t.minLength}个字符`;if(t.maxLength&&s.length>t.maxLength)return t.message||`最多允许${t.maxLength}个字符`;if(t.pattern&&!t.pattern.test(s))return t.message||"格式不正确";if(t.custom){let s=t.custom(e);if(s)return s}}return null},N=({children:e,initialData:s={},onSubmit:t,className:n,fields:a=[]})=>{let[i,c]=(0,l.useState)(s),[o,x]=(0,l.useState)({}),[m,u]=(0,l.useState)(!1),h=(e,s)=>{x(t=>({...t,[e]:s}))},p=e=>{x(s=>{let t={...s};return delete t[e],t})},f=e=>{let s=!0,t={};for(let r of e){let e=y(i[r.name],r.rules);e&&(t[r.name]=e,s=!1)}return x(t),s},j=async e=>{if(e.preventDefault(),f(a)){u(!0);try{await t(i)}catch(e){console.error("表单提交失败:",e)}finally{u(!1)}}};return r.jsx(b.Provider,{value:{data:i,errors:o,isSubmitting:m,setValue:(e,s)=>{c(t=>({...t,[e]:s})),o[e]&&x(s=>{let t={...s};return delete t[e],t})},setError:h,clearError:p,validateField:(e,s)=>{let t=s||a.find(s=>s.name===e)?.rules||[],r=y(i[e],t);return r?(h(e,r),!1):(p(e),!0)},validateForm:f},children:r.jsx("form",{onSubmit:j,className:(0,d.cn)("space-y-4",n),children:e})})},w=n().memo(({field:e,className:s})=>{let{data:t,errors:n,setValue:a,validateField:i}=v(),c=t[e.name]||"",o=n[e.name],x=(0,l.useCallback)(s=>{let t="checkbox"===s.target.type?s.target.checked:s.target.value;a(e.name,t)},[e.name,a]),m=(0,l.useCallback)(()=>{i(e.name,e.rules)},[e.name,e.rules,i]);return"select"===e.type&&e.options?(0,r.jsxs)("div",{className:s,children:[e.label&&r.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:e.label}),(0,r.jsxs)("select",{value:String(c),onChange:x,onBlur:m,disabled:e.disabled,className:(0,d.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",o&&"border-red-500 focus-visible:ring-red-500"),children:[r.jsx("option",{value:"",children:e.placeholder||"请选择"}),e.options.map(e=>r.jsx("option",{value:e.value,children:e.label},e.value))]}),o&&r.jsx("p",{className:"mt-1 text-sm text-red-600",children:o}),e.helperText&&!o&&r.jsx("p",{className:"mt-1 text-sm text-gray-500",children:e.helperText})]}):"checkbox"===e.type?(0,r.jsxs)("div",{className:(0,d.cn)("flex items-center space-x-2",s),children:[r.jsx("input",{type:"checkbox",id:e.name,checked:!!c,onChange:x,onBlur:m,disabled:e.disabled,className:"h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"}),e.label&&r.jsx("label",{htmlFor:e.name,className:"text-sm font-medium text-gray-700",children:e.label}),o&&r.jsx("p",{className:"text-sm text-red-600",children:o})]}):r.jsx("div",{className:s,children:r.jsx(g.I,{type:e.type||"text",value:String(c),onChange:x,onBlur:m,placeholder:e.placeholder,disabled:e.disabled,label:e.label,error:o,helperText:e.helperText})})});w.displayName="FormField";let k=({children:e,className:s,variant:t="default",disabled:l=!1})=>{let{isSubmitting:n}=v();return r.jsx(i.z,{type:"submit",variant:t,disabled:l||n,loading:n,className:s,children:e})},C=({fields:e,onSubmit:s,submitText:t="提交",className:l,initialData:n})=>(0,r.jsxs)(N,{fields:e,onSubmit:s,initialData:n,className:l,children:[e.map(e=>r.jsx(w,{field:e},e.name)),r.jsx(k,{children:t})]});function P(){let[e,s]=(0,l.useState)(null),[t,n]=(0,l.useState)([]),[d,o]=(0,l.useState)([]),[x,h]=(0,l.useState)(!0),[g,b]=(0,l.useState)(null);(0,l.useEffect)(()=>{v()},[]);let v=async()=>{try{h(!0),b(null);let[e,t,r]=await Promise.all([fetch("/api/system?action=status"),fetch("/api/agents"),fetch("/api/assessments")]);if(!e.ok||!t.ok||!r.ok)throw Error("加载系统数据失败");let[l,a,i]=await Promise.all([e.json(),t.json(),r.json()]);s(l.data),n(a.data||[]),o(i.data||[])}catch(e){b(e instanceof Error?e.message:"未知错误")}finally{h(!1)}},y=[{name:"title",label:"评估标题",type:"text",placeholder:"请输入评估标题",rules:[{required:!0,message:"评估标题不能为空"},{minLength:2,message:"标题至少需要2个字符"},{maxLength:100,message:"标题不能超过100个字符"}]},{name:"description",label:"评估描述",type:"text",placeholder:"请输入评估描述",rules:[{maxLength:500,message:"描述不能超过500个字符"}]},{name:"type",label:"评估类型",type:"select",options:[{label:"能力评估",value:"capability"},{label:"知识测试",value:"knowledge"},{label:"技能验证",value:"skill"},{label:"综合评估",value:"comprehensive"}],rules:[{required:!0,message:"请选择评估类型"}]}],N=async e=>{try{if(!(await fetch("/api/assessments",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)})).ok)throw Error("创建评估失败");await v()}catch(e){throw console.error("创建评估失败:",e),e}},w=e=>{switch(e){case"healthy":case"active":return"text-green-600";case"warning":case"idle":return"text-yellow-600";case"error":return"text-red-600";default:return"text-gray-600"}},k=e=>{switch(e){case"healthy":return"健康";case"warning":return"警告";case"error":return"错误";case"active":return"活跃";case"idle":return"空闲";case"draft":return"草稿";case"completed":return"已完成";default:return e}};return x?r.jsx("div",{className:"min-h-screen flex items-center justify-center",children:r.jsx(c.gb,{size:"lg",text:"加载系统数据中..."})}):g?r.jsx("div",{className:"min-h-screen flex items-center justify-center",children:(0,r.jsxs)(a.Zb,{className:"max-w-md",children:[(0,r.jsxs)(a.Ol,{children:[r.jsx(a.ll,{className:"text-red-600",children:"加载失败"}),r.jsx(a.SZ,{children:g})]}),r.jsx(a.aY,{children:r.jsx(i.z,{onClick:v,className:"w-full",children:"重新加载"})})]})}):r.jsx("div",{className:"min-h-screen bg-gray-50 p-6",children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto",children:[(0,r.jsxs)("div",{className:"mb-8",children:[r.jsx("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"OCTI 智能评估系统"}),r.jsx("p",{className:"text-gray-600",children:"基于AI的在线能力测试与智能评估平台"}),(0,r.jsxs)("div",{className:"mt-4 flex space-x-4",children:[r.jsx(i.z,{onClick:()=>window.location.href="/questionnaire",size:"lg",className:"bg-blue-600 hover:bg-blue-700",children:"开始OCTI评估"}),r.jsx(i.z,{onClick:()=>window.location.href="/report",size:"lg",variant:"outline",children:"查看报告"})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8",children:[(0,r.jsxs)(a.Zb,{children:[r.jsx(a.Ol,{children:r.jsx(a.ll,{className:"text-lg",children:"系统状态"})}),r.jsx(a.aY,{children:e&&(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[r.jsx("span",{children:"整体健康度"}),r.jsx("span",{className:w(e.health),children:k(e.health)})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("div",{className:"flex justify-between text-sm",children:[r.jsx("span",{children:"配置服务"}),r.jsx("span",{className:e.services?.config?"text-green-600":"text-red-600",children:e.services?.config?"正常":"异常"})]}),(0,r.jsxs)("div",{className:"flex justify-between text-sm",children:[r.jsx("span",{children:"智能体服务"}),r.jsx("span",{className:e.services?.agents?"text-green-600":"text-red-600",children:e.services?.agents?"正常":"异常"})]}),(0,r.jsxs)("div",{className:"flex justify-between text-sm",children:[r.jsx("span",{children:"API服务"}),r.jsx("span",{className:e.services?.api?"text-green-600":"text-red-600",children:e.services?.api?"正常":"异常"})]})]})]})})]}),(0,r.jsxs)(a.Zb,{children:[r.jsx(a.Ol,{children:r.jsx(a.ll,{className:"text-lg",children:"系统指标"})}),r.jsx(a.aY,{children:e&&(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[r.jsx("span",{children:"运行时间"}),r.jsx("span",{className:"text-sm text-gray-600",children:e.metrics?.uptime})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"flex justify-between text-sm mb-1",children:[r.jsx("span",{children:"内存使用"}),(0,r.jsxs)("span",{children:[e.metrics?.memory,"%"]})]}),r.jsx(c.Ex,{value:e.metrics?.memory||0,size:"sm"})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"flex justify-between text-sm mb-1",children:[r.jsx("span",{children:"CPU使用"}),(0,r.jsxs)("span",{children:[e.metrics?.cpu,"%"]})]}),r.jsx(c.Ex,{value:e.metrics?.cpu||0,size:"sm"})]})]})})]}),(0,r.jsxs)(a.Zb,{children:[r.jsx(a.Ol,{children:r.jsx(a.ll,{className:"text-lg",children:"智能体状态"})}),r.jsx(a.aY,{children:(0,r.jsxs)("div",{className:"space-y-2",children:[t.map(e=>(0,r.jsxs)("div",{className:"flex justify-between items-center text-sm",children:[r.jsx("span",{children:e.name}),r.jsx("span",{className:w(e.status),children:k(e.status)})]},e.id)),0===t.length&&r.jsx("p",{className:"text-sm text-gray-500",children:"暂无智能体"})]})})]})]}),(0,r.jsxs)(a.Zb,{children:[r.jsx(a.Ol,{children:(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsxs)("div",{children:[r.jsx(a.ll,{className:"text-xl",children:"评估管理"}),r.jsx(a.SZ,{children:"管理和创建在线评估"})]}),(0,r.jsxs)(m,{children:[r.jsx(u,{asChild:!0,children:r.jsx(i.z,{children:"创建评估"})}),(0,r.jsxs)(p,{children:[r.jsx(f,{children:r.jsx(j,{children:"创建新评估"})}),r.jsx(C,{fields:y,onSubmit:N,submitText:"创建评估"})]})]})]})}),r.jsx(a.aY,{children:d.length>0?r.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:d.map(e=>(0,r.jsxs)(a.Zb,{variant:"outlined",children:[(0,r.jsxs)(a.Ol,{children:[r.jsx(a.ll,{className:"text-lg",children:e.title}),r.jsx(a.SZ,{children:e.description})]}),(0,r.jsxs)(a.aY,{children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("div",{className:"flex justify-between text-sm",children:[r.jsx("span",{children:"状态"}),r.jsx("span",{className:w(e.status),children:k(e.status)})]}),(0,r.jsxs)("div",{className:"flex justify-between text-sm",children:[r.jsx("span",{children:"完成数"}),r.jsx("span",{children:e.completedCount})]}),(0,r.jsxs)("div",{className:"flex justify-between text-sm",children:[r.jsx("span",{children:"题目数"}),r.jsx("span",{children:e.totalQuestions})]}),(0,r.jsxs)("div",{className:"flex justify-between text-sm",children:[r.jsx("span",{children:"创建时间"}),r.jsx("span",{children:new Date(e.createdAt).toLocaleDateString()})]})]}),(0,r.jsxs)("div",{className:"mt-4 flex space-x-2",children:[r.jsx(i.z,{size:"sm",variant:"outline",className:"flex-1",children:"编辑"}),r.jsx(i.z,{size:"sm",className:"flex-1",children:"查看"})]})]})]},e.id))}):(0,r.jsxs)("div",{className:"text-center py-8",children:[r.jsx("p",{className:"text-gray-500 mb-4",children:"暂无评估项目"}),(0,r.jsxs)(m,{children:[r.jsx(u,{asChild:!0,children:r.jsx(i.z,{variant:"outline",children:"创建第一个评估"})}),(0,r.jsxs)(p,{children:[r.jsx(f,{children:r.jsx(j,{children:"创建新评估"})}),r.jsx(C,{fields:y,onSubmit:N,submitText:"创建评估"})]})]})]})})]})]})})}},4034:(e,s,t)=>{"use strict";t.d(s,{z:()=>d});var r=t(2295),l=t(3729),n=t.n(l),a=t(1453);let i={default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},c={default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"},d=n().forwardRef(({className:e,variant:s="default",size:t="default",loading:l=!1,disabled:n,children:d,...o},x)=>(0,r.jsxs)("button",{className:(0,a.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",i[s],c[t],e),ref:x,disabled:n||l,...o,children:[l&&(0,r.jsxs)("svg",{className:"mr-2 h-4 w-4 animate-spin",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[r.jsx("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),r.jsx("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),d]}));d.displayName="Button"},9956:(e,s,t)=>{"use strict";t.d(s,{Ol:()=>d,SZ:()=>x,Zb:()=>c,aY:()=>m,ll:()=>o});var r=t(2295),l=t(3729),n=t.n(l),a=t(1453);let i={default:"bg-card text-card-foreground",outlined:"bg-card text-card-foreground border border-border",elevated:"bg-card text-card-foreground shadow-lg"},c=n().forwardRef(({className:e,variant:s="default",...t},l)=>r.jsx("div",{ref:l,className:(0,a.cn)("rounded-lg border shadow-sm",i[s],e),...t}));c.displayName="Card";let d=n().forwardRef(({className:e,...s},t)=>r.jsx("div",{ref:t,className:(0,a.cn)("flex flex-col space-y-1.5 p-6",e),...s}));d.displayName="CardHeader";let o=n().forwardRef(({className:e,...s},t)=>r.jsx("h3",{ref:t,className:(0,a.cn)("text-2xl font-semibold leading-none tracking-tight",e),...s}));o.displayName="CardTitle";let x=n().forwardRef(({className:e,...s},t)=>r.jsx("p",{ref:t,className:(0,a.cn)("text-sm text-muted-foreground",e),...s}));x.displayName="CardDescription";let m=n().forwardRef(({className:e,...s},t)=>r.jsx("div",{ref:t,className:(0,a.cn)("p-6 pt-0",e),...s}));m.displayName="CardContent",n().forwardRef(({className:e,...s},t)=>r.jsx("div",{ref:t,className:(0,a.cn)("flex items-center p-6 pt-0",e),...s})).displayName="CardFooter"},3337:(e,s,t)=>{"use strict";t.d(s,{I:()=>i});var r=t(2295),l=t(3729),n=t.n(l),a=t(1453);let i=n().forwardRef(({className:e,type:s="text",error:t,label:l,helperText:n,id:i,...c},d)=>{let o=i||`input-${Math.random().toString(36).substring(2,9)}`;return(0,r.jsxs)("div",{className:"w-full",children:[l&&r.jsx("label",{htmlFor:o,className:"block text-sm font-medium text-gray-700 mb-1",children:l}),r.jsx("input",{type:s,id:o,className:(0,a.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",t&&"border-red-500 focus-visible:ring-red-500",e),ref:d,...c}),t&&r.jsx("p",{className:"mt-1 text-sm text-red-600",children:t}),n&&!t&&r.jsx("p",{className:"mt-1 text-sm text-gray-500",children:n})]})});i.displayName="Input"},8563:(e,s,t)=>{"use strict";t.d(s,{Ex:()=>c,gb:()=>i});var r=t(2295);t(3729);var l=t(1453);let n={sm:"h-4 w-4",md:"h-6 w-6",lg:"h-8 w-8"},a=({size:e,className:s})=>(0,r.jsxs)("svg",{className:(0,l.cn)("animate-spin",n[e],s),xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[r.jsx("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),r.jsx("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),i=({size:e="md",className:s,text:t,overlay:n=!1})=>{let i=(0,r.jsxs)("div",{className:(0,l.cn)("flex items-center justify-center",t&&"flex-col space-y-2",!t&&"space-x-2",s),children:[r.jsx(a,{size:e,className:"text-primary"}),t&&r.jsx("span",{className:"text-sm text-muted-foreground",children:t})]});return n?r.jsx("div",{className:"fixed inset-0 z-50 flex items-center justify-center bg-background/80 backdrop-blur-sm",children:i}):i},c=({value:e,max:s=100,className:t,showLabel:n=!1,size:a="md"})=>{let i=Math.min(Math.max(e/s*100,0),100);return(0,r.jsxs)("div",{className:(0,l.cn)("w-full",t),children:[n&&(0,r.jsxs)("div",{className:"flex justify-between text-sm text-muted-foreground mb-1",children:[r.jsx("span",{children:"进度"}),(0,r.jsxs)("span",{children:[Math.round(i),"%"]})]}),r.jsx("div",{className:(0,l.cn)("w-full bg-gray-200 rounded-full overflow-hidden",{sm:"h-1",md:"h-2",lg:"h-3"}[a]),children:r.jsx("div",{className:"h-full bg-primary transition-all duration-300 ease-out",style:{width:`${i}%`}})})]})}},1453:(e,s,t)=>{"use strict";t.d(s,{cn:()=>n});var r=t(6553),l=t(9976);function n(...e){return(0,l.m6)((0,r.W)(e))}},1342:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>i,metadata:()=>a});var r=t(5036),l=t(2195),n=t.n(l);t(5023);let a={title:"OCTI智能评估系统",description:"基于OCTI四维八极理论的智能组织评估平台",keywords:["OCTI","组织评估","智能评估","四维八极"],authors:[{name:"OCTI Team"}],viewport:"width=device-width, initial-scale=1",robots:"index, follow",openGraph:{title:"OCTI智能评估系统",description:"基于OCTI四维八极理论的智能组织评估平台",type:"website",locale:"zh_CN"}};function i({children:e}){return r.jsx("html",{lang:"zh-CN",className:"h-full",children:r.jsx("body",{className:`${n().className} h-full antialiased`,children:r.jsx("div",{id:"root",className:"min-h-full",children:e})})})}},1136:(e,s,t)=>{"use strict";t.r(s),t.d(s,{$$typeof:()=>n,__esModule:()=>l,default:()=>a});let r=(0,t(6843).createProxy)(String.raw`/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/page.tsx`),{__esModule:l,$$typeof:n}=r,a=r.default},5023:()=>{}};var s=require("../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[638,883,844],()=>t(2044));module.exports=r})();