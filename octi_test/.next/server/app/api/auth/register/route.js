"use strict";(()=>{var e={};e.id=2,e.ids=[2],e.modules={517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},6113:e=>{e.exports=require("crypto")},8870:(e,r,t)=>{t.r(r),t.d(r,{headerHooks:()=>j,originalPathname:()=>E,patchFetch:()=>w,requestAsyncStorage:()=>g,routeModule:()=>h,serverHooks:()=>f,staticGenerationAsyncStorage:()=>_,staticGenerationBailout:()=>A});var a={};t.r(a),t.d(a,{POST:()=>R});var s=t(5419),o=t(9108),n=t(9678),i=t(8070),u=t(5252),c=t(2178),l=t(7529),p=t(9631),d=t(9413);let m=u.Ry({email:u.Z_().email("邮箱格式无效"),password:u.Z_().min(8,"密码至少8位").max(100,"密码不能超过100位"),name:u.Z_().min(1,"姓名不能为空").max(50,"姓名不能超过50字符"),organizationName:u.Z_().optional(),role:u.Km(["USER","ADMIN"]).default("USER")});async function R(e){try{let r=await e.json(),t=m.parse(r);if(await p._.user.findUnique({where:{email:t.email}}))return i.Z.json({success:!1,error:{code:"EMAIL_EXISTS",message:"该邮箱已被注册"}},{status:400});let a=await (0,l.vp)(t.password,12),s=await p._.user.create({data:{email:t.email,password:a,name:t.name,role:t.role},select:{id:!0,email:!0,name:!0,role:!0,createdAt:!0}});return(0,d.Xj)({user:s,message:"用户注册成功"},201)}catch(e){if(e instanceof c.jm)return(0,d.sm)(e);return console.error("用户注册失败:",e),(0,d.Vd)("注册失败，请稍后重试")}}let h=new s.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/auth/register/route",pathname:"/api/auth/register",filename:"route",bundlePath:"app/api/auth/register/route"},resolvedPagePath:"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/api/auth/register/route.ts",nextConfigOutput:"",userland:a}),{requestAsyncStorage:g,staticGenerationAsyncStorage:_,serverHooks:f,headerHooks:j,staticGenerationBailout:A}=h,E="/api/auth/register/route";function w(){return(0,n.patchFetch)({serverHooks:f,staticGenerationAsyncStorage:_})}},9413:(e,r,t)=>{t.d(r,{VR:()=>i,Vd:()=>n,Xj:()=>s,sm:()=>o});var a=t(8070);function s(e,r=200){return a.Z.json({success:!0,data:e},{status:r})}function o(e){return a.Z.json({success:!1,error:{code:"VALIDATION_ERROR",message:"请求参数验证失败",details:e.errors}},{status:400})}function n(e="服务器内部错误"){return a.Z.json({success:!1,error:{code:"INTERNAL_ERROR",message:e}},{status:500})}function i(e,r=500){return a.Z.json({success:!1,error:{code:r>=500?"INTERNAL_ERROR":"CLIENT_ERROR",message:e}},{status:r})}},9631:(e,r,t)=>{let a;t.d(r,{_:()=>a}),a=new(require("@prisma/client")).PrismaClient}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[638,206,252,50],()=>t(8870));module.exports=a})();