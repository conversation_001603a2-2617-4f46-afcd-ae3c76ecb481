(()=>{var e={};e.id=873,e.ids=[873],e.modules={517:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},4300:e=>{"use strict";e.exports=require("buffer")},6113:e=>{"use strict";e.exports=require("crypto")},2781:e=>{"use strict";e.exports=require("stream")},3837:e=>{"use strict";e.exports=require("util")},6205:(e,r,t)=>{"use strict";t.r(r),t.d(r,{headerHooks:()=>b,originalPathname:()=>$,patchFetch:()=>R,requestAsyncStorage:()=>y,routeModule:()=>E,serverHooks:()=>v,staticGenerationAsyncStorage:()=>g,staticGenerationBailout:()=>S});var s={};t.r(s),t.d(s,{POST:()=>d});var i=t(5419),n=t(9108),o=t(9678),a=t(8070),l=t(5252),u=t(2178),c=t(7529),p=t(6082),f=t(9631),h=t(9413);let m=l.Ry({email:l.Z_().email("邮箱格式无效"),password:l.Z_().min(1,"密码不能为空")});async function d(e){try{let r=await e.json(),t=m.parse(r),s=await f._.user.findUnique({where:{email:t.email}});if(!s||!await (0,c.qu)(t.password,s.password))return a.Z.json({success:!1,error:{code:"INVALID_CREDENTIALS",message:"邮箱或密码错误"}},{status:401});let i=(0,p.sign)({sub:s.id,email:s.email,role:s.role},process.env.JWT_SECRET,{expiresIn:"7d"});return(0,h.Xj)({token:i,user:{id:s.id,email:s.email,name:s.name,role:s.role}})}catch(e){if(e instanceof u.jm)return(0,h.sm)(e);return console.error("用户登录失败:",e),(0,h.Vd)("登录失败，请稍后重试")}}let E=new i.AppRouteRouteModule({definition:{kind:n.x.APP_ROUTE,page:"/api/auth/login/route",pathname:"/api/auth/login",filename:"route",bundlePath:"app/api/auth/login/route"},resolvedPagePath:"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/api/auth/login/route.ts",nextConfigOutput:"",userland:s}),{requestAsyncStorage:y,staticGenerationAsyncStorage:g,serverHooks:v,headerHooks:b,staticGenerationBailout:S}=E,$="/api/auth/login/route";function R(){return(0,o.patchFetch)({serverHooks:v,staticGenerationAsyncStorage:g})}},9756:(e,r,t)=>{"use strict";var s=t(4300).Buffer,i=t(4300).SlowBuffer;function n(e,r){if(!s.isBuffer(e)||!s.isBuffer(r)||e.length!==r.length)return!1;for(var t=0,i=0;i<e.length;i++)t|=e[i]^r[i];return 0===t}e.exports=n,n.install=function(){s.prototype.equal=i.prototype.equal=function(e){return n(this,e)}};var o=s.prototype.equal,a=i.prototype.equal;n.restore=function(){s.prototype.equal=o,i.prototype.equal=a}},8516:(e,r,t)=>{"use strict";var s=t(9026).Buffer,i=t(3447);function n(e){if(s.isBuffer(e))return e;if("string"==typeof e)return s.from(e,"base64");throw TypeError("ECDSA signature must be a Base64 string or a Buffer")}function o(e,r,t){for(var s=0;r+s<t&&0===e[r+s];)++s;return e[r+s]>=128&&--s,s}e.exports={derToJose:function(e,r){e=n(e);var t=i(r),o=t+1,a=e.length,l=0;if(48!==e[l++])throw Error('Could not find expected "seq"');var u=e[l++];if(129===u&&(u=e[l++]),a-l<u)throw Error('"seq" specified length of "'+u+'", only "'+(a-l)+'" remaining');if(2!==e[l++])throw Error('Could not find expected "int" for "r"');var c=e[l++];if(a-l-2<c)throw Error('"r" specified length of "'+c+'", only "'+(a-l-2)+'" available');if(o<c)throw Error('"r" specified length of "'+c+'", max of "'+o+'" is acceptable');var p=l;if(l+=c,2!==e[l++])throw Error('Could not find expected "int" for "s"');var f=e[l++];if(a-l!==f)throw Error('"s" specified length of "'+f+'", expected "'+(a-l)+'"');if(o<f)throw Error('"s" specified length of "'+f+'", max of "'+o+'" is acceptable');var h=l;if((l+=f)!==a)throw Error('Expected to consume entire buffer, but "'+(a-l)+'" bytes remain');var m=t-c,d=t-f,E=s.allocUnsafe(m+c+d+f);for(l=0;l<m;++l)E[l]=0;e.copy(E,l,p+Math.max(-m,0),p+c),l=t;for(var y=l;l<y+d;++l)E[l]=0;return e.copy(E,l,h+Math.max(-d,0),h+f),E=(E=E.toString("base64")).replace(/=/g,"").replace(/\+/g,"-").replace(/\//g,"_")},joseToDer:function(e,r){e=n(e);var t=i(r),a=e.length;if(a!==2*t)throw TypeError('"'+r+'" signatures must be "'+2*t+'" bytes, saw "'+a+'"');var l=o(e,0,t),u=o(e,t,e.length),c=t-l,p=t-u,f=2+c+1+1+p,h=f<128,m=s.allocUnsafe((h?2:3)+f),d=0;return m[d++]=48,h?m[d++]=f:(m[d++]=129,m[d++]=255&f),m[d++]=2,m[d++]=c,l<0?(m[d++]=0,d+=e.copy(m,d,0,t)):d+=e.copy(m,d,l,t),m[d++]=2,m[d++]=p,u<0?(m[d++]=0,e.copy(m,d,t)):e.copy(m,d,t+u),m}}},3447:e=>{"use strict";function r(e){return(e/8|0)+(e%8==0?0:1)}var t={ES256:r(256),ES384:r(384),ES512:r(521)};e.exports=function(e){var r=t[e];if(r)return r;throw Error('Unknown algorithm "'+e+'"')}},6778:(e,r,t)=>{var s=t(4305);e.exports=function(e,r){r=r||{};var t=s.decode(e,r);if(!t)return null;var i=t.payload;if("string"==typeof i)try{var n=JSON.parse(i);null!==n&&"object"==typeof n&&(i=n)}catch(e){}return!0===r.complete?{header:t.header,payload:i,signature:t.signature}:i}},6082:(e,r,t)=>{e.exports={decode:t(6778),verify:t(4588),sign:t(5741),JsonWebTokenError:t(5417),NotBeforeError:t(4865),TokenExpiredError:t(6287)}},5417:e=>{var r=function(e,r){Error.call(this,e),Error.captureStackTrace&&Error.captureStackTrace(this,this.constructor),this.name="JsonWebTokenError",this.message=e,r&&(this.inner=r)};r.prototype=Object.create(Error.prototype),r.prototype.constructor=r,e.exports=r},4865:(e,r,t)=>{var s=t(5417),i=function(e,r){s.call(this,e),this.name="NotBeforeError",this.date=r};i.prototype=Object.create(s.prototype),i.prototype.constructor=i,e.exports=i},6287:(e,r,t)=>{var s=t(5417),i=function(e,r){s.call(this,e),this.name="TokenExpiredError",this.expiredAt=r};i.prototype=Object.create(s.prototype),i.prototype.constructor=i,e.exports=i},3601:(e,r,t)=>{let s=t(9743);e.exports=s.satisfies(process.version,">=15.7.0")},7562:(e,r,t)=>{var s=t(9743);e.exports=s.satisfies(process.version,"^6.12.0 || >=8.0.0")},1157:(e,r,t)=>{let s=t(9743);e.exports=s.satisfies(process.version,">=16.9.0")},4130:(e,r,t)=>{var s=t(7553);e.exports=function(e,r){var t=r||Math.floor(Date.now()/1e3);if("string"==typeof e){var i=s(e);if(void 0===i)return;return Math.floor(t+i/1e3)}if("number"==typeof e)return t+e}},2877:(e,r,t)=>{let s=t(3601),i=t(1157),n={ec:["ES256","ES384","ES512"],rsa:["RS256","PS256","RS384","PS384","RS512","PS512"],"rsa-pss":["PS256","PS384","PS512"]},o={ES256:"prime256v1",ES384:"secp384r1",ES512:"secp521r1"};e.exports=function(e,r){if(!e||!r)return;let t=r.asymmetricKeyType;if(!t)return;let a=n[t];if(!a)throw Error(`Unknown key type "${t}".`);if(!a.includes(e))throw Error(`"alg" parameter for "${t}" key type must be one of: ${a.join(", ")}.`);if(s)switch(t){case"ec":let l=r.asymmetricKeyDetails.namedCurve,u=o[e];if(l!==u)throw Error(`"alg" parameter "${e}" requires curve "${u}".`);break;case"rsa-pss":if(i){let t=parseInt(e.slice(-3),10),{hashAlgorithm:s,mgf1HashAlgorithm:i,saltLength:n}=r.asymmetricKeyDetails;if(s!==`sha${t}`||i!==s)throw Error(`Invalid key for this operation, its RSA-PSS parameters do not meet the requirements of "alg" ${e}.`);if(void 0!==n&&n>t>>3)throw Error(`Invalid key for this operation, its RSA-PSS parameter saltLength does not meet the requirements of "alg" ${e}.`)}}}},5741:(e,r,t)=>{let s=t(4130),i=t(7562),n=t(2877),o=t(4305),a=t(7871),l=t(4709),u=t(593),c=t(3794),p=t(7461),f=t(2841),h=t(3624),{KeyObject:m,createSecretKey:d,createPrivateKey:E}=t(6113),y=["RS256","RS384","RS512","ES256","ES384","ES512","HS256","HS384","HS512","none"];i&&y.splice(3,0,"PS256","PS384","PS512");let g={expiresIn:{isValid:function(e){return u(e)||f(e)&&e},message:'"expiresIn" should be a number of seconds or string representing a timespan'},notBefore:{isValid:function(e){return u(e)||f(e)&&e},message:'"notBefore" should be a number of seconds or string representing a timespan'},audience:{isValid:function(e){return f(e)||Array.isArray(e)},message:'"audience" must be a string or array'},algorithm:{isValid:a.bind(null,y),message:'"algorithm" must be a valid string enum value'},header:{isValid:p,message:'"header" must be an object'},encoding:{isValid:f,message:'"encoding" must be a string'},issuer:{isValid:f,message:'"issuer" must be a string'},subject:{isValid:f,message:'"subject" must be a string'},jwtid:{isValid:f,message:'"jwtid" must be a string'},noTimestamp:{isValid:l,message:'"noTimestamp" must be a boolean'},keyid:{isValid:f,message:'"keyid" must be a string'},mutatePayload:{isValid:l,message:'"mutatePayload" must be a boolean'},allowInsecureKeySizes:{isValid:l,message:'"allowInsecureKeySizes" must be a boolean'},allowInvalidAsymmetricKeyTypes:{isValid:l,message:'"allowInvalidAsymmetricKeyTypes" must be a boolean'}},v={iat:{isValid:c,message:'"iat" should be a number of seconds'},exp:{isValid:c,message:'"exp" should be a number of seconds'},nbf:{isValid:c,message:'"nbf" should be a number of seconds'}};function b(e,r,t,s){if(!p(t))throw Error('Expected "'+s+'" to be a plain object.');Object.keys(t).forEach(function(i){let n=e[i];if(!n){if(!r)throw Error('"'+i+'" is not allowed in "'+s+'"');return}if(!n.isValid(t[i]))throw Error(n.message)})}let S={audience:"aud",issuer:"iss",subject:"sub",jwtid:"jti"},$=["expiresIn","notBefore","noTimestamp","audience","issuer","subject","jwtid"];e.exports=function(e,r,t,i){var a,l;"function"==typeof t?(i=t,t={}):t=t||{};let u="object"==typeof e&&!Buffer.isBuffer(e),c=Object.assign({alg:t.algorithm||"HS256",typ:u?"JWT":void 0,kid:t.keyid},t.header);function p(e){if(i)return i(e);throw e}if(!r&&"none"!==t.algorithm)return p(Error("secretOrPrivateKey must have a value"));if(null!=r&&!(r instanceof m))try{r=E(r)}catch(e){try{r=d("string"==typeof r?Buffer.from(r):r)}catch(e){return p(Error("secretOrPrivateKey is not valid key material"))}}if(c.alg.startsWith("HS")&&"secret"!==r.type)return p(Error(`secretOrPrivateKey must be a symmetric key when using ${c.alg}`));if(/^(?:RS|PS|ES)/.test(c.alg)){if("private"!==r.type)return p(Error(`secretOrPrivateKey must be an asymmetric key when using ${c.alg}`));if(!t.allowInsecureKeySizes&&!c.alg.startsWith("ES")&&void 0!==r.asymmetricKeyDetails&&r.asymmetricKeyDetails.modulusLength<2048)return p(Error(`secretOrPrivateKey has a minimum key size of 2048 bits for ${c.alg}`))}if(void 0===e)return p(Error("payload is required"));if(u){try{a=e,b(v,!0,a,"payload")}catch(e){return p(e)}t.mutatePayload||(e=Object.assign({},e))}else{let r=$.filter(function(e){return void 0!==t[e]});if(r.length>0)return p(Error("invalid "+r.join(",")+" option for "+typeof e+" payload"))}if(void 0!==e.exp&&void 0!==t.expiresIn)return p(Error('Bad "options.expiresIn" option the payload already has an "exp" property.'));if(void 0!==e.nbf&&void 0!==t.notBefore)return p(Error('Bad "options.notBefore" option the payload already has an "nbf" property.'));try{l=t,b(g,!1,l,"options")}catch(e){return p(e)}if(!t.allowInvalidAsymmetricKeyTypes)try{n(c.alg,r)}catch(e){return p(e)}let f=e.iat||Math.floor(Date.now()/1e3);if(t.noTimestamp?delete e.iat:u&&(e.iat=f),void 0!==t.notBefore){try{e.nbf=s(t.notBefore,f)}catch(e){return p(e)}if(void 0===e.nbf)return p(Error('"notBefore" should be a number of seconds or string representing a timespan eg: "1d", "20h", 60'))}if(void 0!==t.expiresIn&&"object"==typeof e){try{e.exp=s(t.expiresIn,f)}catch(e){return p(e)}if(void 0===e.exp)return p(Error('"expiresIn" should be a number of seconds or string representing a timespan eg: "1d", "20h", 60'))}Object.keys(S).forEach(function(r){let s=S[r];if(void 0!==t[r]){if(void 0!==e[s])return p(Error('Bad "options.'+r+'" option. The payload already has an "'+s+'" property.'));e[s]=t[r]}});let y=t.encoding||"utf8";if("function"==typeof i)i=i&&h(i),o.createSign({header:c,privateKey:r,payload:e,encoding:y}).once("error",i).once("done",function(e){if(!t.allowInsecureKeySizes&&/^(?:RS|PS)/.test(c.alg)&&e.length<256)return i(Error(`secretOrPrivateKey has a minimum key size of 2048 bits for ${c.alg}`));i(null,e)});else{let s=o.sign({header:c,payload:e,secret:r,encoding:y});if(!t.allowInsecureKeySizes&&/^(?:RS|PS)/.test(c.alg)&&s.length<256)throw Error(`secretOrPrivateKey has a minimum key size of 2048 bits for ${c.alg}`);return s}}},4588:(e,r,t)=>{let s=t(5417),i=t(4865),n=t(6287),o=t(6778),a=t(4130),l=t(2877),u=t(7562),c=t(4305),{KeyObject:p,createSecretKey:f,createPublicKey:h}=t(6113),m=["RS256","RS384","RS512"],d=["ES256","ES384","ES512"],E=["RS256","RS384","RS512"],y=["HS256","HS384","HS512"];u&&(m.splice(m.length,0,"PS256","PS384","PS512"),E.splice(E.length,0,"PS256","PS384","PS512")),e.exports=function(e,r,t,u){let g,v,b;if("function"!=typeof t||u||(u=t,t={}),t||(t={}),t=Object.assign({},t),g=u||function(e,r){if(e)throw e;return r},t.clockTimestamp&&"number"!=typeof t.clockTimestamp)return g(new s("clockTimestamp must be a number"));if(void 0!==t.nonce&&("string"!=typeof t.nonce||""===t.nonce.trim()))return g(new s("nonce must be a non-empty string"));if(void 0!==t.allowInvalidAsymmetricKeyTypes&&"boolean"!=typeof t.allowInvalidAsymmetricKeyTypes)return g(new s("allowInvalidAsymmetricKeyTypes must be a boolean"));let S=t.clockTimestamp||Math.floor(Date.now()/1e3);if(!e)return g(new s("jwt must be provided"));if("string"!=typeof e)return g(new s("jwt must be a string"));let $=e.split(".");if(3!==$.length)return g(new s("jwt malformed"));try{v=o(e,{complete:!0})}catch(e){return g(e)}if(!v)return g(new s("invalid token"));let R=v.header;if("function"==typeof r){if(!u)return g(new s("verify must be called asynchronous if secret or public key is provided as a callback"));b=r}else b=function(e,t){return t(null,r)};return b(R,function(r,o){let u;if(r)return g(new s("error in secret or public key callback: "+r.message));let b=""!==$[2].trim();if(!b&&o)return g(new s("jwt signature is required"));if(b&&!o)return g(new s("secret or public key must be provided"));if(!b&&!t.algorithms)return g(new s('please specify "none" in "algorithms" to verify unsigned tokens'));if(null!=o&&!(o instanceof p))try{o=h(o)}catch(e){try{o=f("string"==typeof o?Buffer.from(o):o)}catch(e){return g(new s("secretOrPublicKey is not valid key material"))}}if(t.algorithms||("secret"===o.type?t.algorithms=y:["rsa","rsa-pss"].includes(o.asymmetricKeyType)?t.algorithms=E:"ec"===o.asymmetricKeyType?t.algorithms=d:t.algorithms=m),-1===t.algorithms.indexOf(v.header.alg))return g(new s("invalid algorithm"));if(R.alg.startsWith("HS")&&"secret"!==o.type)return g(new s(`secretOrPublicKey must be a symmetric key when using ${R.alg}`));if(/^(?:RS|PS|ES)/.test(R.alg)&&"public"!==o.type)return g(new s(`secretOrPublicKey must be an asymmetric key when using ${R.alg}`));if(!t.allowInvalidAsymmetricKeyTypes)try{l(R.alg,o)}catch(e){return g(e)}try{u=c.verify(e,v.header.alg,o)}catch(e){return g(e)}if(!u)return g(new s("invalid signature"));let w=v.payload;if(void 0!==w.nbf&&!t.ignoreNotBefore){if("number"!=typeof w.nbf)return g(new s("invalid nbf value"));if(w.nbf>S+(t.clockTolerance||0))return g(new i("jwt not active",new Date(1e3*w.nbf)))}if(void 0!==w.exp&&!t.ignoreExpiration){if("number"!=typeof w.exp)return g(new s("invalid exp value"));if(S>=w.exp+(t.clockTolerance||0))return g(new n("jwt expired",new Date(1e3*w.exp)))}if(t.audience){let e=Array.isArray(t.audience)?t.audience:[t.audience];if(!(Array.isArray(w.aud)?w.aud:[w.aud]).some(function(r){return e.some(function(e){return e instanceof RegExp?e.test(r):e===r})}))return g(new s("jwt audience invalid. expected: "+e.join(" or ")))}if(t.issuer&&("string"==typeof t.issuer&&w.iss!==t.issuer||Array.isArray(t.issuer)&&-1===t.issuer.indexOf(w.iss)))return g(new s("jwt issuer invalid. expected: "+t.issuer));if(t.subject&&w.sub!==t.subject)return g(new s("jwt subject invalid. expected: "+t.subject));if(t.jwtid&&w.jti!==t.jwtid)return g(new s("jwt jwtid invalid. expected: "+t.jwtid));if(t.nonce&&w.nonce!==t.nonce)return g(new s("jwt nonce invalid. expected: "+t.nonce));if(t.maxAge){if("number"!=typeof w.iat)return g(new s("iat required when maxAge is specified"));let e=a(t.maxAge,w.iat);if(void 0===e)return g(new s('"maxAge" should be a number of seconds or string representing a timespan eg: "1d", "20h", 60'));if(S>=e+(t.clockTolerance||0))return g(new n("maxAge exceeded",new Date(1e3*e)))}return!0===t.complete?g(null,{header:R,payload:w,signature:v.signature}):g(null,w)})}},2323:(e,r,t)=>{var s,i=t(9026).Buffer,n=t(6113),o=t(8516),a=t(3837),l="secret must be a string or buffer",u="key must be a string or a buffer",c="function"==typeof n.createPublicKey;function p(e){if(!i.isBuffer(e)&&"string"!=typeof e&&(!c||"object"!=typeof e||"string"!=typeof e.type||"string"!=typeof e.asymmetricKeyType||"function"!=typeof e.export))throw d(u)}function f(e){if(!i.isBuffer(e)&&"string"!=typeof e&&"object"!=typeof e)throw d("key must be a string, a buffer or an object")}function h(e){return e.replace(/=/g,"").replace(/\+/g,"-").replace(/\//g,"_")}function m(e){var r=4-(e=e.toString()).length%4;if(4!==r)for(var t=0;t<r;++t)e+="=";return e.replace(/\-/g,"+").replace(/_/g,"/")}function d(e){var r=[].slice.call(arguments,1);return TypeError(a.format.bind(a,e).apply(null,r))}function E(e){var r;return r=e,i.isBuffer(r)||"string"==typeof r||(e=JSON.stringify(e)),e}function y(e){return function(r,t){(function(e){if(!i.isBuffer(e)&&"string"!=typeof e&&(!c||"object"!=typeof e||"secret"!==e.type||"function"!=typeof e.export))throw d(l)})(t),r=E(r);var s=n.createHmac("sha"+e,t);return h((s.update(r),s.digest("base64")))}}c&&(u+=" or a KeyObject",l+="or a KeyObject");var g="timingSafeEqual"in n?function(e,r){return e.byteLength===r.byteLength&&n.timingSafeEqual(e,r)}:function(e,r){return s||(s=t(9756)),s(e,r)};function v(e){return function(r,t,s){var n=y(e)(r,s);return g(i.from(t),i.from(n))}}function b(e){return function(r,t){f(t),r=E(r);var s=n.createSign("RSA-SHA"+e);return h((s.update(r),s.sign(t,"base64")))}}function S(e){return function(r,t,s){p(s),r=E(r),t=m(t);var i=n.createVerify("RSA-SHA"+e);return i.update(r),i.verify(s,t,"base64")}}function $(e){return function(r,t){f(t),r=E(r);var s=n.createSign("RSA-SHA"+e);return h((s.update(r),s.sign({key:t,padding:n.constants.RSA_PKCS1_PSS_PADDING,saltLength:n.constants.RSA_PSS_SALTLEN_DIGEST},"base64")))}}function R(e){return function(r,t,s){p(s),r=E(r),t=m(t);var i=n.createVerify("RSA-SHA"+e);return i.update(r),i.verify({key:s,padding:n.constants.RSA_PKCS1_PSS_PADDING,saltLength:n.constants.RSA_PSS_SALTLEN_DIGEST},t,"base64")}}function w(e){var r=b(e);return function(){var t=r.apply(null,arguments);return o.derToJose(t,"ES"+e)}}function I(e){var r=S(e);return function(t,s,i){return r(t,s=o.joseToDer(s,"ES"+e).toString("base64"),i)}}function O(){return function(){return""}}function A(){return function(e,r){return""===r}}e.exports=function(e){var r=e.match(/^(RS|PS|ES|HS)(256|384|512)$|^(none)$/i);if(!r)throw d('"%s" is not a valid algorithm.\n  Supported algorithms are:\n  "HS256", "HS384", "HS512", "RS256", "RS384", "RS512", "PS256", "PS384", "PS512", "ES256", "ES384", "ES512" and "none".',e);var t=(r[1]||r[3]).toLowerCase(),s=r[2];return{sign:({hs:y,rs:b,ps:$,es:w,none:O})[t](s),verify:({hs:v,rs:S,ps:R,es:I,none:A})[t](s)}}},4305:(e,r,t)=>{var s=t(7661),i=t(6282);r.ALGORITHMS=["HS256","HS384","HS512","RS256","RS384","RS512","PS256","PS384","PS512","ES256","ES384","ES512"],r.sign=s.sign,r.verify=i.verify,r.decode=i.decode,r.isValid=i.isValid,r.createSign=function(e){return new s(e)},r.createVerify=function(e){return new i(e)}},1498:(e,r,t)=>{var s=t(9026).Buffer,i=t(2781);function n(e){if(this.buffer=null,this.writable=!0,this.readable=!0,!e)return this.buffer=s.alloc(0),this;if("function"==typeof e.pipe)return this.buffer=s.alloc(0),e.pipe(this),this;if(e.length||"object"==typeof e)return this.buffer=e,this.writable=!1,process.nextTick((function(){this.emit("end",e),this.readable=!1,this.emit("close")}).bind(this)),this;throw TypeError("Unexpected data type ("+typeof e+")")}t(3837).inherits(n,i),n.prototype.write=function(e){this.buffer=s.concat([this.buffer,s.from(e)]),this.emit("data",e)},n.prototype.end=function(e){e&&this.write(e),this.emit("end",e),this.emit("close"),this.writable=!1,this.readable=!1},e.exports=n},7661:(e,r,t)=>{var s=t(9026).Buffer,i=t(1498),n=t(2323),o=t(2781),a=t(8190),l=t(3837);function u(e,r){return s.from(e,r).toString("base64").replace(/=/g,"").replace(/\+/g,"-").replace(/\//g,"_")}function c(e){var r,t,s,i=e.header,o=e.payload,c=e.secret||e.privateKey,p=e.encoding,f=n(i.alg),h=(r=(r=p)||"utf8",t=u(a(i),"binary"),s=u(a(o),r),l.format("%s.%s",t,s)),m=f.sign(h,c);return l.format("%s.%s",h,m)}function p(e){var r=new i(e.secret||e.privateKey||e.key);this.readable=!0,this.header=e.header,this.encoding=e.encoding,this.secret=this.privateKey=this.key=r,this.payload=new i(e.payload),this.secret.once("close",(function(){!this.payload.writable&&this.readable&&this.sign()}).bind(this)),this.payload.once("close",(function(){!this.secret.writable&&this.readable&&this.sign()}).bind(this))}l.inherits(p,o),p.prototype.sign=function(){try{var e=c({header:this.header,payload:this.payload.buffer,secret:this.secret.buffer,encoding:this.encoding});return this.emit("done",e),this.emit("data",e),this.emit("end"),this.readable=!1,e}catch(e){this.readable=!1,this.emit("error",e),this.emit("close")}},p.sign=c,e.exports=p},8190:(e,r,t)=>{var s=t(4300).Buffer;e.exports=function(e){return"string"==typeof e?e:"number"==typeof e||s.isBuffer(e)?e.toString():JSON.stringify(e)}},6282:(e,r,t)=>{var s=t(9026).Buffer,i=t(1498),n=t(2323),o=t(2781),a=t(8190),l=t(3837),u=/^[a-zA-Z0-9\-_]+?\.[a-zA-Z0-9\-_]+?\.([a-zA-Z0-9\-_]+)?$/;function c(e){var r=e.split(".",1)[0];return function(e){if("[object Object]"===Object.prototype.toString.call(e))return e;try{return JSON.parse(e)}catch(e){return}}(s.from(r,"base64").toString("binary"))}function p(e){return e.split(".")[2]}function f(e){return u.test(e)&&!!c(e)}function h(e,r,t){if(!r){var s=Error("Missing algorithm parameter for jws.verify");throw s.code="MISSING_ALGORITHM",s}var i=p(e=a(e)),o=e.split(".",2).join(".");return n(r).verify(o,i,t)}function m(e,r){if(r=r||{},!f(e=a(e)))return null;var t,i,n=c(e);if(!n)return null;var o=(t=t||"utf8",i=e.split(".")[1],s.from(i,"base64").toString(t));return("JWT"===n.typ||r.json)&&(o=JSON.parse(o,r.encoding)),{header:n,payload:o,signature:p(e)}}function d(e){var r=new i((e=e||{}).secret||e.publicKey||e.key);this.readable=!0,this.algorithm=e.algorithm,this.encoding=e.encoding,this.secret=this.publicKey=this.key=r,this.signature=new i(e.signature),this.secret.once("close",(function(){!this.signature.writable&&this.readable&&this.verify()}).bind(this)),this.signature.once("close",(function(){!this.secret.writable&&this.readable&&this.verify()}).bind(this))}l.inherits(d,o),d.prototype.verify=function(){try{var e=h(this.signature.buffer,this.algorithm,this.key.buffer),r=m(this.signature.buffer,this.encoding);return this.emit("done",e,r),this.emit("data",e),this.emit("end"),this.readable=!1,e}catch(e){this.readable=!1,this.emit("error",e),this.emit("close")}},d.decode=m,d.isValid=f,d.verify=h,e.exports=d},7871:e=>{var r,t,s=1/0,i=0/0,n=/^\s+|\s+$/g,o=/^[-+]0x[0-9a-f]+$/i,a=/^0b[01]+$/i,l=/^0o[0-7]+$/i,u=/^(?:0|[1-9]\d*)$/,c=parseInt;function p(e){return e!=e}var f=Object.prototype,h=f.hasOwnProperty,m=f.toString,d=f.propertyIsEnumerable,E=(r=Object.keys,t=Object,function(e){return r(t(e))}),y=Math.max,g=Array.isArray;function v(e){var r,t;return null!=e&&"number"==typeof(r=e.length)&&r>-1&&r%1==0&&r<=9007199254740991&&!("[object Function]"==(t=b(e)?m.call(e):"")||"[object GeneratorFunction]"==t)}function b(e){var r=typeof e;return!!e&&("object"==r||"function"==r)}function S(e){return!!e&&"object"==typeof e}e.exports=function(e,r,t,$){e=v(e)?e:(R=e)?function(e,r){for(var t=-1,s=e?e.length:0,i=Array(s);++t<s;)i[t]=r(e[t],t,e);return i}(v(R)?function(e,r){var t,s=g(e)||S(e)&&v(e)&&h.call(e,"callee")&&(!d.call(e,"callee")||"[object Arguments]"==m.call(e))?function(e,r){for(var t=-1,s=Array(e);++t<e;)s[t]=r(t);return s}(e.length,String):[],i=s.length,n=!!i;for(var o in e)h.call(e,o)&&!(n&&("length"==o||(t=null==(t=i)?9007199254740991:t)&&("number"==typeof o||u.test(o))&&o>-1&&o%1==0&&o<t))&&s.push(o);return s}(R):function(e){if(r=e&&e.constructor,e!==("function"==typeof r&&r.prototype||f))return E(e);var r,t=[];for(var s in Object(e))h.call(e,s)&&"constructor"!=s&&t.push(s);return t}(R),function(e){return R[e]}):[],t=t&&!$?(O=(I=(w=t)?(w=function(e){if("number"==typeof e)return e;if("symbol"==typeof(r=e)||S(r)&&"[object Symbol]"==m.call(r))return i;if(b(e)){var r,t="function"==typeof e.valueOf?e.valueOf():e;e=b(t)?t+"":t}if("string"!=typeof e)return 0===e?e:+e;e=e.replace(n,"");var s=a.test(e);return s||l.test(e)?c(e.slice(2),s?2:8):o.test(e)?i:+e}(w))===s||w===-s?(w<0?-1:1)*17976931348623157e292:w==w?w:0:0===w?w:0)%1,I==I?O?I-O:I:0):0;var R,w,I,O,A,T=e.length;return t<0&&(t=y(T+t,0)),"string"==typeof(A=e)||!g(A)&&S(A)&&"[object String]"==m.call(A)?t<=T&&e.indexOf(r,t)>-1:!!T&&function(e,r,t){if(r!=r)return function(e,r,t,s){for(var i=e.length,n=t+(s?1:-1);s?n--:++n<i;)if(r(e[n],n,e))return n;return -1}(e,p,t);for(var s=t-1,i=e.length;++s<i;)if(e[s]===r)return s;return -1}(e,r,t)>-1}},4709:e=>{var r=Object.prototype.toString;e.exports=function(e){return!0===e||!1===e||!!e&&"object"==typeof e&&"[object Boolean]"==r.call(e)}},593:e=>{var r=1/0,t=0/0,s=/^\s+|\s+$/g,i=/^[-+]0x[0-9a-f]+$/i,n=/^0b[01]+$/i,o=/^0o[0-7]+$/i,a=parseInt,l=Object.prototype.toString;function u(e){var r=typeof e;return!!e&&("object"==r||"function"==r)}e.exports=function(e){var c,p,f;return"number"==typeof e&&e==(f=(p=(c=e)?(c=function(e){if("number"==typeof e)return e;if("symbol"==typeof(r=e)||r&&"object"==typeof r&&"[object Symbol]"==l.call(r))return t;if(u(e)){var r,c="function"==typeof e.valueOf?e.valueOf():e;e=u(c)?c+"":c}if("string"!=typeof e)return 0===e?e:+e;e=e.replace(s,"");var p=n.test(e);return p||o.test(e)?a(e.slice(2),p?2:8):i.test(e)?t:+e}(c))===r||c===-r?(c<0?-1:1)*17976931348623157e292:c==c?c:0:0===c?c:0)%1,p==p?f?p-f:p:0)}},3794:e=>{var r=Object.prototype.toString;e.exports=function(e){return"number"==typeof e||!!e&&"object"==typeof e&&"[object Number]"==r.call(e)}},7461:e=>{var r,t,s=Object.prototype,i=Function.prototype.toString,n=s.hasOwnProperty,o=i.call(Object),a=s.toString,l=(r=Object.getPrototypeOf,t=Object,function(e){return r(t(e))});e.exports=function(e){if(!(e&&"object"==typeof e)||"[object Object]"!=a.call(e)||function(e){var r=!1;if(null!=e&&"function"!=typeof e.toString)try{r=!!(e+"")}catch(e){}return r}(e))return!1;var r=l(e);if(null===r)return!0;var t=n.call(r,"constructor")&&r.constructor;return"function"==typeof t&&t instanceof t&&i.call(t)==o}},2841:e=>{var r=Object.prototype.toString,t=Array.isArray;e.exports=function(e){var s;return"string"==typeof e||!t(e)&&!!(s=e)&&"object"==typeof s&&"[object String]"==r.call(e)}},3624:e=>{var r=1/0,t=0/0,s=/^\s+|\s+$/g,i=/^[-+]0x[0-9a-f]+$/i,n=/^0b[01]+$/i,o=/^0o[0-7]+$/i,a=parseInt,l=Object.prototype.toString;function u(e){var r=typeof e;return!!e&&("object"==r||"function"==r)}e.exports=function(e){return function(e,c){var p,f,h,m;if("function"!=typeof c)throw TypeError("Expected a function");return m=(h=(f=e)?(f=function(e){if("number"==typeof e)return e;if("symbol"==typeof(r=e)||r&&"object"==typeof r&&"[object Symbol]"==l.call(r))return t;if(u(e)){var r,c="function"==typeof e.valueOf?e.valueOf():e;e=u(c)?c+"":c}if("string"!=typeof e)return 0===e?e:+e;e=e.replace(s,"");var p=n.test(e);return p||o.test(e)?a(e.slice(2),p?2:8):i.test(e)?t:+e}(f))===r||f===-r?(f<0?-1:1)*17976931348623157e292:f==f?f:0:0===f?f:0)%1,e=h==h?m?h-m:h:0,function(){return--e>0&&(p=c.apply(this,arguments)),e<=1&&(c=void 0),p}}(2,e)}},7553:e=>{function r(e,r,t,s){return Math.round(e/t)+" "+s+(r>=1.5*t?"s":"")}e.exports=function(e,t){t=t||{};var s,i,n=typeof e;if("string"===n&&e.length>0)return function(e){if(!((e=String(e)).length>100)){var r=/^(-?(?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(e);if(r){var t=parseFloat(r[1]);switch((r[2]||"ms").toLowerCase()){case"years":case"year":case"yrs":case"yr":case"y":return 315576e5*t;case"weeks":case"week":case"w":return 6048e5*t;case"days":case"day":case"d":return 864e5*t;case"hours":case"hour":case"hrs":case"hr":case"h":return 36e5*t;case"minutes":case"minute":case"mins":case"min":case"m":return 6e4*t;case"seconds":case"second":case"secs":case"sec":case"s":return 1e3*t;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return t;default:return}}}}(e);if("number"===n&&isFinite(e))return t.long?(s=Math.abs(e))>=864e5?r(e,s,864e5,"day"):s>=36e5?r(e,s,36e5,"hour"):s>=6e4?r(e,s,6e4,"minute"):s>=1e3?r(e,s,1e3,"second"):e+" ms":(i=Math.abs(e))>=864e5?Math.round(e/864e5)+"d":i>=36e5?Math.round(e/36e5)+"h":i>=6e4?Math.round(e/6e4)+"m":i>=1e3?Math.round(e/1e3)+"s":e+"ms";throw Error("val is not a non-empty string or a valid number. val="+JSON.stringify(e))}},9026:(e,r,t)=>{/*! safe-buffer. MIT License. Feross Aboukhadijeh <https://feross.org/opensource> */var s=t(4300),i=s.Buffer;function n(e,r){for(var t in e)r[t]=e[t]}function o(e,r,t){return i(e,r,t)}i.from&&i.alloc&&i.allocUnsafe&&i.allocUnsafeSlow?e.exports=s:(n(s,r),r.Buffer=o),o.prototype=Object.create(i.prototype),n(i,o),o.from=function(e,r,t){if("number"==typeof e)throw TypeError("Argument must not be a number");return i(e,r,t)},o.alloc=function(e,r,t){if("number"!=typeof e)throw TypeError("Argument must be a number");var s=i(e);return void 0!==r?"string"==typeof t?s.fill(r,t):s.fill(r):s.fill(0),s},o.allocUnsafe=function(e){if("number"!=typeof e)throw TypeError("Argument must be a number");return i(e)},o.allocUnsafeSlow=function(e){if("number"!=typeof e)throw TypeError("Argument must be a number");return s.SlowBuffer(e)}},1269:(e,r,t)=>{"use strict";let s=Symbol("SemVer ANY");class i{static get ANY(){return s}constructor(e,r){if(r=n(r),e instanceof i){if(!!r.loose===e.loose)return e;e=e.value}u("comparator",e=e.trim().split(/\s+/).join(" "),r),this.options=r,this.loose=!!r.loose,this.parse(e),this.semver===s?this.value="":this.value=this.operator+this.semver.version,u("comp",this)}parse(e){let r=this.options.loose?o[a.COMPARATORLOOSE]:o[a.COMPARATOR],t=e.match(r);if(!t)throw TypeError(`Invalid comparator: ${e}`);this.operator=void 0!==t[1]?t[1]:"","="===this.operator&&(this.operator=""),t[2]?this.semver=new c(t[2],this.options.loose):this.semver=s}toString(){return this.value}test(e){if(u("Comparator.test",e,this.options.loose),this.semver===s||e===s)return!0;if("string"==typeof e)try{e=new c(e,this.options)}catch(e){return!1}return l(e,this.operator,this.semver,this.options)}intersects(e,r){if(!(e instanceof i))throw TypeError("a Comparator is required");return""===this.operator?""===this.value||new p(e.value,r).test(this.value):""===e.operator?""===e.value||new p(this.value,r).test(e.semver):!((r=n(r)).includePrerelease&&("<0.0.0-0"===this.value||"<0.0.0-0"===e.value)||!r.includePrerelease&&(this.value.startsWith("<0.0.0")||e.value.startsWith("<0.0.0")))&&!!(this.operator.startsWith(">")&&e.operator.startsWith(">")||this.operator.startsWith("<")&&e.operator.startsWith("<")||this.semver.version===e.semver.version&&this.operator.includes("=")&&e.operator.includes("=")||l(this.semver,"<",e.semver,r)&&this.operator.startsWith(">")&&e.operator.startsWith("<")||l(this.semver,">",e.semver,r)&&this.operator.startsWith("<")&&e.operator.startsWith(">"))}}e.exports=i;let n=t(1875),{safeRe:o,t:a}=t(601),l=t(1448),u=t(893),c=t(8340),p=t(8965)},8965:(e,r,t)=>{"use strict";let s=/\s+/g;class i{constructor(e,r){if(r=o(r),e instanceof i){if(!!r.loose===e.loose&&!!r.includePrerelease===e.includePrerelease)return e;return new i(e.raw,r)}if(e instanceof a)return this.raw=e.value,this.set=[[e]],this.formatted=void 0,this;if(this.options=r,this.loose=!!r.loose,this.includePrerelease=!!r.includePrerelease,this.raw=e.trim().replace(s," "),this.set=this.raw.split("||").map(e=>this.parseRange(e.trim())).filter(e=>e.length),!this.set.length)throw TypeError(`Invalid SemVer Range: ${this.raw}`);if(this.set.length>1){let e=this.set[0];if(this.set=this.set.filter(e=>!y(e[0])),0===this.set.length)this.set=[e];else if(this.set.length>1){for(let e of this.set)if(1===e.length&&g(e[0])){this.set=[e];break}}}this.formatted=void 0}get range(){if(void 0===this.formatted){this.formatted="";for(let e=0;e<this.set.length;e++){e>0&&(this.formatted+="||");let r=this.set[e];for(let e=0;e<r.length;e++)e>0&&(this.formatted+=" "),this.formatted+=r[e].toString().trim()}}return this.formatted}format(){return this.range}toString(){return this.range}parseRange(e){let r=((this.options.includePrerelease&&d)|(this.options.loose&&E))+":"+e,t=n.get(r);if(t)return t;let s=this.options.loose,i=s?c[p.HYPHENRANGELOOSE]:c[p.HYPHENRANGE];l("hyphen replace",e=e.replace(i,N(this.options.includePrerelease))),l("comparator trim",e=e.replace(c[p.COMPARATORTRIM],f)),l("tilde trim",e=e.replace(c[p.TILDETRIM],h)),l("caret trim",e=e.replace(c[p.CARETTRIM],m));let o=e.split(" ").map(e=>b(e,this.options)).join(" ").split(/\s+/).map(e=>L(e,this.options));s&&(o=o.filter(e=>(l("loose invalid filter",e,this.options),!!e.match(c[p.COMPARATORLOOSE])))),l("range list",o);let u=new Map;for(let e of o.map(e=>new a(e,this.options))){if(y(e))return[e];u.set(e.value,e)}u.size>1&&u.has("")&&u.delete("");let g=[...u.values()];return n.set(r,g),g}intersects(e,r){if(!(e instanceof i))throw TypeError("a Range is required");return this.set.some(t=>v(t,r)&&e.set.some(e=>v(e,r)&&t.every(t=>e.every(e=>t.intersects(e,r)))))}test(e){if(!e)return!1;if("string"==typeof e)try{e=new u(e,this.options)}catch(e){return!1}for(let r=0;r<this.set.length;r++)if(x(this.set[r],e,this.options))return!0;return!1}}e.exports=i;let n=new(t(4527)),o=t(1875),a=t(1269),l=t(893),u=t(8340),{safeRe:c,t:p,comparatorTrimReplace:f,tildeTrimReplace:h,caretTrimReplace:m}=t(601),{FLAG_INCLUDE_PRERELEASE:d,FLAG_LOOSE:E}=t(7943),y=e=>"<0.0.0-0"===e.value,g=e=>""===e.value,v=(e,r)=>{let t=!0,s=e.slice(),i=s.pop();for(;t&&s.length;)t=s.every(e=>i.intersects(e,r)),i=s.pop();return t},b=(e,r)=>(l("comp",e,r),l("caret",e=w(e,r)),l("tildes",e=$(e,r)),l("xrange",e=O(e,r)),l("stars",e=T(e,r)),e),S=e=>!e||"x"===e.toLowerCase()||"*"===e,$=(e,r)=>e.trim().split(/\s+/).map(e=>R(e,r)).join(" "),R=(e,r)=>{let t=r.loose?c[p.TILDELOOSE]:c[p.TILDE];return e.replace(t,(r,t,s,i,n)=>{let o;return l("tilde",e,r,t,s,i,n),S(t)?o="":S(s)?o=`>=${t}.0.0 <${+t+1}.0.0-0`:S(i)?o=`>=${t}.${s}.0 <${t}.${+s+1}.0-0`:n?(l("replaceTilde pr",n),o=`>=${t}.${s}.${i}-${n} <${t}.${+s+1}.0-0`):o=`>=${t}.${s}.${i} <${t}.${+s+1}.0-0`,l("tilde return",o),o})},w=(e,r)=>e.trim().split(/\s+/).map(e=>I(e,r)).join(" "),I=(e,r)=>{l("caret",e,r);let t=r.loose?c[p.CARETLOOSE]:c[p.CARET],s=r.includePrerelease?"-0":"";return e.replace(t,(r,t,i,n,o)=>{let a;return l("caret",e,r,t,i,n,o),S(t)?a="":S(i)?a=`>=${t}.0.0${s} <${+t+1}.0.0-0`:S(n)?a="0"===t?`>=${t}.${i}.0${s} <${t}.${+i+1}.0-0`:`>=${t}.${i}.0${s} <${+t+1}.0.0-0`:o?(l("replaceCaret pr",o),a="0"===t?"0"===i?`>=${t}.${i}.${n}-${o} <${t}.${i}.${+n+1}-0`:`>=${t}.${i}.${n}-${o} <${t}.${+i+1}.0-0`:`>=${t}.${i}.${n}-${o} <${+t+1}.0.0-0`):(l("no pr"),a="0"===t?"0"===i?`>=${t}.${i}.${n}${s} <${t}.${i}.${+n+1}-0`:`>=${t}.${i}.${n}${s} <${t}.${+i+1}.0-0`:`>=${t}.${i}.${n} <${+t+1}.0.0-0`),l("caret return",a),a})},O=(e,r)=>(l("replaceXRanges",e,r),e.split(/\s+/).map(e=>A(e,r)).join(" ")),A=(e,r)=>{e=e.trim();let t=r.loose?c[p.XRANGELOOSE]:c[p.XRANGE];return e.replace(t,(t,s,i,n,o,a)=>{l("xRange",e,t,s,i,n,o,a);let u=S(i),c=u||S(n),p=c||S(o);return"="===s&&p&&(s=""),a=r.includePrerelease?"-0":"",u?t=">"===s||"<"===s?"<0.0.0-0":"*":s&&p?(c&&(n=0),o=0,">"===s?(s=">=",c?(i=+i+1,n=0):n=+n+1,o=0):"<="===s&&(s="<",c?i=+i+1:n=+n+1),"<"===s&&(a="-0"),t=`${s+i}.${n}.${o}${a}`):c?t=`>=${i}.0.0${a} <${+i+1}.0.0-0`:p&&(t=`>=${i}.${n}.0${a} <${i}.${+n+1}.0-0`),l("xRange return",t),t})},T=(e,r)=>(l("replaceStars",e,r),e.trim().replace(c[p.STAR],"")),L=(e,r)=>(l("replaceGTE0",e,r),e.trim().replace(c[r.includePrerelease?p.GTE0PRE:p.GTE0],"")),N=e=>(r,t,s,i,n,o,a,l,u,c,p,f)=>(t=S(s)?"":S(i)?`>=${s}.0.0${e?"-0":""}`:S(n)?`>=${s}.${i}.0${e?"-0":""}`:o?`>=${t}`:`>=${t}${e?"-0":""}`,l=S(u)?"":S(c)?`<${+u+1}.0.0-0`:S(p)?`<${u}.${+c+1}.0-0`:f?`<=${u}.${c}.${p}-${f}`:e?`<${u}.${c}.${+p+1}-0`:`<=${l}`,`${t} ${l}`.trim()),x=(e,r,t)=>{for(let t=0;t<e.length;t++)if(!e[t].test(r))return!1;if(r.prerelease.length&&!t.includePrerelease){for(let t=0;t<e.length;t++)if(l(e[t].semver),e[t].semver!==a.ANY&&e[t].semver.prerelease.length>0){let s=e[t].semver;if(s.major===r.major&&s.minor===r.minor&&s.patch===r.patch)return!0}return!1}return!0}},8340:(e,r,t)=>{"use strict";let s=t(893),{MAX_LENGTH:i,MAX_SAFE_INTEGER:n}=t(7943),{safeRe:o,t:a}=t(601),l=t(1875),{compareIdentifiers:u}=t(8369);class c{constructor(e,r){if(r=l(r),e instanceof c){if(!!r.loose===e.loose&&!!r.includePrerelease===e.includePrerelease)return e;e=e.version}else if("string"!=typeof e)throw TypeError(`Invalid version. Must be a string. Got type "${typeof e}".`);if(e.length>i)throw TypeError(`version is longer than ${i} characters`);s("SemVer",e,r),this.options=r,this.loose=!!r.loose,this.includePrerelease=!!r.includePrerelease;let t=e.trim().match(r.loose?o[a.LOOSE]:o[a.FULL]);if(!t)throw TypeError(`Invalid Version: ${e}`);if(this.raw=e,this.major=+t[1],this.minor=+t[2],this.patch=+t[3],this.major>n||this.major<0)throw TypeError("Invalid major version");if(this.minor>n||this.minor<0)throw TypeError("Invalid minor version");if(this.patch>n||this.patch<0)throw TypeError("Invalid patch version");t[4]?this.prerelease=t[4].split(".").map(e=>{if(/^[0-9]+$/.test(e)){let r=+e;if(r>=0&&r<n)return r}return e}):this.prerelease=[],this.build=t[5]?t[5].split("."):[],this.format()}format(){return this.version=`${this.major}.${this.minor}.${this.patch}`,this.prerelease.length&&(this.version+=`-${this.prerelease.join(".")}`),this.version}toString(){return this.version}compare(e){if(s("SemVer.compare",this.version,this.options,e),!(e instanceof c)){if("string"==typeof e&&e===this.version)return 0;e=new c(e,this.options)}return e.version===this.version?0:this.compareMain(e)||this.comparePre(e)}compareMain(e){return e instanceof c||(e=new c(e,this.options)),u(this.major,e.major)||u(this.minor,e.minor)||u(this.patch,e.patch)}comparePre(e){if(e instanceof c||(e=new c(e,this.options)),this.prerelease.length&&!e.prerelease.length)return -1;if(!this.prerelease.length&&e.prerelease.length)return 1;if(!this.prerelease.length&&!e.prerelease.length)return 0;let r=0;do{let t=this.prerelease[r],i=e.prerelease[r];if(s("prerelease compare",r,t,i),void 0===t&&void 0===i)return 0;if(void 0===i)return 1;if(void 0===t)return -1;if(t===i)continue;else return u(t,i)}while(++r)}compareBuild(e){e instanceof c||(e=new c(e,this.options));let r=0;do{let t=this.build[r],i=e.build[r];if(s("build compare",r,t,i),void 0===t&&void 0===i)return 0;if(void 0===i)return 1;if(void 0===t)return -1;if(t===i)continue;else return u(t,i)}while(++r)}inc(e,r,t){if(e.startsWith("pre")){if(!r&&!1===t)throw Error("invalid increment argument: identifier is empty");if(r){let e=`-${r}`.match(this.options.loose?o[a.PRERELEASELOOSE]:o[a.PRERELEASE]);if(!e||e[1]!==r)throw Error(`invalid identifier: ${r}`)}}switch(e){case"premajor":this.prerelease.length=0,this.patch=0,this.minor=0,this.major++,this.inc("pre",r,t);break;case"preminor":this.prerelease.length=0,this.patch=0,this.minor++,this.inc("pre",r,t);break;case"prepatch":this.prerelease.length=0,this.inc("patch",r,t),this.inc("pre",r,t);break;case"prerelease":0===this.prerelease.length&&this.inc("patch",r,t),this.inc("pre",r,t);break;case"release":if(0===this.prerelease.length)throw Error(`version ${this.raw} is not a prerelease`);this.prerelease.length=0;break;case"major":(0!==this.minor||0!==this.patch||0===this.prerelease.length)&&this.major++,this.minor=0,this.patch=0,this.prerelease=[];break;case"minor":(0!==this.patch||0===this.prerelease.length)&&this.minor++,this.patch=0,this.prerelease=[];break;case"patch":0===this.prerelease.length&&this.patch++,this.prerelease=[];break;case"pre":{let e=Number(t)?1:0;if(0===this.prerelease.length)this.prerelease=[e];else{let s=this.prerelease.length;for(;--s>=0;)"number"==typeof this.prerelease[s]&&(this.prerelease[s]++,s=-2);if(-1===s){if(r===this.prerelease.join(".")&&!1===t)throw Error("invalid increment argument: identifier already exists");this.prerelease.push(e)}}if(r){let s=[r,e];!1===t&&(s=[r]),0===u(this.prerelease[0],r)?isNaN(this.prerelease[1])&&(this.prerelease=s):this.prerelease=s}break}default:throw Error(`invalid increment argument: ${e}`)}return this.raw=this.format(),this.build.length&&(this.raw+=`+${this.build.join(".")}`),this}}e.exports=c},8388:(e,r,t)=>{"use strict";let s=t(384);e.exports=(e,r)=>{let t=s(e.trim().replace(/^[=v]+/,""),r);return t?t.version:null}},1448:(e,r,t)=>{"use strict";let s=t(8218),i=t(1686),n=t(1793),o=t(8979),a=t(3081),l=t(9488);e.exports=(e,r,t,u)=>{switch(r){case"===":return"object"==typeof e&&(e=e.version),"object"==typeof t&&(t=t.version),e===t;case"!==":return"object"==typeof e&&(e=e.version),"object"==typeof t&&(t=t.version),e!==t;case"":case"=":case"==":return s(e,t,u);case"!=":return i(e,t,u);case">":return n(e,t,u);case">=":return o(e,t,u);case"<":return a(e,t,u);case"<=":return l(e,t,u);default:throw TypeError(`Invalid operator: ${r}`)}}},1643:(e,r,t)=>{"use strict";let s=t(8340),i=t(384),{safeRe:n,t:o}=t(601);e.exports=(e,r)=>{if(e instanceof s)return e;if("number"==typeof e&&(e=String(e)),"string"!=typeof e)return null;let t=null;if((r=r||{}).rtl){let s;let i=r.includePrerelease?n[o.COERCERTLFULL]:n[o.COERCERTL];for(;(s=i.exec(e))&&(!t||t.index+t[0].length!==e.length);)t&&s.index+s[0].length===t.index+t[0].length||(t=s),i.lastIndex=s.index+s[1].length+s[2].length;i.lastIndex=-1}else t=e.match(r.includePrerelease?n[o.COERCEFULL]:n[o.COERCE]);if(null===t)return null;let a=t[2],l=t[3]||"0",u=t[4]||"0",c=r.includePrerelease&&t[5]?`-${t[5]}`:"",p=r.includePrerelease&&t[6]?`+${t[6]}`:"";return i(`${a}.${l}.${u}${c}${p}`,r)}},4220:(e,r,t)=>{"use strict";let s=t(8340);e.exports=(e,r,t)=>{let i=new s(e,t),n=new s(r,t);return i.compare(n)||i.compareBuild(n)}},1847:(e,r,t)=>{"use strict";let s=t(5998);e.exports=(e,r)=>s(e,r,!0)},5998:(e,r,t)=>{"use strict";let s=t(8340);e.exports=(e,r,t)=>new s(e,t).compare(new s(r,t))},2694:(e,r,t)=>{"use strict";let s=t(384);e.exports=(e,r)=>{let t=s(e,null,!0),i=s(r,null,!0),n=t.compare(i);if(0===n)return null;let o=n>0,a=o?t:i,l=o?i:t,u=!!a.prerelease.length;if(l.prerelease.length&&!u){if(!l.patch&&!l.minor)return"major";if(0===l.compareMain(a))return l.minor&&!l.patch?"minor":"patch"}let c=u?"pre":"";return t.major!==i.major?c+"major":t.minor!==i.minor?c+"minor":t.patch!==i.patch?c+"patch":"prerelease"}},8218:(e,r,t)=>{"use strict";let s=t(5998);e.exports=(e,r,t)=>0===s(e,r,t)},1793:(e,r,t)=>{"use strict";let s=t(5998);e.exports=(e,r,t)=>s(e,r,t)>0},8979:(e,r,t)=>{"use strict";let s=t(5998);e.exports=(e,r,t)=>s(e,r,t)>=0},7248:(e,r,t)=>{"use strict";let s=t(8340);e.exports=(e,r,t,i,n)=>{"string"==typeof t&&(n=i,i=t,t=void 0);try{return new s(e instanceof s?e.version:e,t).inc(r,i,n).version}catch(e){return null}}},3081:(e,r,t)=>{"use strict";let s=t(5998);e.exports=(e,r,t)=>0>s(e,r,t)},9488:(e,r,t)=>{"use strict";let s=t(5998);e.exports=(e,r,t)=>0>=s(e,r,t)},8663:(e,r,t)=>{"use strict";let s=t(8340);e.exports=(e,r)=>new s(e,r).major},2891:(e,r,t)=>{"use strict";let s=t(8340);e.exports=(e,r)=>new s(e,r).minor},1686:(e,r,t)=>{"use strict";let s=t(5998);e.exports=(e,r,t)=>0!==s(e,r,t)},384:(e,r,t)=>{"use strict";let s=t(8340);e.exports=(e,r,t=!1)=>{if(e instanceof s)return e;try{return new s(e,r)}catch(e){if(!t)return null;throw e}}},817:(e,r,t)=>{"use strict";let s=t(8340);e.exports=(e,r)=>new s(e,r).patch},1185:(e,r,t)=>{"use strict";let s=t(384);e.exports=(e,r)=>{let t=s(e,r);return t&&t.prerelease.length?t.prerelease:null}},8798:(e,r,t)=>{"use strict";let s=t(5998);e.exports=(e,r,t)=>s(r,e,t)},1994:(e,r,t)=>{"use strict";let s=t(4220);e.exports=(e,r)=>e.sort((e,t)=>s(t,e,r))},7987:(e,r,t)=>{"use strict";let s=t(8965);e.exports=(e,r,t)=>{try{r=new s(r,t)}catch(e){return!1}return r.test(e)}},6558:(e,r,t)=>{"use strict";let s=t(4220);e.exports=(e,r)=>e.sort((e,t)=>s(e,t,r))},5506:(e,r,t)=>{"use strict";let s=t(384);e.exports=(e,r)=>{let t=s(e,r);return t?t.version:null}},9743:(e,r,t)=>{"use strict";let s=t(601),i=t(7943),n=t(8340),o=t(8369),a=t(384),l=t(5506),u=t(8388),c=t(7248),p=t(2694),f=t(8663),h=t(2891),m=t(817),d=t(1185),E=t(5998),y=t(8798),g=t(1847),v=t(4220),b=t(6558),S=t(1994),$=t(1793),R=t(3081),w=t(8218),I=t(1686),O=t(8979),A=t(9488),T=t(1448),L=t(1643),N=t(1269),x=t(8965),j=t(7987),P=t(9503),C=t(5389),D=t(9318),k=t(4323),M=t(9637),G=t(8806),_=t(2808),B=t(147),F=t(5586),U=t(7190),V=t(11);e.exports={parse:a,valid:l,clean:u,inc:c,diff:p,major:f,minor:h,patch:m,prerelease:d,compare:E,rcompare:y,compareLoose:g,compareBuild:v,sort:b,rsort:S,gt:$,lt:R,eq:w,neq:I,gte:O,lte:A,cmp:T,coerce:L,Comparator:N,Range:x,satisfies:j,toComparators:P,maxSatisfying:C,minSatisfying:D,minVersion:k,validRange:M,outside:G,gtr:_,ltr:B,intersects:F,simplifyRange:U,subset:V,SemVer:n,re:s.re,src:s.src,tokens:s.t,SEMVER_SPEC_VERSION:i.SEMVER_SPEC_VERSION,RELEASE_TYPES:i.RELEASE_TYPES,compareIdentifiers:o.compareIdentifiers,rcompareIdentifiers:o.rcompareIdentifiers}},7943:e=>{"use strict";let r=Number.MAX_SAFE_INTEGER||9007199254740991;e.exports={MAX_LENGTH:256,MAX_SAFE_COMPONENT_LENGTH:16,MAX_SAFE_BUILD_LENGTH:250,MAX_SAFE_INTEGER:r,RELEASE_TYPES:["major","premajor","minor","preminor","patch","prepatch","prerelease"],SEMVER_SPEC_VERSION:"2.0.0",FLAG_INCLUDE_PRERELEASE:1,FLAG_LOOSE:2}},893:e=>{"use strict";let r="object"==typeof process&&process.env&&process.env.NODE_DEBUG&&/\bsemver\b/i.test(process.env.NODE_DEBUG)?(...e)=>console.error("SEMVER",...e):()=>{};e.exports=r},8369:e=>{"use strict";let r=/^[0-9]+$/,t=(e,t)=>{let s=r.test(e),i=r.test(t);return s&&i&&(e=+e,t=+t),e===t?0:s&&!i?-1:i&&!s?1:e<t?-1:1};e.exports={compareIdentifiers:t,rcompareIdentifiers:(e,r)=>t(r,e)}},4527:e=>{"use strict";class r{constructor(){this.max=1e3,this.map=new Map}get(e){let r=this.map.get(e);if(void 0!==r)return this.map.delete(e),this.map.set(e,r),r}delete(e){return this.map.delete(e)}set(e,r){if(!this.delete(e)&&void 0!==r){if(this.map.size>=this.max){let e=this.map.keys().next().value;this.delete(e)}this.map.set(e,r)}return this}}e.exports=r},1875:e=>{"use strict";let r=Object.freeze({loose:!0}),t=Object.freeze({});e.exports=e=>e?"object"!=typeof e?r:e:t},601:(e,r,t)=>{"use strict";let{MAX_SAFE_COMPONENT_LENGTH:s,MAX_SAFE_BUILD_LENGTH:i,MAX_LENGTH:n}=t(7943),o=t(893),a=(r=e.exports={}).re=[],l=r.safeRe=[],u=r.src=[],c=r.safeSrc=[],p=r.t={},f=0,h="[a-zA-Z0-9-]",m=[["\\s",1],["\\d",n],[h,i]],d=e=>{for(let[r,t]of m)e=e.split(`${r}*`).join(`${r}{0,${t}}`).split(`${r}+`).join(`${r}{1,${t}}`);return e},E=(e,r,t)=>{let s=d(r),i=f++;o(e,i,r),p[e]=i,u[i]=r,c[i]=s,a[i]=new RegExp(r,t?"g":void 0),l[i]=new RegExp(s,t?"g":void 0)};E("NUMERICIDENTIFIER","0|[1-9]\\d*"),E("NUMERICIDENTIFIERLOOSE","\\d+"),E("NONNUMERICIDENTIFIER",`\\d*[a-zA-Z-]${h}*`),E("MAINVERSION",`(${u[p.NUMERICIDENTIFIER]})\\.(${u[p.NUMERICIDENTIFIER]})\\.(${u[p.NUMERICIDENTIFIER]})`),E("MAINVERSIONLOOSE",`(${u[p.NUMERICIDENTIFIERLOOSE]})\\.(${u[p.NUMERICIDENTIFIERLOOSE]})\\.(${u[p.NUMERICIDENTIFIERLOOSE]})`),E("PRERELEASEIDENTIFIER",`(?:${u[p.NONNUMERICIDENTIFIER]}|${u[p.NUMERICIDENTIFIER]})`),E("PRERELEASEIDENTIFIERLOOSE",`(?:${u[p.NONNUMERICIDENTIFIER]}|${u[p.NUMERICIDENTIFIERLOOSE]})`),E("PRERELEASE",`(?:-(${u[p.PRERELEASEIDENTIFIER]}(?:\\.${u[p.PRERELEASEIDENTIFIER]})*))`),E("PRERELEASELOOSE",`(?:-?(${u[p.PRERELEASEIDENTIFIERLOOSE]}(?:\\.${u[p.PRERELEASEIDENTIFIERLOOSE]})*))`),E("BUILDIDENTIFIER",`${h}+`),E("BUILD",`(?:\\+(${u[p.BUILDIDENTIFIER]}(?:\\.${u[p.BUILDIDENTIFIER]})*))`),E("FULLPLAIN",`v?${u[p.MAINVERSION]}${u[p.PRERELEASE]}?${u[p.BUILD]}?`),E("FULL",`^${u[p.FULLPLAIN]}$`),E("LOOSEPLAIN",`[v=\\s]*${u[p.MAINVERSIONLOOSE]}${u[p.PRERELEASELOOSE]}?${u[p.BUILD]}?`),E("LOOSE",`^${u[p.LOOSEPLAIN]}$`),E("GTLT","((?:<|>)?=?)"),E("XRANGEIDENTIFIERLOOSE",`${u[p.NUMERICIDENTIFIERLOOSE]}|x|X|\\*`),E("XRANGEIDENTIFIER",`${u[p.NUMERICIDENTIFIER]}|x|X|\\*`),E("XRANGEPLAIN",`[v=\\s]*(${u[p.XRANGEIDENTIFIER]})(?:\\.(${u[p.XRANGEIDENTIFIER]})(?:\\.(${u[p.XRANGEIDENTIFIER]})(?:${u[p.PRERELEASE]})?${u[p.BUILD]}?)?)?`),E("XRANGEPLAINLOOSE",`[v=\\s]*(${u[p.XRANGEIDENTIFIERLOOSE]})(?:\\.(${u[p.XRANGEIDENTIFIERLOOSE]})(?:\\.(${u[p.XRANGEIDENTIFIERLOOSE]})(?:${u[p.PRERELEASELOOSE]})?${u[p.BUILD]}?)?)?`),E("XRANGE",`^${u[p.GTLT]}\\s*${u[p.XRANGEPLAIN]}$`),E("XRANGELOOSE",`^${u[p.GTLT]}\\s*${u[p.XRANGEPLAINLOOSE]}$`),E("COERCEPLAIN",`(^|[^\\d])(\\d{1,${s}})(?:\\.(\\d{1,${s}}))?(?:\\.(\\d{1,${s}}))?`),E("COERCE",`${u[p.COERCEPLAIN]}(?:$|[^\\d])`),E("COERCEFULL",u[p.COERCEPLAIN]+`(?:${u[p.PRERELEASE]})?`+`(?:${u[p.BUILD]})?`+"(?:$|[^\\d])"),E("COERCERTL",u[p.COERCE],!0),E("COERCERTLFULL",u[p.COERCEFULL],!0),E("LONETILDE","(?:~>?)"),E("TILDETRIM",`(\\s*)${u[p.LONETILDE]}\\s+`,!0),r.tildeTrimReplace="$1~",E("TILDE",`^${u[p.LONETILDE]}${u[p.XRANGEPLAIN]}$`),E("TILDELOOSE",`^${u[p.LONETILDE]}${u[p.XRANGEPLAINLOOSE]}$`),E("LONECARET","(?:\\^)"),E("CARETTRIM",`(\\s*)${u[p.LONECARET]}\\s+`,!0),r.caretTrimReplace="$1^",E("CARET",`^${u[p.LONECARET]}${u[p.XRANGEPLAIN]}$`),E("CARETLOOSE",`^${u[p.LONECARET]}${u[p.XRANGEPLAINLOOSE]}$`),E("COMPARATORLOOSE",`^${u[p.GTLT]}\\s*(${u[p.LOOSEPLAIN]})$|^$`),E("COMPARATOR",`^${u[p.GTLT]}\\s*(${u[p.FULLPLAIN]})$|^$`),E("COMPARATORTRIM",`(\\s*)${u[p.GTLT]}\\s*(${u[p.LOOSEPLAIN]}|${u[p.XRANGEPLAIN]})`,!0),r.comparatorTrimReplace="$1$2$3",E("HYPHENRANGE",`^\\s*(${u[p.XRANGEPLAIN]})\\s+-\\s+(${u[p.XRANGEPLAIN]})\\s*$`),E("HYPHENRANGELOOSE",`^\\s*(${u[p.XRANGEPLAINLOOSE]})\\s+-\\s+(${u[p.XRANGEPLAINLOOSE]})\\s*$`),E("STAR","(<|>)?=?\\s*\\*"),E("GTE0","^\\s*>=\\s*0\\.0\\.0\\s*$"),E("GTE0PRE","^\\s*>=\\s*0\\.0\\.0-0\\s*$")},2808:(e,r,t)=>{"use strict";let s=t(8806);e.exports=(e,r,t)=>s(e,r,">",t)},5586:(e,r,t)=>{"use strict";let s=t(8965);e.exports=(e,r,t)=>(e=new s(e,t),r=new s(r,t),e.intersects(r,t))},147:(e,r,t)=>{"use strict";let s=t(8806);e.exports=(e,r,t)=>s(e,r,"<",t)},5389:(e,r,t)=>{"use strict";let s=t(8340),i=t(8965);e.exports=(e,r,t)=>{let n=null,o=null,a=null;try{a=new i(r,t)}catch(e){return null}return e.forEach(e=>{a.test(e)&&(!n||-1===o.compare(e))&&(o=new s(n=e,t))}),n}},9318:(e,r,t)=>{"use strict";let s=t(8340),i=t(8965);e.exports=(e,r,t)=>{let n=null,o=null,a=null;try{a=new i(r,t)}catch(e){return null}return e.forEach(e=>{a.test(e)&&(!n||1===o.compare(e))&&(o=new s(n=e,t))}),n}},4323:(e,r,t)=>{"use strict";let s=t(8340),i=t(8965),n=t(1793);e.exports=(e,r)=>{e=new i(e,r);let t=new s("0.0.0");if(e.test(t)||(t=new s("0.0.0-0"),e.test(t)))return t;t=null;for(let r=0;r<e.set.length;++r){let i=e.set[r],o=null;i.forEach(e=>{let r=new s(e.semver.version);switch(e.operator){case">":0===r.prerelease.length?r.patch++:r.prerelease.push(0),r.raw=r.format();case"":case">=":(!o||n(r,o))&&(o=r);break;case"<":case"<=":break;default:throw Error(`Unexpected operation: ${e.operator}`)}}),o&&(!t||n(t,o))&&(t=o)}return t&&e.test(t)?t:null}},8806:(e,r,t)=>{"use strict";let s=t(8340),i=t(1269),{ANY:n}=i,o=t(8965),a=t(7987),l=t(1793),u=t(3081),c=t(9488),p=t(8979);e.exports=(e,r,t,f)=>{let h,m,d,E,y;switch(e=new s(e,f),r=new o(r,f),t){case">":h=l,m=c,d=u,E=">",y=">=";break;case"<":h=u,m=p,d=l,E="<",y="<=";break;default:throw TypeError('Must provide a hilo val of "<" or ">"')}if(a(e,r,f))return!1;for(let t=0;t<r.set.length;++t){let s=r.set[t],o=null,a=null;if(s.forEach(e=>{e.semver===n&&(e=new i(">=0.0.0")),o=o||e,a=a||e,h(e.semver,o.semver,f)?o=e:d(e.semver,a.semver,f)&&(a=e)}),o.operator===E||o.operator===y||(!a.operator||a.operator===E)&&m(e,a.semver)||a.operator===y&&d(e,a.semver))return!1}return!0}},7190:(e,r,t)=>{"use strict";let s=t(7987),i=t(5998);e.exports=(e,r,t)=>{let n=[],o=null,a=null,l=e.sort((e,r)=>i(e,r,t));for(let e of l)s(e,r,t)?(a=e,o||(o=e)):(a&&n.push([o,a]),a=null,o=null);o&&n.push([o,null]);let u=[];for(let[e,r]of n)e===r?u.push(e):r||e!==l[0]?r?e===l[0]?u.push(`<=${r}`):u.push(`${e} - ${r}`):u.push(`>=${e}`):u.push("*");let c=u.join(" || "),p="string"==typeof r.raw?r.raw:String(r);return c.length<p.length?c:r}},11:(e,r,t)=>{"use strict";let s=t(8965),i=t(1269),{ANY:n}=i,o=t(7987),a=t(5998),l=[new i(">=0.0.0-0")],u=[new i(">=0.0.0")],c=(e,r,t)=>{let s,i,c,h,m,d,E;if(e===r)return!0;if(1===e.length&&e[0].semver===n){if(1===r.length&&r[0].semver===n)return!0;e=t.includePrerelease?l:u}if(1===r.length&&r[0].semver===n){if(t.includePrerelease)return!0;r=u}let y=new Set;for(let r of e)">"===r.operator||">="===r.operator?s=p(s,r,t):"<"===r.operator||"<="===r.operator?i=f(i,r,t):y.add(r.semver);if(y.size>1||s&&i&&((c=a(s.semver,i.semver,t))>0||0===c&&(">="!==s.operator||"<="!==i.operator)))return null;for(let e of y){if(s&&!o(e,String(s),t)||i&&!o(e,String(i),t))return null;for(let s of r)if(!o(e,String(s),t))return!1;return!0}let g=!!i&&!t.includePrerelease&&!!i.semver.prerelease.length&&i.semver,v=!!s&&!t.includePrerelease&&!!s.semver.prerelease.length&&s.semver;for(let e of(g&&1===g.prerelease.length&&"<"===i.operator&&0===g.prerelease[0]&&(g=!1),r)){if(E=E||">"===e.operator||">="===e.operator,d=d||"<"===e.operator||"<="===e.operator,s){if(v&&e.semver.prerelease&&e.semver.prerelease.length&&e.semver.major===v.major&&e.semver.minor===v.minor&&e.semver.patch===v.patch&&(v=!1),">"===e.operator||">="===e.operator){if((h=p(s,e,t))===e&&h!==s)return!1}else if(">="===s.operator&&!o(s.semver,String(e),t))return!1}if(i){if(g&&e.semver.prerelease&&e.semver.prerelease.length&&e.semver.major===g.major&&e.semver.minor===g.minor&&e.semver.patch===g.patch&&(g=!1),"<"===e.operator||"<="===e.operator){if((m=f(i,e,t))===e&&m!==i)return!1}else if("<="===i.operator&&!o(i.semver,String(e),t))return!1}if(!e.operator&&(i||s)&&0!==c)return!1}return(!s||!d||!!i||0===c)&&(!i||!E||!!s||0===c)&&!v&&!g},p=(e,r,t)=>{if(!e)return r;let s=a(e.semver,r.semver,t);return s>0?e:s<0?r:">"===r.operator&&">="===e.operator?r:e},f=(e,r,t)=>{if(!e)return r;let s=a(e.semver,r.semver,t);return s<0?e:s>0?r:"<"===r.operator&&"<="===e.operator?r:e};e.exports=(e,r,t={})=>{if(e===r)return!0;e=new s(e,t),r=new s(r,t);let i=!1;e:for(let s of e.set){for(let e of r.set){let r=c(s,e,t);if(i=i||null!==r,r)continue e}if(i)return!1}return!0}},9503:(e,r,t)=>{"use strict";let s=t(8965);e.exports=(e,r)=>new s(e,r).set.map(e=>e.map(e=>e.value).join(" ").trim().split(" "))},9637:(e,r,t)=>{"use strict";let s=t(8965);e.exports=(e,r)=>{try{return new s(e,r).range||"*"}catch(e){return null}}},9413:(e,r,t)=>{"use strict";t.d(r,{VR:()=>a,Vd:()=>o,Xj:()=>i,sm:()=>n});var s=t(8070);function i(e,r=200){return s.Z.json({success:!0,data:e},{status:r})}function n(e){return s.Z.json({success:!1,error:{code:"VALIDATION_ERROR",message:"请求参数验证失败",details:e.errors}},{status:400})}function o(e="服务器内部错误"){return s.Z.json({success:!1,error:{code:"INTERNAL_ERROR",message:e}},{status:500})}function a(e,r=500){return s.Z.json({success:!1,error:{code:r>=500?"INTERNAL_ERROR":"CLIENT_ERROR",message:e}},{status:r})}},9631:(e,r,t)=>{"use strict";let s;t.d(r,{_:()=>s}),s=new(require("@prisma/client")).PrismaClient}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[638,206,252,50],()=>t(6205));module.exports=s})();