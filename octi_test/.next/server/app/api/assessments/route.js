"use strict";(()=>{var e={};e.id=388,e.ids=[388],e.modules={517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},5088:(e,s,t)=>{t.r(s),t.d(s,{headerHooks:()=>g,originalPathname:()=>y,patchFetch:()=>_,requestAsyncStorage:()=>A,routeModule:()=>w,serverHooks:()=>I,staticGenerationAsyncStorage:()=>Z,staticGenerationBailout:()=>x});var r={};t.r(r),t.d(r,{GET:()=>R,POST:()=>j,PUT:()=>h});var n=t(5419),a=t(9108),o=t(9678),i=t(8070),u=t(342),c=t(5252),d=t(2178),l=t(9413);let m=null;async function p(){return m||(m=u.s.getInstance(),await m.initialize()),m}async function R(e){try{let{searchParams:s}=new URL(e.url),t=s.get("id"),r=await p();if(t){let e=await r.handleAssessmentRequest({action:"get",assessmentId:t});if(e.success)return i.Z.json(e,{status:200});return i.Z.json(e,{status:404})}{let e={success:!0,data:[{id:"assessment_1",title:"OCTI智能评估 - 团队协作",status:"active",createdAt:new Date().toISOString()},{id:"assessment_2",title:"OCTI智能评估 - 领导力",status:"completed",createdAt:new Date().toISOString()}]};return i.Z.json(e,{status:200})}}catch(e){return console.error("评估API GET请求失败:",e),i.Z.json({success:!1,error:"服务器内部错误"},{status:500})}}let f=c.Ry({title:c.Z_().min(1,"标题不能为空").max(100,"标题不能超过100字符"),description:c.Z_().optional(),type:c.Km(["team_collaboration","leadership","innovation"],{errorMap:()=>({message:"无效的评估类型"})}),dimensions:c.IX(c.Z_()).min(1,"至少需要一个维度").max(10,"维度不能超过10个"),requirements:c.Ry({minQuestions:c.Rx().min(5).max(100).optional(),maxDuration:c.Rx().min(10).max(120).optional(),version:c.Km(["standard","professional"]).default("standard")}).optional()});async function j(e){try{let s=await e.json(),t=f.parse(s),r=await p(),n=await r.handleAssessmentRequest({action:"create",data:t});if(n.success)return(0,l.Xj)(n.data,201);return(0,l.VR)(n.error||"创建评估失败",400)}catch(e){if(e instanceof d.jm)return(0,l.sm)(e);return console.error("创建评估失败:",e),(0,l.Vd)()}}async function h(e){try{let{assessmentId:s,answers:t}=await e.json();if(!s||!t)return i.Z.json({success:!1,error:"缺少必要参数: assessmentId 或 answers"},{status:400});let r=await p(),n=await r.handleAssessmentRequest({action:"submit",assessmentId:s,data:{answers:t}});if(n.success)return i.Z.json(n,{status:200});return i.Z.json(n,{status:400})}catch(e){return console.error("提交评估失败:",e),i.Z.json({success:!1,error:"服务器内部错误"},{status:500})}}let w=new n.AppRouteRouteModule({definition:{kind:a.x.APP_ROUTE,page:"/api/assessments/route",pathname:"/api/assessments",filename:"route",bundlePath:"app/api/assessments/route"},resolvedPagePath:"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/api/assessments/route.ts",nextConfigOutput:"",userland:r}),{requestAsyncStorage:A,staticGenerationAsyncStorage:Z,serverHooks:I,headerHooks:g,staticGenerationBailout:x}=w,y="/api/assessments/route";function _(){return(0,o.patchFetch)({serverHooks:I,staticGenerationAsyncStorage:Z})}},9413:(e,s,t)=>{t.d(s,{VR:()=>i,Vd:()=>o,Xj:()=>n,sm:()=>a});var r=t(8070);function n(e,s=200){return r.Z.json({success:!0,data:e},{status:s})}function a(e){return r.Z.json({success:!1,error:{code:"VALIDATION_ERROR",message:"请求参数验证失败",details:e.errors}},{status:400})}function o(e="服务器内部错误"){return r.Z.json({success:!1,error:{code:"INTERNAL_ERROR",message:e}},{status:500})}function i(e,s=500){return r.Z.json({success:!1,error:{code:s>=500?"INTERNAL_ERROR":"CLIENT_ERROR",message:e}},{status:s})}}};var s=require("../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[638,206,252,683,686,342],()=>t(5088));module.exports=r})();