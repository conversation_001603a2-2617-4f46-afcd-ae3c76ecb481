"use strict";(()=>{var e={};e.id=657,e.ids=[657],e.modules={517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},7811:(e,t,s)=>{s.r(t),s.d(t,{headerHooks:()=>A,originalPathname:()=>E,patchFetch:()=>S,requestAsyncStorage:()=>v,routeModule:()=>f,serverHooks:()=>x,staticGenerationAsyncStorage:()=>R,staticGenerationBailout:()=>D});var a={};s.r(a),s.d(a,{GET:()=>w,OPTIONS:()=>I});var n=s(5419),r=s(9108),i=s(9678),o=s(8070),u=s(5252),l=s(2178),m=s(4100),c=s(2259),d=s(335),p=s(3320);let h=new(s(3966)).Yd("AssessmentEvaluateAPI"),g=u.Ry({assessmentId:u.Z_().min(1,"评估ID不能为空")});function y(){let e=new c.e({minimax:{timeout:3e4},deepseek:{timeout:25e3},maxRetries:3}),t=new d.Z({templatePath:"./config/prompts",maxTokens:4e3,temperature:.7}),s=new p.e({strategy:"weighted",threshold:.8,maxSources:3,weights:{questionnaire:.6,external:.4}});return{organizationTutorAgent:new m.S({llmClient:e,promptBuilder:t,dataFusionEngine:s})}}async function w(e){try{let{searchParams:t}=new URL(e.url),s=t.get("assessmentId");if(!s)return o.Z.json({success:!1,error:{code:"MISSING_PARAMETER",message:"Assessment ID is required"}},{status:400});let a=g.parse({assessmentId:s});h.info("Retrieving assessment report",{assessmentId:a.assessmentId});let{organizationTutorAgent:n}=y();return o.Z.json({success:!1,error:{code:"NOT_FOUND",message:`No assessment found with ID: ${a.assessmentId}. Please generate a new assessment.`}},{status:404})}catch(e){if(h.error("Failed to retrieve assessment report",{error:e}),e instanceof l.jm)return o.Z.json({success:!1,error:{code:"VALIDATION_ERROR",message:"Invalid assessment ID",details:e.errors}},{status:400});return o.Z.json({success:!1,error:{code:"INTERNAL_ERROR",message:"Failed to retrieve assessment report"}},{status:500})}}async function I(){try{h.info("Retrieving assessment statistics");let{organizationTutorAgent:e}=y(),t=e.getCacheStats();return o.Z.json({success:!0,data:{statistics:{totalAssessments:t.size,cacheHitRate:t.hitRate,oldestEntry:t.oldestEntry,lastUpdated:new Date().toISOString()},capabilities:{supportedVersions:["standard","professional"],maxResponses:60,supportedDataFusion:!0,supportedBenchmarking:!0}}})}catch(e){return h.error("Failed to retrieve assessment statistics",{error:e}),o.Z.json({success:!1,error:{code:"INTERNAL_ERROR",message:"Failed to retrieve statistics"}},{status:500})}}u.Ry({assessmentId:u.Z_().min(1,"评估ID不能为空"),responses:u.IX(u.Ry({questionId:u.Z_(),answer:u.Yj()})).min(1,"至少需要一个回答"),version:u.Km(["standard","professional"]).default("standard"),options:u.Ry({includeRecommendations:u.O7().default(!0),includeBenchmarking:u.O7().default(!1),dataFusion:u.Ry({uploadedFiles:u.IX(u.Z_()).optional(),webData:u.IX(u.Z_()).optional()}).optional()}).optional()});let f=new n.AppRouteRouteModule({definition:{kind:r.x.APP_ROUTE,page:"/api/assessment/evaluate/route",pathname:"/api/assessment/evaluate",filename:"route",bundlePath:"app/api/assessment/evaluate/route"},resolvedPagePath:"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/api/assessment/evaluate/route.ts",nextConfigOutput:"",userland:a}),{requestAsyncStorage:v,staticGenerationAsyncStorage:R,serverHooks:x,headerHooks:A,staticGenerationBailout:D}=f,E="/api/assessment/evaluate/route";function S(){return(0,i.patchFetch)({serverHooks:x,staticGenerationAsyncStorage:R})}},4100:(e,t,s)=>{s.d(t,{S:()=>n});let a=new(s(3966)).Yd("OrganizationTutorAgent");class n{constructor(e,t,s){this.resultCache=new Map,this.cacheExpiry=864e5,this.isInitialized=!1,this.llmClient=e,this.promptBuilder=t,this.dataFusionEngine=s}async initialize(){if(this.isInitialized){a.warn("OrganizationTutorAgent 已经初始化");return}try{if(!this.llmClient)throw Error("LLMApiClient 是必需的依赖项");if(!this.promptBuilder)throw Error("PromptBuilder 是必需的依赖项");if(!this.dataFusionEngine)throw Error("DataFusionEngine 是必需的依赖项");this.cleanExpiredCache(),this.isInitialized=!0,a.info("OrganizationTutorAgent 初始化完成")}catch(e){throw a.error("OrganizationTutorAgent 初始化失败",{error:e}),e}}async generateAssessment(e,t={version:"standard",analysisMode:"basic",includeRecommendations:!0,outputLanguage:"zh"}){if(!this.isInitialized)throw Error("OrganizationTutorAgent 未初始化");if(!e||!e.organizationId)throw Error("评估数据无效：缺少组织ID");if(!e.responses||0===e.responses.length)throw Error("评估数据无效：缺少响应数据");let s=Date.now();try{let n;let r=this.generateCacheKey(e,t),i=this.resultCache.get(r);if(i&&Date.now()-i.timestamp<this.cacheExpiry)return a.info("使用缓存的评估结果",{cacheKey:r}),i.data;let o=this.preprocessAssessmentData(e),u=null;t.externalData&&t.externalData.length>0&&(u=await this.dataFusionEngine.fuseData(t.externalData.map(e=>({sourceId:e.source,content:JSON.stringify(e.data),contentType:e.type,metadata:{timestamp:e.timestamp,reliability:e.reliability}})),t.dataFusion));let l={version:t.version,agentType:"organization_tutor",context:{assessmentData:o,analysisMode:t.analysisMode,includeRecommendations:t.includeRecommendations,customFocus:t.customFocus,outputLanguage:t.outputLanguage,organizationType:e.metadata.organizationType,industryContext:e.metadata.industryContext},externalData:u?[{source:"external_data_fusion",content:JSON.stringify(u),type:"fusion_result",weight:1}]:void 0,dataFusion:t.dataFusion},m=await this.promptBuilder.buildPrompt(l);n="professional"===t.version?(await this.llmClient.dualModelChat({messages:[{role:"system",content:m.systemPrompt},{role:"user",content:m.userPrompt}],temperature:.3,maxTokens:8e3},"sequential")).secondary:await this.llmClient.chat("minimax",{messages:[{role:"system",content:m.systemPrompt},{role:"user",content:m.userPrompt}],temperature:.3,maxTokens:4e3});let c=await this.parseAssessmentResponse(n.content,e,t);this.resultCache.set(r,{data:c,timestamp:Date.now()});let d=Date.now()-s;return a.info("评估报告生成完成",{organizationId:e.organizationId,version:t.version,processingTime:d}),c}catch(s){throw a.error("评估报告生成失败",{error:s instanceof Error?s.message:String(s),organizationId:e.organizationId,options:t}),Error(`评估报告生成失败: ${s instanceof Error?s.message:"未知错误"}`)}}generateCacheKey(e,t){return[e.organizationId,t.version,t.analysisMode,JSON.stringify(t.customFocus||[]),e.metadata.completedAt].join("_").replace(/[^a-zA-Z0-9_]/g,"_")}preprocessAssessmentData(e){return{organizationId:e.organizationId,responses:e.responses.map(e=>({questionId:e.questionId,answer:e.answer,dimension:e.dimension,subdimension:e.subdimension})),metadata:e.metadata}}async parseAssessmentResponse(e,t,s){try{let a;let n=e.match(/```json\s*([\s\S]*?)\s*```/)||e.match(/\{[\s\S]*\}/);return a=n?JSON.parse(n[1]||n[0]):this.parseStructuredReport(e),{id:`report_${Date.now()}`,organizationId:t.organizationId,version:s.version,overallScore:a.overallScore||0,dimensionScores:a.dimensionScores||{},strengths:a.strengths||[],improvements:a.improvements||[],recommendations:a.recommendations||[],nextSteps:a.nextSteps||[],metadata:{generatedAt:new Date,analysisMode:s.analysisMode,dataSourcesUsed:s.externalData?.map(e=>e.source)||[]}}}catch(t){throw a.error("评估响应解析失败",{error:t,content:e.substring(0,500)}),Error("评估响应解析失败")}}parseStructuredReport(e){return{overallScore:75,dimensionScores:{},strengths:[],improvements:[],recommendations:[],nextSteps:[]}}cleanExpiredCache(){let e=Date.now();for(let[t,s]of this.resultCache.entries())e-s.timestamp>this.cacheExpiry&&this.resultCache.delete(t)}getCacheStats(){let e=Array.from(this.resultCache.values()),t=e.length>0?e.reduce((e,t)=>new Date(t.timestamp)<new Date(e.timestamp)?t:e).timestamp:null;return{size:this.resultCache.size,hitRate:0,oldestEntry:t}}clearCache(){this.resultCache.clear(),a.info("Assessment cache cleared")}}}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[638,206,252,683],()=>s(7811));module.exports=a})();