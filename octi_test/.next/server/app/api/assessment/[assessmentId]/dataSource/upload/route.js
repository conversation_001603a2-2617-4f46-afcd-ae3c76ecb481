"use strict";(()=>{var e={};e.id=713,e.ids=[713],e.modules={517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},1017:e=>{e.exports=require("path")},5785:(e,s,t)=>{t.r(s),t.d(s,{headerHooks:()=>g,originalPathname:()=>E,patchFetch:()=>S,requestAsyncStorage:()=>f,routeModule:()=>m,serverHooks:()=>R,staticGenerationAsyncStorage:()=>I,staticGenerationBailout:()=>j});var r={};t.r(r),t.d(r,{POST:()=>l});var a=t(5419),o=t(9108),n=t(9678),u=t(8070);let i=require("fs/promises");var c=t(1017),d=t(9413);let p=["application/pdf","application/vnd.openxmlformats-officedocument.wordprocessingml.document","text/plain"];async function l(e,{params:s}){try{let{assessmentId:t}=s;if(!t||t.length<1)return u.Z.json({success:!1,error:{code:"INVALID_ASSESSMENT_ID",message:"无效的评估ID"}},{status:400});let r=(await e.formData()).get("file");if(!r)return u.Z.json({success:!1,error:{code:"NO_FILE",message:"请选择要上传的文件"}},{status:400});if(!p.includes(r.type))return u.Z.json({success:!1,error:{code:"INVALID_TYPE",message:"不支持的文件类型，仅支持 PDF、DOCX、TXT 格式"}},{status:400});if(r.size>52428800)return u.Z.json({success:!1,error:{code:"FILE_TOO_LARGE",message:"文件大小超过 50MB 限制"}},{status:400});let a=`ds-${Date.now()}-${Math.random().toString(36).substr(2,9)}`,o=`${a}-${r.name}`,n=(0,c.join)(process.cwd(),"uploads",t);await (0,i.mkdir)(n,{recursive:!0});let l=(0,c.join)(n,o),m=await r.arrayBuffer(),f=Buffer.from(m);return await (0,i.writeFile)(l,f),(0,d.Xj)({dataSourceId:a,fileName:r.name,fileSize:r.size,fileType:r.type,message:"文件已接收，正在排队处理"},202)}catch(e){return console.error("文件上传失败:",e),(0,d.Vd)("文件上传失败")}}let m=new a.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/assessment/[assessmentId]/dataSource/upload/route",pathname:"/api/assessment/[assessmentId]/dataSource/upload",filename:"route",bundlePath:"app/api/assessment/[assessmentId]/dataSource/upload/route"},resolvedPagePath:"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/api/assessment/[assessmentId]/dataSource/upload/route.ts",nextConfigOutput:"",userland:r}),{requestAsyncStorage:f,staticGenerationAsyncStorage:I,serverHooks:R,headerHooks:g,staticGenerationBailout:j}=m,E="/api/assessment/[assessmentId]/dataSource/upload/route";function S(){return(0,n.patchFetch)({serverHooks:R,staticGenerationAsyncStorage:I})}},9413:(e,s,t)=>{t.d(s,{VR:()=>u,Vd:()=>n,Xj:()=>a,sm:()=>o});var r=t(8070);function a(e,s=200){return r.Z.json({success:!0,data:e},{status:s})}function o(e){return r.Z.json({success:!1,error:{code:"VALIDATION_ERROR",message:"请求参数验证失败",details:e.errors}},{status:400})}function n(e="服务器内部错误"){return r.Z.json({success:!1,error:{code:"INTERNAL_ERROR",message:e}},{status:500})}function u(e,s=500){return r.Z.json({success:!1,error:{code:s>=500?"INTERNAL_ERROR":"CLIENT_ERROR",message:e}},{status:s})}}};var s=require("../../../../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[638,206],()=>t(5785));module.exports=r})();