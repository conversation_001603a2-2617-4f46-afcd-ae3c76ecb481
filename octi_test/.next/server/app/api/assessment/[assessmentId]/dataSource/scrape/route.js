"use strict";(()=>{var e={};e.id=194,e.ids=[194],e.modules={517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},1493:(e,s,r)=>{r.r(s),r.d(s,{headerHooks:()=>f,originalPathname:()=>j,patchFetch:()=>g,requestAsyncStorage:()=>R,routeModule:()=>l,serverHooks:()=>S,staticGenerationAsyncStorage:()=>I,staticGenerationBailout:()=>h});var t={};r.r(t),r.d(t,{POST:()=>m});var a=r(5419),n=r(9108),o=r(9678),u=r(8070),c=r(5252),i=r(2178),d=r(9413);let p=c.Ry({url:c.Z_().url("请提供有效的URL地址")});async function m(e,{params:s}){try{let{assessmentId:r}=s,t=await e.json(),a=p.parse(t);if(!r||r.length<1)return u.Z.json({success:!1,error:{code:"INVALID_ASSESSMENT_ID",message:"无效的评估ID"}},{status:400});let n=`ds-${Date.now()}-${Math.random().toString(36).substr(2,9)}`;return(0,d.Xj)({dataSourceId:n,url:a.url,message:"URL已接收，正在排队进行数据采集"},202)}catch(e){if(e instanceof i.jm)return(0,d.sm)(e);return console.error("网络数据采集请求失败:",e),(0,d.Vd)("数据采集请求失败")}}let l=new a.AppRouteRouteModule({definition:{kind:n.x.APP_ROUTE,page:"/api/assessment/[assessmentId]/dataSource/scrape/route",pathname:"/api/assessment/[assessmentId]/dataSource/scrape",filename:"route",bundlePath:"app/api/assessment/[assessmentId]/dataSource/scrape/route"},resolvedPagePath:"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/api/assessment/[assessmentId]/dataSource/scrape/route.ts",nextConfigOutput:"",userland:t}),{requestAsyncStorage:R,staticGenerationAsyncStorage:I,serverHooks:S,headerHooks:f,staticGenerationBailout:h}=l,j="/api/assessment/[assessmentId]/dataSource/scrape/route";function g(){return(0,o.patchFetch)({serverHooks:S,staticGenerationAsyncStorage:I})}},9413:(e,s,r)=>{r.d(s,{VR:()=>u,Vd:()=>o,Xj:()=>a,sm:()=>n});var t=r(8070);function a(e,s=200){return t.Z.json({success:!0,data:e},{status:s})}function n(e){return t.Z.json({success:!1,error:{code:"VALIDATION_ERROR",message:"请求参数验证失败",details:e.errors}},{status:400})}function o(e="服务器内部错误"){return t.Z.json({success:!1,error:{code:"INTERNAL_ERROR",message:e}},{status:500})}function u(e,s=500){return t.Z.json({success:!1,error:{code:s>=500?"INTERNAL_ERROR":"CLIENT_ERROR",message:e}},{status:s})}}};var s=require("../../../../../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[638,206,252],()=>r(1493));module.exports=t})();