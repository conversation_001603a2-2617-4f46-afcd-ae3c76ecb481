"use strict";(()=>{var e={};e.id=529,e.ids=[529],e.modules={517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},7098:(e,t,s)=>{s.r(t),s.d(t,{headerHooks:()=>f,originalPathname:()=>w,patchFetch:()=>y,requestAsyncStorage:()=>h,routeModule:()=>g,serverHooks:()=>m,staticGenerationAsyncStorage:()=>j,staticGenerationBailout:()=>v});var r={};s.r(r),s.d(r,{GET:()=>l,POST:()=>d});var a=s(5419),n=s(9108),o=s(9678),u=s(8070),i=s(342);let c=null;async function p(){return c||(c=i.s.getInstance(),await c.initialize()),c}async function l(e){try{let{searchParams:t}=new URL(e.url),s=t.get("agent"),r=t.get("action")||"list",a=await p(),n=await a.handleAgentRequest({action:r,agentName:s||void 0});if(n.success)return u.Z.json(n,{status:200});return u.Z.json(n,{status:400})}catch(e){return console.error("智能体API GET请求失败:",e),u.Z.json({success:!1,error:"服务器内部错误"},{status:500})}}async function d(e){try{let{agentName:t,input:s}=await e.json();if(!t||!s)return u.Z.json({success:!1,error:"缺少必要参数: agentName 或 input"},{status:400});let r=await p(),a=await r.handleAgentRequest({action:"execute",agentName:t,input:s});if(a.success)return u.Z.json(a,{status:200});return u.Z.json(a,{status:400})}catch(e){return console.error("智能体执行失败:",e),u.Z.json({success:!1,error:"服务器内部错误"},{status:500})}}let g=new a.AppRouteRouteModule({definition:{kind:n.x.APP_ROUTE,page:"/api/agents/route",pathname:"/api/agents",filename:"route",bundlePath:"app/api/agents/route"},resolvedPagePath:"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/api/agents/route.ts",nextConfigOutput:"",userland:r}),{requestAsyncStorage:h,staticGenerationAsyncStorage:j,serverHooks:m,headerHooks:f,staticGenerationBailout:v}=g,w="/api/agents/route";function y(){return(0,o.patchFetch)({serverHooks:m,staticGenerationAsyncStorage:j})}}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[638,206,252,683,686,342],()=>s(7098));module.exports=r})();