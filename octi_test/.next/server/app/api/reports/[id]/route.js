"use strict";(()=>{var e={};e.id=162,e.ids=[162],e.modules={517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},9707:(e,t,r)=>{r.r(t),r.d(t,{headerHooks:()=>f,originalPathname:()=>v,patchFetch:()=>x,requestAsyncStorage:()=>j,routeModule:()=>y,serverHooks:()=>h,staticGenerationAsyncStorage:()=>Z,staticGenerationBailout:()=>g});var s={};r.r(s),r.d(s,{DELETE:()=>m,GET:()=>p,PUT:()=>l});var o=r(5419),n=r(9108),a=r(9678),i=r(8070),u=r(5252),d=r(2178);let c={"demo-report-1":{id:"demo-report-1",assessmentId:"assessment-demo-1",organizationName:"示例科技公司",version:"standard",generatedAt:new Date("2024-01-15T10:30:00Z"),radarChart:{dimensions:{SF:3.8,IT:4.2,MV:3.5,AD:3.9},maxScore:5},sections:[{id:"executive-summary",title:"执行摘要",type:"text",content:"基于OCTI模型的评估结果显示，贵组织在团队协同度方面表现优秀，达到4.2分，显示出良好的内部沟通和协作能力。战略聚焦度和适应性发展也处于良好水平，分别为3.8分和3.9分。激励价值观方面还有提升空间，建议加强员工激励机制和企业文化建设。",order:1},{id:"strengths",title:"组织优势",type:"list",content:"",data:["团队协作机制完善，跨部门沟通顺畅","组织适应性强，能够快速响应市场变化","战略目标相对明确，执行力较强","员工专业能力突出，学习意愿强烈"],order:2},{id:"improvement-areas",title:"改进领域",type:"list",content:"",data:["激励机制需要进一步完善和个性化","企业文化价值观传播有待加强","长期战略规划的细化和落地执行","创新文化氛围的营造和制度支持"],order:3},{id:"dimension-analysis",title:"维度详细分析",type:"table",content:"",data:{headers:["维度","得分","水平","关键特征"],rows:[["战略聚焦度","3.8","良好","目标明确，执行有力"],["团队协同度","4.2","优秀","沟通顺畅，协作高效"],["激励价值观","3.5","一般","制度完善，文化待强化"],["适应性发展","3.9","良好","变化敏感，学习积极"]]},order:4}]}};async function p(e,{params:t}){try{let e=t.id;if(!e)return i.Z.json({error:"缺少报告ID"},{status:400});let r=c[e];if(!r)return i.Z.json({error:"报告不存在"},{status:404});return i.Z.json({success:!0,report:r})}catch(e){return console.error("获取报告失败:",e),i.Z.json({error:"服务器内部错误"},{status:500})}}async function l(e,{params:t}){try{let r=t.id,s=await e.json(),o=u.Ry({organizationName:u.Z_().optional(),sections:u.IX(u.Ry({id:u.Z_(),title:u.Z_(),type:u.Km(["text","list","table"]),content:u.Z_(),data:u.Yj().optional(),order:u.Rx()})).optional()}).parse(s);if(!c[r])return i.Z.json({error:"报告不存在"},{status:404});return c[r]={...c[r],...o},i.Z.json({success:!0,report:c[r]})}catch(e){if(e instanceof d.jm)return i.Z.json({error:"请求数据格式错误",details:e.errors},{status:400});return console.error("更新报告失败:",e),i.Z.json({error:"服务器内部错误"},{status:500})}}async function m(e,{params:t}){try{let e=t.id;if(!c[e])return i.Z.json({error:"报告不存在"},{status:404});return delete c[e],i.Z.json({success:!0,message:"报告已删除"})}catch(e){return console.error("删除报告失败:",e),i.Z.json({error:"服务器内部错误"},{status:500})}}let y=new o.AppRouteRouteModule({definition:{kind:n.x.APP_ROUTE,page:"/api/reports/[id]/route",pathname:"/api/reports/[id]",filename:"route",bundlePath:"app/api/reports/[id]/route"},resolvedPagePath:"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/api/reports/[id]/route.ts",nextConfigOutput:"",userland:s}),{requestAsyncStorage:j,staticGenerationAsyncStorage:Z,serverHooks:h,headerHooks:f,staticGenerationBailout:g}=y,v="/api/reports/[id]/route";function x(){return(0,a.patchFetch)({serverHooks:h,staticGenerationAsyncStorage:Z})}}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[638,206,252],()=>r(9707));module.exports=s})();