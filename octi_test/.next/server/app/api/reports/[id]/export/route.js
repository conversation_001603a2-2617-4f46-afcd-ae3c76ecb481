"use strict";(()=>{var e={};e.id=196,e.ids=[196],e.modules={517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},6554:(e,t,r)=>{r.r(t),r.d(t,{headerHooks:()=>x,originalPathname:()=>g,patchFetch:()=>y,requestAsyncStorage:()=>m,routeModule:()=>l,serverHooks:()=>h,staticGenerationAsyncStorage:()=>f,staticGenerationBailout:()=>$});var o={};r.r(o),r.d(o,{GET:()=>c,POST:()=>u});var a=r(5419),n=r(9108),s=r(9678),i=r(8070);let d={"demo-report-1":{id:"demo-report-1",assessmentId:"assessment-demo-1",organizationName:"示例科技公司",version:"standard",generatedAt:new Date("2024-01-15T10:30:00Z"),radarChart:{dimensions:{SF:3.8,IT:4.2,MV:3.5,AD:3.9},maxScore:5},sections:[{id:"executive-summary",title:"执行摘要",type:"text",content:"基于OCTI模型的评估结果显示，贵组织在团队协同度方面表现优秀，达到4.2分，显示出良好的内部沟通和协作能力。",order:1}]}};async function u(e,{params:t}){try{let e=t.id;if(!e)return i.Z.json({error:"缺少报告ID"},{status:400});let r=d[e];if(!r)return i.Z.json({error:"报告不存在"},{status:404});let o=function(e){let t=`%PDF-1.4
1 0 obj
<<
/Type /Catalog
/Pages 2 0 R
>>
endobj

`,r=`
OCTI 组织文化评估报告

组织名称: ${e.organizationName}
报告版本: ${"professional"===e.version?"专业版":"标准版"}
生成时间: ${p(e.generatedAt)}

总体评分:
`,o="";if(e.radarChart){let{dimensions:t,maxScore:r}=e.radarChart,a=(t.SF+t.IT+t.MV+t.AD)/4;o=`
整体得分: ${a.toFixed(1)} / ${r}

维度详情:
- 战略聚焦度 (SF): ${t.SF} / ${r}
- 团队协同度 (IT): ${t.IT} / ${r}
- 激励价值观 (MV): ${t.MV} / ${r}
- 适应性发展 (AD): ${t.AD} / ${r}

`}let a="\n报告内容:\n";e.sections.sort((e,t)=>e.order-t.order).forEach(e=>{a+=`
${e.title}:
${e.content}
`,"list"===e.type&&e.data&&e.data.forEach((e,t)=>{a+=`${t+1}. ${e}
`})});let n=t+r+o+a;return Buffer.from(n,"utf-8")}(r);return new i.Z(o,{status:200,headers:{"Content-Type":"application/pdf","Content-Disposition":`attachment; filename="OCTI评估报告_${r.organizationName}_${p(r.generatedAt)}.pdf"`,"Cache-Control":"no-cache"}})}catch(e){return console.error("导出报告失败:",e),i.Z.json({error:"导出失败，请稍后重试"},{status:500})}}function p(e){return new Intl.DateTimeFormat("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit"}).format(e).replace(/\//g,"-")}async function c(e,{params:t}){try{let e=t.id;if(!e)return i.Z.json({error:"缺少报告ID"},{status:400});let r=d[e];if(!r)return i.Z.json({error:"报告不存在"},{status:404});return i.Z.json({success:!0,exportOptions:{formats:[{type:"pdf",name:"PDF报告",description:"完整的PDF格式报告，包含图表和详细分析",available:!0},{type:"excel",name:"Excel数据",description:"包含原始数据和计算结果的Excel文件",available:!1},{type:"word",name:"Word文档",description:"可编辑的Word格式报告",available:!1}],customization:{includeSections:!0,includeCharts:!0,includeRawData:"professional"===r.version,watermark:"standard"===r.version}}})}catch(e){return console.error("获取导出选项失败:",e),i.Z.json({error:"服务器内部错误"},{status:500})}}let l=new a.AppRouteRouteModule({definition:{kind:n.x.APP_ROUTE,page:"/api/reports/[id]/export/route",pathname:"/api/reports/[id]/export",filename:"route",bundlePath:"app/api/reports/[id]/export/route"},resolvedPagePath:"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/api/reports/[id]/export/route.ts",nextConfigOutput:"",userland:o}),{requestAsyncStorage:m,staticGenerationAsyncStorage:f,serverHooks:h,headerHooks:x,staticGenerationBailout:$}=l,g="/api/reports/[id]/export/route";function y(){return(0,s.patchFetch)({serverHooks:h,staticGenerationAsyncStorage:f})}}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),o=t.X(0,[638,206],()=>r(6554));module.exports=o})();