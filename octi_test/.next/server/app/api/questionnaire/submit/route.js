"use strict";(()=>{var e={};e.id=575,e.ids=[575],e.modules={517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},7301:(e,t,n)=>{n.r(t),n.d(t,{headerHooks:()=>w,originalPathname:()=>R,patchFetch:()=>b,requestAsyncStorage:()=>I,routeModule:()=>f,serverHooks:()=>S,staticGenerationAsyncStorage:()=>h,staticGenerationBailout:()=>_});var s={};n.r(s),n.d(s,{GET:()=>g,POST:()=>l});var o=n(5419),r=n(9108),a=n(9678),i=n(8070),u=n(3966),d=n(5252),c=n(2178);let m=new u.Yd("QuestionnaireSubmitAPI"),p=d.Ry({questionnaireId:d.Z_().min(1,"问卷ID不能为空"),organizationId:d.Z_().optional(),userId:d.Z_().optional(),responses:d.IX(d.Ry({questionId:d.Z_(),answer:d.G0([d.Z_(),d.Rx(),d.IX(d.Z_()),d.Ry({value:d.G0([d.Z_(),d.Rx()]),text:d.Z_().optional()})]),dimension:d.Z_(),timeSpent:d.Rx().optional(),confidence:d.Rx().min(0).max(1).optional()})),metadata:d.Ry({startTime:d.Z_(),endTime:d.Z_(),totalTimeSpent:d.Rx(),deviceInfo:d.Ry({userAgent:d.Z_().optional(),screenResolution:d.Z_().optional(),timezone:d.Z_().optional()}).optional(),completionRate:d.Rx().min(0).max(1)})});async function l(e){try{m.info("问卷提交请求接收");let t=await e.json(),n=p.parse(t);m.info("请求数据验证通过",{questionnaireId:n.questionnaireId,responseCount:n.responses.length,completionRate:n.metadata.completionRate}),n.metadata.completionRate<.8&&m.warn("问卷完成率过低",{completionRate:n.metadata.completionRate});let s={id:`response_${Date.now()}_${Math.random().toString(36).substr(2,9)}`,questionnaireId:n.questionnaireId,organizationId:n.organizationId,userId:n.userId,responses:n.responses.map(e=>({questionId:e.questionId,answer:e.answer,dimension:e.dimension,timeSpent:e.timeSpent||0,confidence:e.confidence||1,timestamp:new Date().toISOString()})),metadata:{...n.metadata,submittedAt:new Date().toISOString(),ipAddress:e.headers.get("x-forwarded-for")||e.headers.get("x-real-ip")||"unknown",version:"1.0"},status:"completed",createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()},o=function(e){let t={};e.forEach(e=>{t[e.dimension]||(t[e.dimension]=[]);let n=0;"number"==typeof e.answer?n=e.answer:"string"==typeof e.answer&&(n=({A:1,B:2,C:3,D:4,E:5,完全不同意:1,不同意:2,中立:3,同意:4,完全同意:5})[e.answer]||3),t[e.dimension].push(n)});let n={};return Object.entries(t).forEach(([e,t])=>{n[e]=t.reduce((e,t)=>e+t,0)/t.length}),n}(s.responses),r={id:`assessment_${Date.now()}_${Math.random().toString(36).substr(2,9)}`,questionnaireResponseId:s.id,organizationId:n.organizationId,scores:o,overallScore:Object.values(o).reduce((e,t)=>e+t,0)/Object.keys(o).length,insights:function(e){let t=[];return Object.entries(e).forEach(([e,n])=>{n>=4?t.push(`${e}维度表现优秀，得分${n.toFixed(1)}`):n<=2&&t.push(`${e}维度需要重点关注，得分${n.toFixed(1)}`)}),t}(o),recommendations:function(e){let t=[];return Object.entries(e).forEach(([e,n])=>{n<=2.5&&t.push(`建议加强${e}相关的培训和改进措施`)}),0===t.length&&t.push("整体表现良好，建议继续保持并寻求进一步优化机会"),t}(o),createdAt:new Date().toISOString()};return m.info("问卷提交处理成功",{responseId:s.id,assessmentId:r.id,overallScore:r.overallScore}),i.Z.json({success:!0,data:{response:{id:s.id,status:s.status,submittedAt:s.metadata.submittedAt,completionRate:n.metadata.completionRate},assessment:{id:r.id,overallScore:r.overallScore,dimensionScores:r.scores,insights:r.insights,recommendations:r.recommendations}},metadata:{timestamp:new Date().toISOString(),processingTime:Date.now()-Date.now()}})}catch(e){if(m.error("问卷提交失败",{error:e}),e instanceof c.jm)return i.Z.json({success:!1,error:{type:"validation_error",message:"请求数据格式不正确",details:e.errors.map(e=>({field:e.path.join("."),message:e.message,code:e.code}))}},{status:400});if(e instanceof Error)return i.Z.json({success:!1,error:{type:"submission_error",message:e.message}},{status:500});return i.Z.json({success:!1,error:{type:"unknown_error",message:"提交过程中发生未知错误"}},{status:500})}}async function g(e){try{let{searchParams:t}=new URL(e.url),n=t.get("responseId"),s=t.get("questionnaireId");if(t.get("organizationId"),!n&&!s)return i.Z.json({success:!1,error:{type:"validation_error",message:"需要提供回答ID或问卷ID"}},{status:400});let o={id:n||"mock_response_id",questionnaireId:s||"mock_questionnaire_id",status:"completed",completionRate:1,submittedAt:new Date().toISOString(),totalTimeSpent:1800};return i.Z.json({success:!0,data:{response:o}})}catch(e){return m.error("获取问卷回答状态失败",{error:e}),i.Z.json({success:!1,error:{type:"retrieval_error",message:e instanceof Error?e.message:"获取回答状态失败"}},{status:500})}}let f=new o.AppRouteRouteModule({definition:{kind:r.x.APP_ROUTE,page:"/api/questionnaire/submit/route",pathname:"/api/questionnaire/submit",filename:"route",bundlePath:"app/api/questionnaire/submit/route"},resolvedPagePath:"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/api/questionnaire/submit/route.ts",nextConfigOutput:"",userland:s}),{requestAsyncStorage:I,staticGenerationAsyncStorage:h,serverHooks:S,headerHooks:w,staticGenerationBailout:_}=f,R="/api/questionnaire/submit/route";function b(){return(0,a.patchFetch)({serverHooks:S,staticGenerationAsyncStorage:h})}},3966:(e,t,n)=>{n.d(t,{Yd:()=>s});class s{constructor(e="App"){this.context=e}formatMessage(e,t,n){let s=new Date().toISOString(),o=n?` ${JSON.stringify(n)}`:"";return`[${s}] [${e.toUpperCase()}] [${this.context}] ${t}${o}`}debug(e,t){"debug"===process.env.LOG_LEVEL&&console.debug(this.formatMessage("debug",e,t))}info(e,t){console.info(this.formatMessage("info",e,t))}warn(e,t){console.warn(this.formatMessage("warn",e,t))}error(e,t){console.error(this.formatMessage("error",e,t))}setContext(e){this.context=e}}new s("OCTI")}};var t=require("../../../../webpack-runtime.js");t.C(e);var n=e=>t(t.s=e),s=t.X(0,[638,206,252],()=>n(7301));module.exports=s})();