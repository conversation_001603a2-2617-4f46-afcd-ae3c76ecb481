"use strict";(()=>{var e={};e.id=468,e.ids=[468],e.modules={517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},6950:(e,t,s)=>{s.r(t),s.d(t,{headerHooks:()=>E,originalPathname:()=>h,patchFetch:()=>A,requestAsyncStorage:()=>m,routeModule:()=>p,serverHooks:()=>d,staticGenerationAsyncStorage:()=>l,staticGenerationBailout:()=>_});var r={};s.r(r),s.d(r,{GET:()=>c,POST:()=>u});var n=s(5419),o=s(9108),a=s(9678),i=s(9413);async function c(e){try{let e={minimax:{status:"unknown",error:null},deepseek:{status:"unknown",error:null}};try{let t=await fetch("https://api.minimax.chat/v1/text/chatcompletion",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${process.env.MINIMAX_API_KEY}`},body:JSON.stringify({model:"abab6.5s-chat",messages:[{role:"user",content:'你好，这是一个API连接测试。请简单回复"连接成功"。'}],max_tokens:50,temperature:.1})});if(t.ok){let s=await t.json();e.minimax.status="success",e.minimax.response=s.choices?.[0]?.message?.content||"响应为空"}else{let s=await t.text();e.minimax.status="error",e.minimax.error=`HTTP ${t.status}: ${s}`}}catch(t){e.minimax.status="error",e.minimax.error=t instanceof Error?t.message:"未知错误"}try{let t=await fetch("https://api.deepseek.com/v1/chat/completions",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${process.env.DEEPSEEK_API_KEY}`},body:JSON.stringify({model:"deepseek-chat",messages:[{role:"user",content:'你好，这是一个API连接测试。请简单回复"连接成功"。'}],max_tokens:50,temperature:.1})});if(t.ok){let s=await t.json();e.deepseek.status="success",e.deepseek.response=s.choices?.[0]?.message?.content||"响应为空"}else{let s=await t.text();e.deepseek.status="error",e.deepseek.error=`HTTP ${t.status}: ${s}`}}catch(t){e.deepseek.status="error",e.deepseek.error=t instanceof Error?t.message:"未知错误"}let t={minimax_key_exists:!!process.env.MINIMAX_API_KEY,deepseek_key_exists:!!process.env.DEEPSEEK_API_KEY,minimax_key_length:process.env.MINIMAX_API_KEY?.length||0,deepseek_key_length:process.env.DEEPSEEK_API_KEY?.length||0};return(0,i.Xj)({environment:t,results:e})}catch(e){return console.error("LLM测试API错误:",e),(0,i.Vd)("LLM连接测试失败")}}async function u(e){try{let e={minimax_configured:!!process.env.MINIMAX_API_KEY,deepseek_configured:!!process.env.DEEPSEEK_API_KEY};return(0,i.Xj)({message:"API密钥环境变量检查完成",environment:e})}catch(e){return(0,i.Vd)("环境变量检查失败")}}let p=new n.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/test/llm/route",pathname:"/api/test/llm",filename:"route",bundlePath:"app/api/test/llm/route"},resolvedPagePath:"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/api/test/llm/route.ts",nextConfigOutput:"",userland:r}),{requestAsyncStorage:m,staticGenerationAsyncStorage:l,serverHooks:d,headerHooks:E,staticGenerationBailout:_}=p,h="/api/test/llm/route";function A(){return(0,a.patchFetch)({serverHooks:d,staticGenerationAsyncStorage:l})}},9413:(e,t,s)=>{s.d(t,{VR:()=>i,Vd:()=>a,Xj:()=>n,sm:()=>o});var r=s(8070);function n(e,t=200){return r.Z.json({success:!0,data:e},{status:t})}function o(e){return r.Z.json({success:!1,error:{code:"VALIDATION_ERROR",message:"请求参数验证失败",details:e.errors}},{status:400})}function a(e="服务器内部错误"){return r.Z.json({success:!1,error:{code:"INTERNAL_ERROR",message:e}},{status:500})}function i(e,t=500){return r.Z.json({success:!1,error:{code:t>=500?"INTERNAL_ERROR":"CLIENT_ERROR",message:e}},{status:t})}}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[638,206],()=>s(6950));module.exports=r})();