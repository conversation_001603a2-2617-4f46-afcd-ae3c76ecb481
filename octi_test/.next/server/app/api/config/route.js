"use strict";(()=>{var t={};t.id=729,t.ids=[729],t.modules={517:t=>{t.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},5255:(t,e,s)=>{s.r(e),s.d(e,{headerHooks:()=>w,originalPathname:()=>Z,patchFetch:()=>m,requestAsyncStorage:()=>h,routeModule:()=>g,serverHooks:()=>y,staticGenerationAsyncStorage:()=>j,staticGenerationBailout:()=>P});var r={};s.r(r),s.d(r,{GET:()=>p,POST:()=>d,PUT:()=>f});var n=s(5419),a=s(9108),o=s(9678),u=s(8070),i=s(342);let c=null;async function l(){return c||(c=i.s.getInstance(),await c.initialize()),c}async function p(t){try{let e;let{searchParams:s}=new URL(t.url),r=s.get("key"),n=s.get("batch"),a=await l();if(n){let t=n.split(",");e={action:"batch_get",batch:t}}else e={action:"get",key:r||void 0};let o=await a.handleConfigRequest(e);if(o.success)return u.Z.json(o,{status:200});return u.Z.json(o,{status:400})}catch(t){return console.error("配置API GET请求失败:",t),u.Z.json({success:!1,error:"服务器内部错误"},{status:500})}}async function d(t){try{let{action:e,key:s,value:r,batch:n}=await t.json();if(!e)return u.Z.json({success:!1,error:"缺少必要参数: action"},{status:400});let a=await l(),o=await a.handleConfigRequest({action:e,key:s,value:r,batch:n});if(o.success)return u.Z.json(o,{status:200});return u.Z.json(o,{status:400})}catch(t){return console.error("配置API POST请求失败:",t),u.Z.json({success:!1,error:"服务器内部错误"},{status:500})}}async function f(t){try{let t=await l(),e=await t.handleConfigRequest({action:"reload"});if(e.success)return u.Z.json(e,{status:200});return u.Z.json(e,{status:400})}catch(t){return console.error("配置重新加载失败:",t),u.Z.json({success:!1,error:"服务器内部错误"},{status:500})}}let g=new n.AppRouteRouteModule({definition:{kind:a.x.APP_ROUTE,page:"/api/config/route",pathname:"/api/config",filename:"route",bundlePath:"app/api/config/route"},resolvedPagePath:"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/api/config/route.ts",nextConfigOutput:"",userland:r}),{requestAsyncStorage:h,staticGenerationAsyncStorage:j,serverHooks:y,headerHooks:w,staticGenerationBailout:P}=g,Z="/api/config/route";function m(){return(0,o.patchFetch)({serverHooks:y,staticGenerationAsyncStorage:j})}}};var e=require("../../../webpack-runtime.js");e.C(t);var s=t=>e(e.s=t),r=e.X(0,[638,206,252,683,686,342],()=>s(5255));module.exports=r})();