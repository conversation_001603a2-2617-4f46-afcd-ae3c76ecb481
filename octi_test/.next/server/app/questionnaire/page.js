(()=>{var e={};e.id=453,e.ids=[453],e.modules={7849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},5403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},4749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},7384:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>i.a,__next_app__:()=>m,originalPathname:()=>x,pages:()=>d,routeModule:()=>u,tree:()=>c});var t=r(482),a=r(9108),n=r(2563),i=r.n(n),l=r(8300),o={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);r.d(s,o);let c=["",{children:["questionnaire",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,2342)),"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/questionnaire/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,1342)),"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,9361,23)),"next/dist/client/components/not-found-error"]}],d=["/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/questionnaire/page.tsx"],x="/questionnaire/page",m={require:r,loadChunk:()=>Promise.resolve()},u=new t.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/questionnaire/page",pathname:"/questionnaire",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},5534:(e,s,r)=>{Promise.resolve().then(r.t.bind(r,2583,23)),Promise.resolve().then(r.t.bind(r,6840,23)),Promise.resolve().then(r.t.bind(r,8771,23)),Promise.resolve().then(r.t.bind(r,3225,23)),Promise.resolve().then(r.t.bind(r,9295,23)),Promise.resolve().then(r.t.bind(r,3982,23))},3155:()=>{},8924:(e,s,r)=>{Promise.resolve().then(r.bind(r,5600))},5600:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>x});var t={};r.r(t);var a=r(2295),n=r(3729),i=r.n(n),l=r(8563),o=r(9956),c=r(4034);i().memo(({question:e,value:s,onChange:r,disabled:t=!1})=>{let i=(0,n.useCallback)(e=>{t||r(e)},[r,t]);return"choice"!==e.type?(console.warn("ChoiceQuestion received non-choice question type:",e.type),null):e.options&&0!==e.options.length?(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[a.jsx("h3",{className:"text-lg font-semibold text-gray-900 leading-relaxed",children:e.text}),(0,a.jsxs)("div",{className:"text-sm text-gray-500",children:[e.sub_dimension," • 单选题"]})]}),a.jsx("div",{className:"space-y-3",children:e.options.map((e,r)=>{let n=s===e.value;return a.jsx(o.Zb,{className:`p-4 cursor-pointer transition-all duration-200 hover:shadow-md ${n?"border-primary-500 bg-primary-50":"border-gray-200 hover:border-gray-300"} ${t?"opacity-50 cursor-not-allowed":""}`,onClick:()=>i(e.value),children:(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[a.jsx("div",{className:`w-4 h-4 rounded-full border-2 mt-0.5 flex-shrink-0 ${n?"border-primary-500 bg-primary-500":"border-gray-300"}`,children:n&&a.jsx("div",{className:"w-2 h-2 bg-white rounded-full m-0.5"})}),(0,a.jsxs)("div",{className:"flex-1",children:[a.jsx("div",{className:"text-gray-900 font-medium",children:e.text}),e.description&&a.jsx("div",{className:"text-sm text-gray-600 mt-1",children:e.description})]})]})},`${e.value}-${r}`)})})]}):a.jsx("div",{className:"text-red-500",children:"问题配置错误：缺少选项"})}).displayName="ChoiceQuestion";let d=({version:e="standard",organizationId:s,onComplete:r,onSave:i,className:d=""})=>{let[x,m]=(0,n.useState)(null),[u,h]=(0,n.useState)(!0),[p,g]=(0,n.useState)(null),[f,j]=(0,n.useState)([]),[v,b]=(0,n.useState)(0),[N,y]=(0,n.useState)([]),[w,C]=(0,n.useState)(!1),q=async()=>{try{h(!0),g(null);let r=await fetch("/api/questionnaire/generate",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({version:e,organizationId:s})});if(!r.ok)throw Error(`生成问卷失败: ${r.statusText}`);let t=await r.json();if(!t.success)throw Error(t.error||"生成问卷失败");m(t.data)}catch(e){console.error("加载问卷配置失败:",e),g(e instanceof Error?e.message:"加载问卷配置失败")}finally{h(!1)}},_=async()=>{if(s)try{let r=await fetch(`/api/questionnaire/responses?organizationId=${s}&version=${e}`);if(r.ok){let e=await r.json();e.success&&e.data&&j(e.data)}}catch(e){console.error("加载已保存回答失败:",e)}},k=async r=>{if(s)try{(await fetch("/api/questionnaire/responses",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({organizationId:s,version:e,responses:r})})).ok&&(j(r),i?.(r))}catch(e){console.error("保存回答失败:",e)}},P=async e=>{await k(e),r(e)},z=()=>{m(null),j([]),q()};if((0,n.useEffect)(()=>{q(),_()},[e,s]),u)return a.jsx(o.Zb,{className:`p-8 ${d}`,children:(0,a.jsxs)("div",{className:"flex flex-col items-center justify-center space-y-4",children:[a.jsx(l.gb,{size:"lg"}),(0,a.jsxs)("div",{className:"text-center",children:[a.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"正在生成问卷..."}),a.jsx("p",{className:"text-gray-600",children:"AI正在根据OCTI模型为您生成个性化问卷，请稍候"})]})]})});if(p)return a.jsx(o.Zb,{className:`p-8 ${d}`,children:(0,a.jsxs)("div",{className:"text-center space-y-4",children:[a.jsx("div",{className:"w-16 h-16 mx-auto bg-red-100 rounded-full flex items-center justify-center",children:a.jsx("svg",{className:"w-8 h-8 text-red-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:a.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})})}),(0,a.jsxs)("div",{children:[a.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"加载失败"}),a.jsx("p",{className:"text-gray-600 mb-4",children:p}),a.jsx(c.z,{onClick:z,children:"重新生成问卷"})]})]})});if(!x)return a.jsx(o.Zb,{className:`p-8 ${d}`,children:a.jsx("div",{className:"text-center text-gray-500",children:"问卷配置为空"})});(0,n.useEffect)(()=>{v+10>=N.length&&!w&&T()},[v]);let T=async()=>{C(!0);let e=await fetchNextQuestions();y(s=>[...s,...e]),C(!1)};return(0,a.jsxs)("div",{className:d,children:[a.jsx(o.Zb,{className:"p-6 mb-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[a.jsx("h2",{className:"text-xl font-semibold text-gray-900",children:"OCTI组织能力评估问卷"}),(0,a.jsxs)("p",{className:"text-gray-600 mt-1",children:["professional"===e?"专业版":"标准版"," • 共 ",x.total_questions," 题"]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[f.length>0&&(0,a.jsxs)("div",{className:"text-sm text-green-600 bg-green-50 px-3 py-1 rounded-full",children:["已保存 ",f.length," 题"]}),a.jsx(c.z,{variant:"outline",size:"sm",onClick:z,children:"重新生成"})]})]})}),a.jsx(t.default,{config:x,onComplete:P,onSave:k,initialResponses:f,autoSave:!0})]})};function x(){let[e,s]=(0,n.useState)({mode:"selection",selectedVersion:null,questionnaireConfig:null,isLoading:!1,error:null}),r=async e=>{s(s=>({...s,isLoading:!0,error:null,selectedVersion:e}));try{let r=await fetch("/api/questionnaire/generate",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({version:e,organizationType:"technology",targetAudience:"management"})}),t=await r.json();if(!t.success)throw Error(t.error?.message||"生成问卷失败");let a={version:t.data.questionnaire.version,total_questions:t.data.questionnaire.questions.length,dimensions:{SF:{questions:[]},IT:{questions:[]},MV:{questions:[]},AD:{questions:[]}}};t.data.questionnaire.questions.forEach(e=>{let s={id:e.id,dimension:e.dimension,sub_dimension:e.subdimension||"",type:e.type,text:e.text,options:e.options?.map((s,r)=>({id:`${e.id}_opt_${r}`,text:s.text||s,value:s.value||s,score:s.score||r+1}))||[],scoring:{dimension_weight:e.weight||1,sub_dimension_weight:1,option_scores:e.options?.map((e,s)=>s+1)||[],reverse_scoring:!1}};a.dimensions[s.dimension]&&a.dimensions[s.dimension].questions.push(s)}),s(e=>({...e,questionnaireConfig:a,mode:"questionnaire",isLoading:!1}))}catch(e){console.error("生成问卷失败:",e),s(s=>({...s,error:e instanceof Error?e.message:"生成问卷失败",isLoading:!1}))}};switch(e.mode){case"selection":default:return(0,a.jsxs)("div",{className:"container mx-auto px-4 py-8 max-w-4xl",children:[(0,a.jsxs)("div",{className:"text-center mb-8",children:[a.jsx("h1",{className:"text-3xl font-bold text-gray-900 mb-4",children:"OCTI 组织文化评估问卷"}),a.jsx("p",{className:"text-lg text-gray-600 mb-8",children:"选择适合您组织的评估版本，开始深入了解您的组织文化特征"})]}),(0,a.jsxs)("div",{className:"grid md:grid-cols-2 gap-6",children:[(0,a.jsxs)(o.Zb,{className:"cursor-pointer hover:shadow-lg transition-shadow border-2 hover:border-blue-500",children:[(0,a.jsxs)(o.Ol,{children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[a.jsx(o.ll,{className:"text-xl",children:"标准版"}),a.jsx("span",{className:"px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full",children:"推荐"})]}),a.jsx(o.SZ,{children:"适合大多数组织的基础评估，快速了解组织文化现状"})]}),(0,a.jsxs)(o.aY,{children:[(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx("span",{className:"w-2 h-2 bg-blue-500 rounded-full"}),a.jsx("span",{className:"text-sm",children:"约 40 道题目"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx("span",{className:"w-2 h-2 bg-green-500 rounded-full"}),a.jsx("span",{className:"text-sm",children:"预计 15-20 分钟"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx("span",{className:"w-2 h-2 bg-purple-500 rounded-full"}),a.jsx("span",{className:"text-sm",children:"适合中小型团队"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx("span",{className:"w-2 h-2 bg-orange-500 rounded-full"}),a.jsx("span",{className:"text-sm",children:"基础分析报告"})]})]}),a.jsx(c.z,{className:"w-full mt-6",onClick:()=>r("standard"),disabled:e.isLoading,children:e.isLoading&&"standard"===e.selectedVersion?a.jsx(a.Fragment,{children:"生成中..."}):"选择标准版"})]})]}),(0,a.jsxs)(o.Zb,{className:"cursor-pointer hover:shadow-lg transition-shadow border-2 hover:border-purple-500",children:[(0,a.jsxs)(o.Ol,{children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[a.jsx(o.ll,{className:"text-xl",children:"专业版"}),a.jsx("span",{className:"px-2 py-1 border border-gray-300 text-gray-700 text-xs rounded-full",children:"深度分析"})]}),a.jsx(o.SZ,{children:"深度评估组织文化，提供详细的分析和改进建议"})]}),(0,a.jsxs)(o.aY,{children:[(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx("span",{className:"w-2 h-2 bg-blue-500 rounded-full"}),a.jsx("span",{className:"text-sm",children:"约 60 道题目"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx("span",{className:"w-2 h-2 bg-green-500 rounded-full"}),a.jsx("span",{className:"text-sm",children:"预计 25-35 分钟"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx("span",{className:"w-2 h-2 bg-purple-500 rounded-full"}),a.jsx("span",{className:"text-sm",children:"适合大型组织"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx("span",{className:"w-2 h-2 bg-orange-500 rounded-full"}),a.jsx("span",{className:"text-sm",children:"详细分析报告"})]})]}),a.jsx(c.z,{className:"w-full mt-6",variant:"outline",onClick:()=>r("professional"),disabled:e.isLoading,children:e.isLoading&&"professional"===e.selectedVersion?a.jsx(a.Fragment,{children:"生成中..."}):"选择专业版"})]})]})]}),e.error&&(0,a.jsxs)("div",{className:"mt-6 p-4 bg-red-50 border border-red-200 rounded-lg",children:[a.jsx("p",{className:"text-red-600",children:e.error}),a.jsx(c.z,{variant:"outline",size:"sm",className:"mt-2",onClick:()=>s(e=>({...e,error:null})),children:"重试"})]})]});case"questionnaire":return e.questionnaireConfig?a.jsx("div",{className:"min-h-screen bg-gray-50",children:a.jsx(d,{version:e.selectedVersion||"standard",organizationId:"demo-org",onComplete:e=>{console.log("问卷完成:",e),s(e=>({...e,mode:"completed"}))}})}):null;case"completed":return a.jsx("div",{className:"container mx-auto px-4 py-8 max-w-2xl text-center",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-lg p-8",children:[(0,a.jsxs)("div",{className:"mb-6",children:[a.jsx("div",{className:"w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4",children:a.jsx("svg",{className:"w-8 h-8 text-green-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:a.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})})}),a.jsx("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:"问卷完成！"}),a.jsx("p",{className:"text-gray-600",children:"感谢您完成 OCTI 组织文化评估问卷。我们正在分析您的回答，稍后将为您生成详细的评估报告。"})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[a.jsx(c.z,{onClick:()=>{s({mode:"selection",selectedVersion:null,questionnaireConfig:null,isLoading:!1,error:null})},variant:"outline",className:"w-full",children:"重新开始评估"}),a.jsx(c.z,{className:"w-full",children:"查看评估报告"})]})]})})}}},4034:(e,s,r)=>{"use strict";r.d(s,{z:()=>c});var t=r(2295),a=r(3729),n=r.n(a),i=r(1453);let l={default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},o={default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"},c=n().forwardRef(({className:e,variant:s="default",size:r="default",loading:a=!1,disabled:n,children:c,...d},x)=>(0,t.jsxs)("button",{className:(0,i.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",l[s],o[r],e),ref:x,disabled:n||a,...d,children:[a&&(0,t.jsxs)("svg",{className:"mr-2 h-4 w-4 animate-spin",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[t.jsx("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),t.jsx("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),c]}));c.displayName="Button"},9956:(e,s,r)=>{"use strict";r.d(s,{Ol:()=>c,SZ:()=>x,Zb:()=>o,aY:()=>m,ll:()=>d});var t=r(2295),a=r(3729),n=r.n(a),i=r(1453);let l={default:"bg-card text-card-foreground",outlined:"bg-card text-card-foreground border border-border",elevated:"bg-card text-card-foreground shadow-lg"},o=n().forwardRef(({className:e,variant:s="default",...r},a)=>t.jsx("div",{ref:a,className:(0,i.cn)("rounded-lg border shadow-sm",l[s],e),...r}));o.displayName="Card";let c=n().forwardRef(({className:e,...s},r)=>t.jsx("div",{ref:r,className:(0,i.cn)("flex flex-col space-y-1.5 p-6",e),...s}));c.displayName="CardHeader";let d=n().forwardRef(({className:e,...s},r)=>t.jsx("h3",{ref:r,className:(0,i.cn)("text-2xl font-semibold leading-none tracking-tight",e),...s}));d.displayName="CardTitle";let x=n().forwardRef(({className:e,...s},r)=>t.jsx("p",{ref:r,className:(0,i.cn)("text-sm text-muted-foreground",e),...s}));x.displayName="CardDescription";let m=n().forwardRef(({className:e,...s},r)=>t.jsx("div",{ref:r,className:(0,i.cn)("p-6 pt-0",e),...s}));m.displayName="CardContent",n().forwardRef(({className:e,...s},r)=>t.jsx("div",{ref:r,className:(0,i.cn)("flex items-center p-6 pt-0",e),...s})).displayName="CardFooter"},8563:(e,s,r)=>{"use strict";r.d(s,{Ex:()=>o,gb:()=>l});var t=r(2295);r(3729);var a=r(1453);let n={sm:"h-4 w-4",md:"h-6 w-6",lg:"h-8 w-8"},i=({size:e,className:s})=>(0,t.jsxs)("svg",{className:(0,a.cn)("animate-spin",n[e],s),xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[t.jsx("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),t.jsx("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),l=({size:e="md",className:s,text:r,overlay:n=!1})=>{let l=(0,t.jsxs)("div",{className:(0,a.cn)("flex items-center justify-center",r&&"flex-col space-y-2",!r&&"space-x-2",s),children:[t.jsx(i,{size:e,className:"text-primary"}),r&&t.jsx("span",{className:"text-sm text-muted-foreground",children:r})]});return n?t.jsx("div",{className:"fixed inset-0 z-50 flex items-center justify-center bg-background/80 backdrop-blur-sm",children:l}):l},o=({value:e,max:s=100,className:r,showLabel:n=!1,size:i="md"})=>{let l=Math.min(Math.max(e/s*100,0),100);return(0,t.jsxs)("div",{className:(0,a.cn)("w-full",r),children:[n&&(0,t.jsxs)("div",{className:"flex justify-between text-sm text-muted-foreground mb-1",children:[t.jsx("span",{children:"进度"}),(0,t.jsxs)("span",{children:[Math.round(l),"%"]})]}),t.jsx("div",{className:(0,a.cn)("w-full bg-gray-200 rounded-full overflow-hidden",{sm:"h-1",md:"h-2",lg:"h-3"}[i]),children:t.jsx("div",{className:"h-full bg-primary transition-all duration-300 ease-out",style:{width:`${l}%`}})})]})}},1453:(e,s,r)=>{"use strict";r.d(s,{cn:()=>n});var t=r(6553),a=r(9976);function n(...e){return(0,a.m6)((0,t.W)(e))}},1342:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>l,metadata:()=>i});var t=r(5036),a=r(2195),n=r.n(a);r(5023);let i={title:"OCTI智能评估系统",description:"基于OCTI四维八极理论的智能组织评估平台",keywords:["OCTI","组织评估","智能评估","四维八极"],authors:[{name:"OCTI Team"}],viewport:"width=device-width, initial-scale=1",robots:"index, follow",openGraph:{title:"OCTI智能评估系统",description:"基于OCTI四维八极理论的智能组织评估平台",type:"website",locale:"zh_CN"}};function l({children:e}){return t.jsx("html",{lang:"zh-CN",className:"h-full",children:t.jsx("body",{className:`${n().className} h-full antialiased`,children:t.jsx("div",{id:"root",className:"min-h-full",children:e})})})}},2342:(e,s,r)=>{"use strict";r.r(s),r.d(s,{$$typeof:()=>n,__esModule:()=>a,default:()=>i});let t=(0,r(6843).createProxy)(String.raw`/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/questionnaire/page.tsx`),{__esModule:a,$$typeof:n}=t,i=t.default},5023:()=>{}};var s=require("../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[638,883,844],()=>r(7384));module.exports=t})();