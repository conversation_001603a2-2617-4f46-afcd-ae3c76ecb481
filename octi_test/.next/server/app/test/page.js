(()=>{var e={};e.id=928,e.ids=[928],e.modules={7849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},5403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},4749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},118:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>o.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d});var t=r(482),n=r(9108),i=r(2563),o=r.n(i),a=r(8300),l={};for(let e in a)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>a[e]);r.d(s,l);let d=["",{children:["test",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,8482)),"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,1342)),"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,9361,23)),"next/dist/client/components/not-found-error"]}],c=["/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test/page.tsx"],u="/test/page",m={require:r,loadChunk:()=>Promise.resolve()},p=new t.AppPageRouteModule({definition:{kind:n.x.APP_PAGE,page:"/test/page",pathname:"/test",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},5534:(e,s,r)=>{Promise.resolve().then(r.t.bind(r,2583,23)),Promise.resolve().then(r.t.bind(r,6840,23)),Promise.resolve().then(r.t.bind(r,8771,23)),Promise.resolve().then(r.t.bind(r,3225,23)),Promise.resolve().then(r.t.bind(r,9295,23)),Promise.resolve().then(r.t.bind(r,3982,23))},3155:()=>{},6304:(e,s,r)=>{Promise.resolve().then(r.bind(r,830))},830:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>l});var t=r(2295),n=r(3729),i=r(4034),o=r(9956),a=r(3337);function l(){let[e,s]=(0,n.useState)("questionnaire"),[r,l]=(0,n.useState)(!1),[d,c]=(0,n.useState)(null),[u,m]=(0,n.useState)({organizationType:"startup",industryContext:"technology",focusDimensions:["leadership","innovation"],questionCount:20,version:"standard"}),[p,x]=(0,n.useState)({organizationName:"测试公司",organizationType:"startup",industryContext:"technology",responses:[{questionId:"q1",score:4,dimension:"leadership",subdimension:"vision"},{questionId:"q2",score:3,dimension:"innovation",subdimension:"creativity"},{questionId:"q3",score:5,dimension:"execution",subdimension:"efficiency"}]}),f=async()=>{l(!0),c(null);try{let e=await fetch("/api/questionnaire/generate",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(u)}),s=await e.json();c(s)}catch(e){c({success:!1,error:{message:e instanceof Error?e.message:"未知错误"}})}finally{l(!1)}},g=async()=>{l(!0),c(null);try{let e=await fetch("/api/assessment/evaluate",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({...p,evaluationOptions:{version:"standard",includeRecommendations:!0,includeBenchmarking:!1,customFocus:[],dataFusion:{enabled:!0,fusionStrategy:"weighted",maxDataLength:1e4,dataSources:[]}}})}),s=await e.json();c(s)}catch(e){c({success:!1,error:{message:e instanceof Error?e.message:"未知错误"}})}finally{l(!1)}},h=async e=>{l(!0),c(null);try{let s=await fetch(e,{method:"OPTIONS"}),r=await s.json();c(r)}catch(e){c({success:!1,error:{message:e instanceof Error?e.message:"未知错误"}})}finally{l(!1)}};return(0,t.jsxs)("div",{className:"container mx-auto p-6 max-w-6xl",children:[(0,t.jsxs)("div",{className:"mb-8",children:[t.jsx("h1",{className:"text-3xl font-bold mb-2",children:"OCTI API 测试页面"}),t.jsx("p",{className:"text-muted-foreground",children:"测试问卷生成和组织评估API的功能"})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[t.jsx("div",{className:"space-y-6",children:(0,t.jsxs)("div",{className:"w-full",children:[(0,t.jsxs)("div",{className:"grid w-full grid-cols-2 mb-4",children:[t.jsx("button",{className:`px-4 py-2 text-sm font-medium rounded-l-md border ${"questionnaire"===e?"bg-primary text-primary-foreground":"bg-background text-foreground border-input"}`,onClick:()=>s("questionnaire"),children:"问卷生成"}),t.jsx("button",{className:`px-4 py-2 text-sm font-medium rounded-r-md border ${"assessment"===e?"bg-primary text-primary-foreground":"bg-background text-foreground border-input"}`,onClick:()=>s("assessment"),children:"组织评估"})]}),"questionnaire"===e&&t.jsx("div",{className:"space-y-4",children:(0,t.jsxs)(o.Zb,{children:[(0,t.jsxs)(o.Ol,{children:[t.jsx(o.ll,{children:"问卷生成 API"}),t.jsx(o.SZ,{children:"测试 /api/questionnaire/generate 端点"})]}),(0,t.jsxs)(o.aY,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[t.jsx("label",{htmlFor:"orgType",className:"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",children:"组织类型"}),(0,t.jsxs)("select",{value:u.organizationType,onChange:e=>m(s=>({...s,organizationType:e.target.value})),className:"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",children:[t.jsx("option",{value:"startup",children:"初创公司"}),t.jsx("option",{value:"sme",children:"中小企业"}),t.jsx("option",{value:"enterprise",children:"大型企业"}),t.jsx("option",{value:"nonprofit",children:"非营利组织"})]})]}),(0,t.jsxs)("div",{children:[t.jsx("label",{htmlFor:"industry",className:"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",children:"行业背景"}),(0,t.jsxs)("select",{value:u.industryContext,onChange:e=>m(s=>({...s,industryContext:e.target.value})),className:"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",children:[t.jsx("option",{value:"technology",children:"科技"}),t.jsx("option",{value:"finance",children:"金融"}),t.jsx("option",{value:"healthcare",children:"医疗"}),t.jsx("option",{value:"education",children:"教育"}),t.jsx("option",{value:"manufacturing",children:"制造业"})]})]})]}),(0,t.jsxs)("div",{children:[t.jsx("label",{htmlFor:"questionCount",className:"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",children:"问题数量"}),t.jsx(a.I,{id:"questionCount",type:"number",min:"10",max:"50",value:u.questionCount,onChange:e=>m(s=>({...s,questionCount:parseInt(e.target.value)||20}))})]}),(0,t.jsxs)("div",{children:[t.jsx("label",{className:"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",children:"关注维度"}),t.jsx("div",{className:"flex flex-wrap gap-2 mt-2",children:["leadership","innovation","execution","culture","strategy"].map(e=>t.jsx("span",{className:`inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors cursor-pointer ${u.focusDimensions.includes(e)?"border-transparent bg-primary text-primary-foreground hover:bg-primary/80":"text-foreground border-input bg-background hover:bg-accent hover:text-accent-foreground"}`,onClick:()=>{m(s=>({...s,focusDimensions:s.focusDimensions.includes(e)?s.focusDimensions.filter(s=>s!==e):[...s.focusDimensions,e]}))},children:e},e))})]}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsxs)(i.z,{onClick:f,disabled:r,className:"flex-1",children:[r&&t.jsx("div",{className:"mr-2 h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent"}),"生成问卷"]}),t.jsx(i.z,{variant:"outline",onClick:()=>h("/api/questionnaire/generate"),disabled:r,children:"获取统计"})]})]})]})}),"assessment"===e&&t.jsx("div",{className:"space-y-4",children:(0,t.jsxs)(o.Zb,{children:[(0,t.jsxs)(o.Ol,{children:[t.jsx(o.ll,{children:"组织评估 API"}),t.jsx(o.SZ,{children:"测试 /api/assessment/evaluate 端点"})]}),(0,t.jsxs)(o.aY,{className:"space-y-4",children:[(0,t.jsxs)("div",{children:[t.jsx("label",{htmlFor:"orgName",className:"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",children:"组织名称"}),t.jsx(a.I,{id:"orgName",value:p.organizationName,onChange:e=>x(s=>({...s,organizationName:e.target.value}))})]}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[t.jsx("label",{className:"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",children:"组织类型"}),(0,t.jsxs)("select",{value:p.organizationType,onChange:e=>x(s=>({...s,organizationType:e.target.value})),className:"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",children:[t.jsx("option",{value:"startup",children:"初创公司"}),t.jsx("option",{value:"sme",children:"中小企业"}),t.jsx("option",{value:"enterprise",children:"大型企业"})]})]}),(0,t.jsxs)("div",{children:[t.jsx("label",{className:"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",children:"行业背景"}),(0,t.jsxs)("select",{value:p.industryContext,onChange:e=>x(s=>({...s,industryContext:e.target.value})),className:"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",children:[t.jsx("option",{value:"technology",children:"科技"}),t.jsx("option",{value:"finance",children:"金融"}),t.jsx("option",{value:"healthcare",children:"医疗"})]})]})]}),(0,t.jsxs)("div",{children:[t.jsx("label",{className:"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",children:"评估响应数据 (JSON)"}),t.jsx("textarea",{value:JSON.stringify(p.responses,null,2),onChange:e=>{try{let s=JSON.parse(e.target.value);x(e=>({...e,responses:s}))}catch{}},rows:6,className:"flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 font-mono"})]}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsxs)(i.z,{onClick:g,disabled:r,className:"flex-1",children:[r&&t.jsx("div",{className:"mr-2 h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent"}),"生成评估"]}),t.jsx(i.z,{variant:"outline",onClick:()=>h("/api/assessment/evaluate"),disabled:r,children:"获取统计"})]})]})]})})]})}),t.jsx("div",{children:(0,t.jsxs)(o.Zb,{className:"h-fit",children:[t.jsx(o.Ol,{children:(0,t.jsxs)(o.ll,{className:"flex items-center gap-2",children:["API 响应结果",d&&(d.success?t.jsx("svg",{className:"h-5 w-5 text-green-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:t.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})}):t.jsx("svg",{className:"h-5 w-5 text-red-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:t.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"})}))]})}),t.jsx(o.aY,{children:r?(0,t.jsxs)("div",{className:"flex items-center justify-center py-8",children:[t.jsx("div",{className:"h-8 w-8 animate-spin rounded-full border-2 border-current border-t-transparent"}),t.jsx("span",{className:"ml-2",children:"处理中..."})]}):d?(0,t.jsxs)("div",{className:"space-y-4",children:[d.success?t.jsx("div",{className:"rounded-lg border border-green-200 bg-green-50 p-4",children:(0,t.jsxs)("div",{className:"flex items-center",children:[t.jsx("svg",{className:"h-4 w-4 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:t.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})}),t.jsx("span",{className:"ml-2 text-sm text-green-800",children:"API 调用成功！"})]})}):t.jsx("div",{className:"rounded-lg border border-red-200 bg-red-50 p-4",children:(0,t.jsxs)("div",{className:"flex items-center",children:[t.jsx("svg",{className:"h-4 w-4 text-red-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:t.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"})}),(0,t.jsxs)("span",{className:"ml-2 text-sm text-red-800",children:["API 调用失败：",d.error?.message||"未知错误"]})]})}),(0,t.jsxs)("div",{children:[t.jsx("label",{className:"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",children:"响应数据"}),t.jsx("pre",{className:"mt-2 p-4 bg-muted rounded-lg text-sm overflow-auto max-h-96",children:JSON.stringify(d,null,2)})]})]}):t.jsx("div",{className:"text-center py-8 text-muted-foreground",children:"选择一个API端点并点击按钮来测试"})})]})})]})]})}},4034:(e,s,r)=>{"use strict";r.d(s,{z:()=>d});var t=r(2295),n=r(3729),i=r.n(n),o=r(1453);let a={default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},l={default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"},d=i().forwardRef(({className:e,variant:s="default",size:r="default",loading:n=!1,disabled:i,children:d,...c},u)=>(0,t.jsxs)("button",{className:(0,o.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",a[s],l[r],e),ref:u,disabled:i||n,...c,children:[n&&(0,t.jsxs)("svg",{className:"mr-2 h-4 w-4 animate-spin",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[t.jsx("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),t.jsx("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),d]}));d.displayName="Button"},9956:(e,s,r)=>{"use strict";r.d(s,{Ol:()=>d,SZ:()=>u,Zb:()=>l,aY:()=>m,ll:()=>c});var t=r(2295),n=r(3729),i=r.n(n),o=r(1453);let a={default:"bg-card text-card-foreground",outlined:"bg-card text-card-foreground border border-border",elevated:"bg-card text-card-foreground shadow-lg"},l=i().forwardRef(({className:e,variant:s="default",...r},n)=>t.jsx("div",{ref:n,className:(0,o.cn)("rounded-lg border shadow-sm",a[s],e),...r}));l.displayName="Card";let d=i().forwardRef(({className:e,...s},r)=>t.jsx("div",{ref:r,className:(0,o.cn)("flex flex-col space-y-1.5 p-6",e),...s}));d.displayName="CardHeader";let c=i().forwardRef(({className:e,...s},r)=>t.jsx("h3",{ref:r,className:(0,o.cn)("text-2xl font-semibold leading-none tracking-tight",e),...s}));c.displayName="CardTitle";let u=i().forwardRef(({className:e,...s},r)=>t.jsx("p",{ref:r,className:(0,o.cn)("text-sm text-muted-foreground",e),...s}));u.displayName="CardDescription";let m=i().forwardRef(({className:e,...s},r)=>t.jsx("div",{ref:r,className:(0,o.cn)("p-6 pt-0",e),...s}));m.displayName="CardContent",i().forwardRef(({className:e,...s},r)=>t.jsx("div",{ref:r,className:(0,o.cn)("flex items-center p-6 pt-0",e),...s})).displayName="CardFooter"},3337:(e,s,r)=>{"use strict";r.d(s,{I:()=>a});var t=r(2295),n=r(3729),i=r.n(n),o=r(1453);let a=i().forwardRef(({className:e,type:s="text",error:r,label:n,helperText:i,id:a,...l},d)=>{let c=a||`input-${Math.random().toString(36).substring(2,9)}`;return(0,t.jsxs)("div",{className:"w-full",children:[n&&t.jsx("label",{htmlFor:c,className:"block text-sm font-medium text-gray-700 mb-1",children:n}),t.jsx("input",{type:s,id:c,className:(0,o.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",r&&"border-red-500 focus-visible:ring-red-500",e),ref:d,...l}),r&&t.jsx("p",{className:"mt-1 text-sm text-red-600",children:r}),i&&!r&&t.jsx("p",{className:"mt-1 text-sm text-gray-500",children:i})]})});a.displayName="Input"},1453:(e,s,r)=>{"use strict";r.d(s,{cn:()=>i});var t=r(6553),n=r(9976);function i(...e){return(0,n.m6)((0,t.W)(e))}},1342:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>a,metadata:()=>o});var t=r(5036),n=r(2195),i=r.n(n);r(5023);let o={title:"OCTI智能评估系统",description:"基于OCTI四维八极理论的智能组织评估平台",keywords:["OCTI","组织评估","智能评估","四维八极"],authors:[{name:"OCTI Team"}],viewport:"width=device-width, initial-scale=1",robots:"index, follow",openGraph:{title:"OCTI智能评估系统",description:"基于OCTI四维八极理论的智能组织评估平台",type:"website",locale:"zh_CN"}};function a({children:e}){return t.jsx("html",{lang:"zh-CN",className:"h-full",children:t.jsx("body",{className:`${i().className} h-full antialiased`,children:t.jsx("div",{id:"root",className:"min-h-full",children:e})})})}},8482:(e,s,r)=>{"use strict";r.r(s),r.d(s,{$$typeof:()=>i,__esModule:()=>n,default:()=>o});let t=(0,r(6843).createProxy)(String.raw`/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test/page.tsx`),{__esModule:n,$$typeof:i}=t,o=t.default},5023:()=>{}};var s=require("../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[638,883,844],()=>r(118));module.exports=t})();