"use strict";exports.id=683,exports.ids=[683],exports.modules={3966:(e,t,s)=>{s.d(t,{Yd:()=>r});class r{constructor(e="App"){this.context=e}formatMessage(e,t,s){let r=new Date().toISOString(),o=s?` ${JSON.stringify(s)}`:"";return`[${r}] [${e.toUpperCase()}] [${this.context}] ${t}${o}`}debug(e,t){"debug"===process.env.LOG_LEVEL&&console.debug(this.formatMessage("debug",e,t))}info(e,t){console.info(this.formatMessage("info",e,t))}warn(e,t){console.warn(this.formatMessage("warn",e,t))}error(e,t){console.error(this.formatMessage("error",e,t))}setContext(e){this.context=e}}new r("OCTI")},3320:(e,t,s)=>{s.d(t,{e:()=>a});var r=s(3966),o=s(5252);let n=o.Ry({sourceId:o.Z_(),content:o.Z_(),contentType:o.Km(["text","json","pdf","docx","url"]),metadata:o.Ry({timestamp:o.Z_().optional(),reliability:o.Rx().min(0).max(1).optional(),source:o.Z_().optional()}).optional()}),i=o.Ry({strategy:o.Km(["weighted","priority","consensus"]).default("weighted"),weights:o.IM(o.Rx()).optional(),threshold:o.Rx().min(0).max(1).default(.7),maxSources:o.Rx().positive().default(5)});class a{constructor(e={}){this.logger=new r.Yd("DataFusionEngine"),this.config={strategy:"weighted",threshold:.7,maxSources:5,weights:{},enableCache:!0,cacheTimeout:3600,...e}}getDefaultConfig(){return{strategy:"weighted",threshold:.7,maxSources:5,weights:{},enableCache:!0,cacheTimeout:3600}}async fuseData(e,t={}){try{let s=e.map(e=>n.parse(e)),r=i.parse(t);this.logger.info(`开始数据融合，共 ${s.length} 个数据源`);let o=await this.preprocessSources(s),a=await this.applyFusionStrategy(o,r),l=this.calculateConfidence(o,a);return this.logger.info("数据融合完成",{sourcesCount:s.length,confidence:l,strategy:r.strategy}),{fusedContent:a.content,confidence:l,sourcesUsed:a.sourcesUsed,metadata:{strategy:r.strategy,processedAt:new Date().toISOString(),originalSourcesCount:s.length,qualityScore:this.assessDataQuality(o)}}}catch(e){throw this.logger.error("数据融合失败",{error:e}),Error(`数据融合失败: ${e}`)}}async preprocessSources(e){let t=[];for(let s of e)try{let e=s.content;switch(s.contentType){case"json":e=this.processJsonContent(s.content);break;case"pdf":case"docx":e=await this.processDocumentContent(s.content);break;case"url":e=await this.processUrlContent(s.content);break;default:e=this.processTextContent(s.content)}let r=this.assessContentQuality(e),o=this.assessContentRelevance(e);t.push({...s,processedContent:e,qualityScore:r,relevanceScore:o})}catch(e){this.logger.warn(`数据源预处理失败: ${s.sourceId}`,{error:e})}return t}async applyFusionStrategy(e,t){let s=e.filter(e=>e.qualityScore>=t.threshold).slice(0,t.maxSources);if(0===s.length)throw Error("没有符合质量要求的数据源");switch(t.strategy){case"weighted":return this.weightedFusion(s,t.weights||{});case"priority":return this.priorityFusion(s);case"consensus":return this.consensusFusion(s);default:throw Error(`不支持的融合策略: ${t.strategy}`)}}weightedFusion(e,t){let s="# 数据融合报告\n\n",r=[];for(let o of e.sort((e,s)=>{let r=t[e.sourceId]||e.qualityScore;return(t[s.sourceId]||s.qualityScore)-r})){let e=t[o.sourceId]||o.qualityScore;s+=`## 数据源: ${o.sourceId} (权重: ${e.toFixed(2)})

${o.processedContent}

`,r.push(o.sourceId)}return{content:s,sourcesUsed:r}}priorityFusion(e){let t=e.sort((e,t)=>{let s=(e.qualityScore+e.relevanceScore)/2;return(t.qualityScore+t.relevanceScore)/2-s}),s="# 优先级数据融合报告\n\n",r=[];for(let e of t){let t=(e.qualityScore+e.relevanceScore)/2;s+=`## 数据源: ${e.sourceId} (评分: ${t.toFixed(2)})

${e.processedContent}

`,r.push(e.sourceId)}return{content:s,sourcesUsed:r}}consensusFusion(e){let t=e.map(e=>e.processedContent).join(" "),s=this.extractKeywords(t),r=this.identifyCommonThemes(e.map(e=>e.processedContent)),o="# 共识数据融合报告\n\n";o+=`## 关键词汇
${s.join(", ")}

## 共同主题
${r.join("\n")}

## 详细内容

`;let n=[];for(let t of e)o+=`### ${t.sourceId}
${t.processedContent}

`,n.push(t.sourceId);return{content:o,sourcesUsed:n}}processJsonContent(e){try{let t=JSON.parse(e);return JSON.stringify(t,null,2)}catch{return e}}processTextContent(e){return e.replace(/\s+/g," ").replace(/\n\s*\n/g,"\n\n").trim()}async processDocumentContent(e){return e}async processUrlContent(e){return e}assessContentQuality(e){let t=.5;e.length>100&&(t+=.1),e.length>500&&(t+=.1),e.length>1e3&&(t+=.1),e.includes("\n")&&(t+=.05),/[.!?]/.test(e)&&(t+=.05);let s=new Set(e.toLowerCase().split(/\s+/)).size;return s>50&&(t+=.1),s>100&&(t+=.1),Math.min(t,1)}assessContentRelevance(e){let t=["组织","团队","领导","管理","文化","能力","评估","结构化","灵活","直觉","思考","愿景","行动","深思"],s=e.toLowerCase();return Math.min(t.filter(e=>s.includes(e)).length/t.length*2,1)}calculateConfidence(e,t){if(0===e.length)return 0;let s=e.slice(0,t.sourcesUsed.length);return s.reduce((e,t)=>e+t.qualityScore,0)/s.length*.4+s.reduce((e,t)=>e+t.relevanceScore,0)/s.length*.4+.2*Math.min(s.length/3,1)}assessDataQuality(e){return 0===e.length?0:e.reduce((e,t)=>e+t.qualityScore,0)/e.length}extractKeywords(e){let t=e.toLowerCase().replace(/[^\w\s\u4e00-\u9fff]/g," ").split(/\s+/).filter(e=>e.length>2),s=new Map;for(let e of t)s.set(e,(s.get(e)||0)+1);return Array.from(s.entries()).sort((e,t)=>t[1]-e[1]).slice(0,10).map(([e])=>e)}identifyCommonThemes(e){let t=[];return t.push("组织能力评估相关内容"),t}}},2259:(e,t,s)=>{s.d(t,{e:()=>i});var r=s(3966),o=s(5252);o.Ry({model:o.Z_(),messages:o.IX(o.Ry({role:o.Km(["system","user","assistant"]),content:o.Z_()})),temperature:o.Rx().min(0).max(2).optional(),maxTokens:o.Rx().positive().optional(),stream:o.O7().optional()});let n=o.Ry({id:o.Z_(),choices:o.IX(o.Ry({message:o.Ry({role:o.Z_(),content:o.Z_()}),finishReason:o.Z_().optional()})),usage:o.Ry({promptTokens:o.Rx(),completionTokens:o.Rx(),totalTokens:o.Rx()}).optional()});class i{constructor(e={}){this.logger=new r.Yd("LLMApiClient"),this.config={defaultTimeout:3e4,maxRetries:3,minimax:{apiKey:process.env.MINIMAX_API_KEY,baseUrl:process.env.MINIMAX_BASE_URL||"https://api.minimax.chat",timeout:3e4,...e.minimax},deepseek:{apiKey:process.env.DEEPSEEK_API_KEY,baseUrl:process.env.DEEPSEEK_BASE_URL||"https://api.deepseek.com",timeout:3e4,...e.deepseek},...e}}getDefaultConfig(){return{defaultTimeout:3e4,maxRetries:3,minimax:{apiKey:process.env.MINIMAX_API_KEY,baseUrl:process.env.MINIMAX_BASE_URL||"https://api.minimax.chat",timeout:3e4},deepseek:{apiKey:process.env.DEEPSEEK_API_KEY,baseUrl:process.env.DEEPSEEK_BASE_URL||"https://api.deepseek.com",timeout:3e4}}}async callMiniMax(e){let t=process.env.MINIMAX_API_KEY;if(!t)throw Error("MiniMax API密钥未配置");return this.makeRequest(`${this.MINIMAX_BASE_URL}/text/chatcompletion`,{...e,model:e.model||"abab6.5-chat"},{Authorization:`Bearer ${t}`,"Content-Type":"application/json"})}async callDeepSeek(e){let t=process.env.DEEPSEEK_API_KEY;if(!t)throw Error("DeepSeek API密钥未配置");return this.makeRequest(`${this.DEEPSEEK_BASE_URL}/chat/completions`,{...e,model:e.model||"deepseek-chat"},{Authorization:`Bearer ${t}`,"Content-Type":"application/json"})}async makeRequest(e,t,s){let r=null;for(let o=1;o<=this.MAX_RETRIES;o++)try{this.logger.debug(`LLM API请求 (尝试 ${o}/${this.MAX_RETRIES})`,{url:e.replace(/\/v1.*/,"/v1/***"),model:t.model});let r=new AbortController,i=setTimeout(()=>r.abort(),this.DEFAULT_TIMEOUT),a=await fetch(e,{method:"POST",headers:s,body:JSON.stringify(t),signal:r.signal});if(clearTimeout(i),!a.ok){let e=await a.text();throw Error(`HTTP ${a.status}: ${e}`)}let l=await a.json(),c=n.parse(l);return this.logger.info("LLM API调用成功",{model:t.model,tokensUsed:c.usage?.totalTokens||0,attempt:o}),c}catch(e){if(r=e,this.logger.warn(`LLM API调用失败 (尝试 ${o}/${this.MAX_RETRIES})`,{error:r.message,model:t.model}),o<this.MAX_RETRIES){let e=1e3*Math.pow(2,o);await new Promise(t=>setTimeout(t,e))}}throw this.logger.error("LLM API调用最终失败",{error:r?.message,attempts:this.MAX_RETRIES}),r||Error("LLM API调用失败")}async healthCheck(){let e={minimax:!1,deepseek:!1};try{await this.callMiniMax({model:"abab6.5-chat",messages:[{role:"user",content:"ping"}],maxTokens:10}),e.minimax=!0}catch(e){this.logger.warn("MiniMax健康检查失败",{error:e})}try{await this.callDeepSeek({model:"deepseek-chat",messages:[{role:"user",content:"ping"}],maxTokens:10}),e.deepseek=!0}catch(e){this.logger.warn("DeepSeek健康检查失败",{error:e})}return e}}},335:(e,t,s)=>{s.d(t,{Z:()=>i});var r=s(3966),o=s(5252);let n=o.Ry({id:o.Z_(),name:o.Z_(),systemPrompt:o.Z_(),userPromptTemplate:o.Z_(),variables:o.IX(o.Z_()),version:o.Z_().optional(),metadata:o.IM(o.Yj()).optional()});class i{constructor(e={}){this.logger=new r.Yd("PromptBuilder"),this.templates=new Map,this.config={templatePath:"./config/prompts",cacheEnabled:!0,maxTokens:4e3,temperature:.7,...e}}getDefaultConfig(){return{templatePath:"./config/prompts",cacheEnabled:!0,maxTokens:4e3,temperature:.7}}initializeDefaultTemplates(){this.addTemplate({id:"questionnaire_designer",name:"问卷设计师",systemPrompt:`你是OCTI组织能力评估系统的专业问卷设计师。你的任务是根据用户需求设计高质量的评估问卷。

核心要求：
1. 严格遵循OCTI四维八极理论框架
2. 问题设计要具有科学性和专业性
3. 确保问题的区分度和信效度
4. 输出标准JSON格式的问卷结构

OCTI四维八极框架：
- S/F维度：结构化 ↔ 灵活化
- I/T维度：直觉 ↔ 思考  
- M/V维度：管理 ↔ 愿景
- A/D维度：行动 ↔ 深思

请确保每个维度的问题数量均衡，问题表述清晰准确。`,userPromptTemplate:`请为以下评估需求设计问卷：

评估类型：{{assessmentType}}
目标维度：{{dimensions}}
问卷版本：{{version}}
特殊要求：{{requirements}}

请生成包含以下结构的JSON格式问卷：
{
  "id": "问卷唯一标识",
  "title": "问卷标题",
  "description": "问卷描述",
  "version": "版本信息",
  "questions": [
    {
      "id": "问题ID",
      "text": "问题内容",
      "type": "问题类型",
      "dimension": "所属维度",
      "subdimension": "子维度",
      "options": [选项数组],
      "weight": 权重值
    }
  ],
  "metadata": {
    "estimatedTime": 预估完成时间,
    "totalQuestions": 问题总数,
    "dimensionDistribution": 维度分布统计
  }
}`,variables:["assessmentType","dimensions","version","requirements"]}),this.addTemplate({id:"organization_tutor_standard",name:"组织评估导师-标准版",systemPrompt:`你是OCTI组织能力评估系统的专业分析师。你的任务是基于问卷回答结果，提供深入的组织能力分析和改进建议。

分析框架：
1. OCTI四维八极得分计算
2. 组织能力优势识别
3. 改进机会分析
4. 具体行动建议

输出要求：
- 客观准确的数据分析
- 实用可行的改进建议
- 结构化的报告格式
- 专业的语言表达`,userPromptTemplate:`请分析以下组织评估数据：

组织信息：{{organizationInfo}}
问卷回答：{{responses}}
评估版本：{{version}}

请提供包含以下内容的分析报告：
1. 总体评估得分
2. 四维八极详细分析
3. 组织优势总结
4. 改进建议
5. 下一步行动计划`,variables:["organizationInfo","responses","version"]}),this.logger.info(`已加载 ${this.templates.size} 个默认提示词模板`)}addTemplate(e){let t=n.parse(e);this.templates.set(t.id,t),this.logger.debug(`已添加提示词模板: ${t.id}`)}getTemplate(e){return this.templates.get(e)||null}buildPrompt(e,t){let s=this.getTemplate(e);if(!s)throw Error(`提示词模板不存在: ${e}`);let r=s.variables.filter(e=>!(e in t)||void 0===t[e]);if(r.length>0)throw Error(`缺少必需变量: ${r.join(", ")}`);let o=s.userPromptTemplate;for(let[e,s]of Object.entries(t)){let t=`{{${e}}}`,r="string"==typeof s?s:JSON.stringify(s);o=o.replace(RegExp(t,"g"),r)}return this.logger.debug(`已构建提示词: ${e}`,{variablesUsed:Object.keys(t)}),{systemPrompt:s.systemPrompt,userPrompt:o}}listTemplates(){return Array.from(this.templates.values())}validatePromptSafety(e){let t=[];for(let s of[/ignore\s+previous\s+instructions/i,/system\s*:\s*you\s+are\s+now/i,/forget\s+everything/i,/new\s+instructions/i,/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,/javascript:/i,/on\w+\s*=/i])s.test(e)&&t.push(`检测到潜在的注入攻击模式: ${s.source}`);for(let s of[/api[_-]?key/i,/password/i,/secret/i,/token/i,/credential/i])s.test(e)&&t.push(`检测到可能的敏感信息: ${s.source}`);return{safe:0===t.length,issues:t}}}}};