"use strict";exports.id=686,exports.ids=[686],exports.modules={3686:(e,t,i)=>{i.d(t,{o:()=>s});let n=new(i(3966)).Yd("QuestionDesignerAgent");class s{constructor(e,t,i){this.name="question_designer",this.questionCache=new Map,this.isInitialized=!1,this.llmClient=e,this.promptBuilder=t,this.dataFusionEngine=i}async initialize(){if(this.isInitialized){n.warn("QuestionDesignerAgent 已经初始化");return}try{if(!this.llmClient)throw Error("LLMApiClient 未正确初始化");if(!this.promptBuilder)throw Error("PromptBuilder 未正确初始化");this.questionCache.clear(),this.isInitialized=!0,n.info("QuestionDesignerAgent 初始化完成")}catch(e){throw n.error("QuestionDesignerAgent 初始化失败",{error:e}),e}}isInit(){return this.isInitialized}getStatus(){return{name:this.name,initialized:this.isInitialized,lastActivity:new Date,config:{}}}async designQuestionnaire(e){if(!this.isInitialized)throw Error("智能体未初始化，请先调用 initialize()");let t=Date.now();try{let i;let s=this.generateCacheKey(e);if(this.questionCache.has(s))return n.debug("Using cached questionnaire"),this.questionCache.get(s);n.info("Starting questionnaire design",{version:e.version,organizationType:e.organizationType,hasExternalData:!!e.externalData?.length});let r=[];if("professional"===e.version&&e.externalData&&this.dataFusionEngine){let t=await this.dataFusionEngine.processRawData(e.externalData);r=(await this.dataFusionEngine.fuseData(t)).metadata.dataSourcesUsed}let a={version:e.version,agentType:"question_designer",context:{organizationType:e.organizationType,industryContext:e.industryContext,targetAudience:e.targetAudience,customRequirements:e.customRequirements},externalData:e.externalData?.map(e=>({source:e.sourceId,content:e.content,type:e.contentType})),dataFusion:e.dataFusion},o=await this.promptBuilder.buildPrompt(a),u={messages:[{role:"system",content:o.systemPrompt},{role:"user",content:o.userPrompt}],temperature:.7,maxTokens:"professional"===e.version?6e3:4e3};i="professional"===e.version?(await this.llmClient.dualModelChat(u,"sequential")).secondary:await this.llmClient.chat("deepseek",u);let l=await this.parseLLMResponse(i.content,e,r),c=await this.validateQuestionnaire(l);if(!c.isValid&&(n.warn("Generated questionnaire failed validation",{errors:c.errors,qualityScore:c.qualityScore}),c.qualityScore<.6))return this.regenerateQuestionnaire(e,c.suggestions);this.questionCache.set(s,l);let d=Date.now()-t;return n.info("Questionnaire design completed",{questionnaireId:l.id,totalQuestions:l.questions.length,version:l.version,qualityScore:c.qualityScore,processingTime:d}),l}catch(t){throw n.error("Questionnaire design failed",{error:t,options:e}),Error(`Failed to design questionnaire: ${t instanceof Error?t.message:"Unknown error"}`)}}async parseLLMResponse(e,t,i){try{let s;let r=e.match(/```json\s*([\s\S]*?)\s*```/)||e.match(/\{[\s\S]*\}/);if(r){let t=r[1]||r[0];try{s=JSON.parse(t)}catch(i){n.warn("JSON解析失败，尝试结构化解析",{content:t.substring(0,200),error:i}),s=this.parseStructuredResponse(e)}}else s=this.parseStructuredResponse(e);if(!s||"object"!=typeof s)throw Error("LLM响应解析结果无效");let a={id:this.generateQuestionnaireId(),version:t.version,title:s.title||`OCTI组织文化评估问卷（${"professional"===t.version?"专业版":"标准版"}）`,description:s.description||"OCTI四维八极框架组织文化评估",instructions:s.instructions||"请根据您的实际情况如实回答以下问题",questions:this.parseQuestions(s.questions||[]),metadata:{totalQuestions:(s.questions||[]).length,estimatedTime:this.calculateEstimatedTime(s.questions||[]),difficulty:this.calculateDifficulty(s.questions||[]),createdAt:new Date,framework:"OCTI四维八极",dataSourcesUsed:i.length>0?i:void 0}};if(0===a.questions.length)throw Error("生成的问卷没有包含任何问题");return a}catch(t){throw n.error("LLM响应解析失败",{error:t instanceof Error?t.message:String(t),contentPreview:e.substring(0,500)}),Error(`问卷解析失败: ${t instanceof Error?t.message:"未知错误"}`)}}parseStructuredResponse(e){let t=e.split("\n").filter(e=>e.trim()),i={title:"",description:"",instructions:"",questions:[]},n=null;for(let e of t){let t=e.trim();t.includes("标题")||t.includes("title")?i.title=this.extractValue(t):t.includes("描述")||t.includes("description")?i.description=this.extractValue(t):t.includes("问题")&&t.includes(":")&&(n&&i.questions.push(n),n={id:`q_${i.questions.length+1}`,text:this.extractValue(t),type:"likert_scale",options:[]})}return n&&i.questions.push(n),i}extractValue(e){let t=e.indexOf(":");return t>-1?e.substring(t+1).trim():e}parseQuestions(e){return e.map((e,t)=>{let i={id:e.id||`q_${t+1}`,dimension:e.dimension||this.inferDimension(e.text),subdimension:e.subdimension||"",type:e.type||this.inferQuestionType(e.text),depth:e.depth||"intermediate",text:e.text||e.question||"",options:e.options,scale:e.scale,reversed:e.reversed||!1,weight:e.weight||1,metadata:{difficulty:e.difficulty||.5,discriminationIndex:e.discriminationIndex||.5,expectedResponseTime:e.expectedResponseTime||30}};return"single_choice"!==i.type&&"multiple_choice"!==i.type||i.options||(i.options=this.generateDefaultOptions(i.type)),"likert_scale"!==i.type||i.scale||(i.scale={min:1,max:5,labels:["完全不同意","不同意","中立","同意","完全同意"]}),i})}inferQuestionType(e){return e.includes("排序")||e.includes("排列")?"ranking":e.includes("多选")||e.includes("可以选择多个")?"multiple_choice":e.includes("同意")||e.includes("程度")?"likert_scale":e.includes("描述")||e.includes("说明")||e.includes("举例")?"open_ended":"single_choice"}inferDimension(e){return e.includes("权力")||e.includes("等级")||e.includes("层级")?"权力距离":e.includes("个人")||e.includes("集体")||e.includes("团队")?"个人主义vs集体主义":e.includes("竞争")||e.includes("合作")||e.includes("成就")?"男性化vs女性化":e.includes("不确定")||e.includes("风险")||e.includes("变化")?"不确定性规避":"综合"}generateDefaultOptions(e){return"single_choice"===e||"multiple_choice"===e?["完全不符合","基本不符合","部分符合","基本符合","完全符合"]:[]}async validateQuestionnaire(e){let t;let i=[],n=[],s=[];e.title||i.push("问卷标题不能为空"),0===e.questions.length&&i.push("问卷必须包含至少一个问题");let r="professional"===e.version?40:20;for(let t of(e.questions.length<.8*r&&n.push(`问题数量偏少，建议至少${r}个问题`),new Set(e.questions.map(e=>e.dimension)).size<4&&i.push("问卷应覆盖OCTI四个维度"),this.analyzeQuestionTypes(e.questions).likert_scale<.5&&s.push("建议增加更多量表题以提高测量精度"),e.questions))(!t.text||t.text.length<10)&&i.push(`问题${t.id}内容过短`),"single_choice"===t.type&&(!t.options||t.options.length<3)&&i.push(`问题${t.id}选项不足`);return t=Math.max(0,t=1-.2*i.length-.1*n.length),{isValid:0===i.length,errors:i,warnings:n,suggestions:s,qualityScore:t}}async regenerateQuestionnaire(e,t){n.info("Regenerating questionnaire with improvements",{suggestions:t});let i={...e,customRequirements:[e.customRequirements||"","请特别注意以下改进建议：",...t].filter(Boolean).join("\n")};return this.designQuestionnaire(i)}analyzeQuestionTypes(e){let t={single_choice:0,multiple_choice:0,likert_scale:0,open_ended:0,ranking:0};e.forEach(e=>{t[e.type]=(t[e.type]||0)+1});let i=e.length;return Object.keys(t).forEach(e=>{t[e]=t[e]/i}),t}calculateEstimatedTime(e){return e.reduce((e,t)=>{let i="open_ended"===t.type?60:30;return e+(t.expectedResponseTime||i)},0)}calculateDifficulty(e){return 0===e.length?.5:e.reduce((e,t)=>e+(t.difficulty||.5),0)/e.length}generateQuestionnaireId(){return`questionnaire_${Date.now()}_${Math.random().toString(36).substr(2,9)}`}generateCacheKey(e){return[e.version,e.organizationType||"default",e.industryContext||"default",e.targetAudience||"default",e.customRequirements||"default"].join("_").replace(/[^a-zA-Z0-9_]/g,"_")}async getQuestionnairePreview(e){for(let t of Array.from(this.questionCache.values()))if(t.id===e)return{title:t.title,description:t.description,questionCount:t.questions.length,estimatedTime:t.metadata.estimatedTime,dimensions:Array.from(new Set(t.questions.map(e=>e.dimension)))};return null}clearCache(){this.questionCache.clear(),n.info("QuestionDesignerAgent cache cleared")}getStats(){let e=Array.from(this.questionCache.values()),t={};return e.forEach(e=>{t[e.version]=(t[e.version]||0)+1}),{cacheSize:this.questionCache.size,totalQuestionnaires:e.length,versionDistribution:t}}async generateQuestionnaireBatched(e){let t=["SF","IT","MV","AD"],i={version:e.version,total_questions:60,dimensions:{SF:{questions:[]},IT:{questions:[]},MV:{questions:[]},AD:{questions:[]}}},n=t.map(t=>this.generateDimensionBatch(t,0,5,e));return(await Promise.all(n)).forEach((e,n)=>{let s=t[n];i.dimensions[s].questions.push(...e)}),this.generateRemainingQuestions(i,e),i}async generateRemainingQuestions(e,t){}}}};