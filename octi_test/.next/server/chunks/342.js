"use strict";exports.id=342,exports.ids=[342],exports.modules={4100:(e,t,s)=>{s.d(t,{S:()=>i});let a=new(s(3966)).Yd("OrganizationTutorAgent");class i{constructor(e,t,s){this.resultCache=new Map,this.cacheExpiry=864e5,this.isInitialized=!1,this.llmClient=e,this.promptBuilder=t,this.dataFusionEngine=s}async initialize(){if(this.isInitialized){a.warn("OrganizationTutorAgent 已经初始化");return}try{if(!this.llmClient)throw Error("LLMApiClient 是必需的依赖项");if(!this.promptBuilder)throw Error("PromptBuilder 是必需的依赖项");if(!this.dataFusionEngine)throw Error("DataFusionEngine 是必需的依赖项");this.cleanExpiredCache(),this.isInitialized=!0,a.info("OrganizationTutorAgent 初始化完成")}catch(e){throw a.error("OrganizationTutorAgent 初始化失败",{error:e}),e}}async generateAssessment(e,t={version:"standard",analysisMode:"basic",includeRecommendations:!0,outputLanguage:"zh"}){if(!this.isInitialized)throw Error("OrganizationTutorAgent 未初始化");if(!e||!e.organizationId)throw Error("评估数据无效：缺少组织ID");if(!e.responses||0===e.responses.length)throw Error("评估数据无效：缺少响应数据");let s=Date.now();try{let i;let r=this.generateCacheKey(e,t),n=this.resultCache.get(r);if(n&&Date.now()-n.timestamp<this.cacheExpiry)return a.info("使用缓存的评估结果",{cacheKey:r}),n.data;let o=this.preprocessAssessmentData(e),c=null;t.externalData&&t.externalData.length>0&&(c=await this.dataFusionEngine.fuseData(t.externalData.map(e=>({sourceId:e.source,content:JSON.stringify(e.data),contentType:e.type,metadata:{timestamp:e.timestamp,reliability:e.reliability}})),t.dataFusion));let l={version:t.version,agentType:"organization_tutor",context:{assessmentData:o,analysisMode:t.analysisMode,includeRecommendations:t.includeRecommendations,customFocus:t.customFocus,outputLanguage:t.outputLanguage,organizationType:e.metadata.organizationType,industryContext:e.metadata.industryContext},externalData:c?[{source:"external_data_fusion",content:JSON.stringify(c),type:"fusion_result",weight:1}]:void 0,dataFusion:t.dataFusion},h=await this.promptBuilder.buildPrompt(l);i="professional"===t.version?(await this.llmClient.dualModelChat({messages:[{role:"system",content:h.systemPrompt},{role:"user",content:h.userPrompt}],temperature:.3,maxTokens:8e3},"sequential")).secondary:await this.llmClient.chat("minimax",{messages:[{role:"system",content:h.systemPrompt},{role:"user",content:h.userPrompt}],temperature:.3,maxTokens:4e3});let g=await this.parseAssessmentResponse(i.content,e,t);this.resultCache.set(r,{data:g,timestamp:Date.now()});let u=Date.now()-s;return a.info("评估报告生成完成",{organizationId:e.organizationId,version:t.version,processingTime:u}),g}catch(s){throw a.error("评估报告生成失败",{error:s instanceof Error?s.message:String(s),organizationId:e.organizationId,options:t}),Error(`评估报告生成失败: ${s instanceof Error?s.message:"未知错误"}`)}}generateCacheKey(e,t){return[e.organizationId,t.version,t.analysisMode,JSON.stringify(t.customFocus||[]),e.metadata.completedAt].join("_").replace(/[^a-zA-Z0-9_]/g,"_")}preprocessAssessmentData(e){return{organizationId:e.organizationId,responses:e.responses.map(e=>({questionId:e.questionId,answer:e.answer,dimension:e.dimension,subdimension:e.subdimension})),metadata:e.metadata}}async parseAssessmentResponse(e,t,s){try{let a;let i=e.match(/```json\s*([\s\S]*?)\s*```/)||e.match(/\{[\s\S]*\}/);return a=i?JSON.parse(i[1]||i[0]):this.parseStructuredReport(e),{id:`report_${Date.now()}`,organizationId:t.organizationId,version:s.version,overallScore:a.overallScore||0,dimensionScores:a.dimensionScores||{},strengths:a.strengths||[],improvements:a.improvements||[],recommendations:a.recommendations||[],nextSteps:a.nextSteps||[],metadata:{generatedAt:new Date,analysisMode:s.analysisMode,dataSourcesUsed:s.externalData?.map(e=>e.source)||[]}}}catch(t){throw a.error("评估响应解析失败",{error:t,content:e.substring(0,500)}),Error("评估响应解析失败")}}parseStructuredReport(e){return{overallScore:75,dimensionScores:{},strengths:[],improvements:[],recommendations:[],nextSteps:[]}}cleanExpiredCache(){let e=Date.now();for(let[t,s]of this.resultCache.entries())e-s.timestamp>this.cacheExpiry&&this.resultCache.delete(t)}getCacheStats(){let e=Array.from(this.resultCache.values()),t=e.length>0?e.reduce((e,t)=>new Date(t.timestamp)<new Date(e.timestamp)?t:e).timestamp:null;return{size:this.resultCache.size,hitRate:0,oldestEntry:t}}clearCache(){this.resultCache.clear(),a.info("Assessment cache cleared")}}},342:(e,t,s)=>{s.d(t,{s:()=>u});var a=s(3966);class i{constructor(e){this.cache=new Map,this.cleanupInterval=null,this.config={ttl:3600,strategy:"ttl",...e},this.startCleanup()}async get(e){let t=this.getFullKey(e),s=this.cache.get(t);return s?this.isExpired(s)?(this.cache.delete(t),null):(s.accessedAt=new Date,s.value):null}async set(e,t,s){let a=this.getFullKey(e),i=new Date,r={key:a,value:t,ttl:s||this.config.ttl,createdAt:i,accessedAt:i};this.cache.set(a,r),this.config.maxSize&&this.cache.size>this.config.maxSize&&this.evictEntries()}async delete(e){let t=this.getFullKey(e);return this.cache.delete(t)}async has(e){let t=this.getFullKey(e),s=this.cache.get(t);return!!s&&(!this.isExpired(s)||(this.cache.delete(t),!1))}async clear(){this.cache.clear()}async size(){return this.cache.size}async keys(){let e=[];for(let[t,s]of Array.from(this.cache.entries()))this.isExpired(s)||e.push(t.replace(this.config.keyPrefix||"",""));return e}async mget(e){let t=[];for(let s of e)t.push(await this.get(s));return t}async mset(e){for(let t of e)await this.set(t.key,t.value,t.ttl)}async expire(e,t){let s=this.getFullKey(e),a=this.cache.get(s);return!!a&&(a.ttl=t,a.createdAt=new Date,!0)}async ttl(e){let t=this.getFullKey(e),s=this.cache.get(t);if(!s)return -2;let a=Date.now()-s.createdAt.getTime(),i=1e3*s.ttl-a;return i<=0?-1:Math.ceil(i/1e3)}destroy(){this.cleanupInterval&&(clearInterval(this.cleanupInterval),this.cleanupInterval=null),this.cache.clear()}getFullKey(e){return this.config.keyPrefix?`${this.config.keyPrefix}${e}`:e}isExpired(e){return Date.now()-e.createdAt.getTime()>1e3*e.ttl}cleanup(){Date.now();let e=[];for(let[t,s]of Array.from(this.cache.entries()))this.isExpired(s)&&e.push(t);for(let t of e)this.cache.delete(t)}startCleanup(){this.cleanupInterval=setInterval(()=>{this.cleanup()},6e4)}evictEntries(){if(!this.config.maxSize)return;let e=this.cache.size-this.config.maxSize;if(e<=0)return;let t=Array.from(this.cache.entries());switch(this.config.strategy){case"lru":t.sort((e,t)=>e[1].accessedAt.getTime()-t[1].accessedAt.getTime());break;case"fifo":t.sort((e,t)=>e[1].createdAt.getTime()-t[1].createdAt.getTime());break;default:t.sort((e,t)=>1e3*e[1].ttl-(Date.now()-e[1].createdAt.getTime())-(1e3*t[1].ttl-(Date.now()-t[1].createdAt.getTime())))}for(let s=0;s<e;s++)this.cache.delete(t[s][0])}}class r{static{this.instance=null}static{this.isInitializing=!1}static async getInstance(){if(r.instance)return r.instance;if(r.isInitializing){for(;r.isInitializing;)await new Promise(e=>setTimeout(e,10));return r.instance}r.isInitializing=!0;try{return r.instance=new r,await r.instance.initialize(),r.instance}finally{r.isInitializing=!1}}constructor(){this.configSchemas=new Map,this.configValues=new Map,this.watchers=new Map,this.cache=new i({ttl:parseInt(process.env.CONFIG_CACHE_TTL||"3600"),keyPrefix:"config:"}),this.logger=new a.Yd("ConfigService")}async initialize(){try{this.logger.info("初始化配置服务..."),await this.loadConfigSchemas(),await this.loadConfigValues(),await this.validateConfigs(),this.logger.info("配置服务初始化完成")}catch(e){throw this.logger.error("配置服务初始化失败",{error:e}),e}}async get(e,t){try{if(!e||"string"!=typeof e)throw Error("配置键必须是非空字符串");let s=await this.cache.get(e);if(null!==s)return s;let a=this.configValues.get(e);if(void 0!==a)return await this.cache.set(e,a),a;if(void 0!==t)return t;throw Error(`配置项 ${e} 不存在且未提供默认值`)}catch(t){throw this.logger.error(`获取配置失败: ${e}`,{error:t instanceof Error?t.message:String(t),stack:t instanceof Error?t.stack:void 0}),t}}async set(e,t){try{await this.validateConfigValue(e,t),this.configValues.set(e,t),await this.cache.set(e,t),await this.notifyWatchers(e,t),this.logger.info(`配置已更新: ${e}`,{value:t})}catch(s){throw this.logger.error(`设置配置失败: ${e}`,{error:s,value:t}),s}}async getMultiple(e){let t={},s=[],a=e.map(async e=>{let a=await this.cache.get(e);null!==a?t[e]=a:s.push(e)});for(let e of(await Promise.all(a),s))try{let s=this.configValues.get(e);void 0!==s?(t[e]=s,this.cache.set(e,s).catch(t=>this.logger.warn(`缓存配置失败: ${e}`,{error:t}))):t[e]=null}catch(s){this.logger.warn(`获取配置失败: ${e}`,{error:s}),t[e]=null}return t}getSchema(e){return this.configSchemas.get(e)}async registerSchema(e){try{this.validateSchema(e),this.configSchemas.set(e.id,e),this.logger.info(`配置模式已注册: ${e.id}`,{schema:e.name})}catch(t){throw this.logger.error(`注册配置模式失败: ${e.id}`,{error:t}),t}}watch(e,t){this.watchers.has(e)||this.watchers.set(e,[]);let s=this.watchers.get(e);return s.push(t),()=>{let e=s.indexOf(t);e>-1&&s.splice(e,1)}}async reload(){try{this.logger.info("重新加载配置..."),await this.cache.clear(),await this.loadConfigValues(),await this.validateConfigs(),this.logger.info("配置重新加载完成")}catch(e){throw this.logger.error("重新加载配置失败",{error:e}),e}}getKeys(){return Array.from(this.configValues.keys())}has(e){return this.configValues.has(e)}async delete(e){try{this.configValues.delete(e),await this.cache.delete(e),this.logger.info(`配置已删除: ${e}`)}catch(t){throw this.logger.error(`删除配置失败: ${e}`,{error:t}),t}}async loadConfigSchemas(){for(let e of[{id:"octi.assessment",name:"OCTI评估配置",version:"1.0.0",description:"OCTI四维八极评估相关配置",schema:{type:"object",properties:{dimensions:{type:"array",items:{type:"object",properties:{id:{type:"string"},name:{type:"string"},weight:{type:"number",minimum:0,maximum:1}}}}}},isActive:!0,createdAt:new Date,updatedAt:new Date},{id:"octi.agents",name:"AI智能体配置",version:"1.0.0",description:"AI智能体相关配置",schema:{type:"object",properties:{questionnaire_designer:{type:"object",properties:{model:{type:"string"},temperature:{type:"number",minimum:0,maximum:2},maxTokens:{type:"number",minimum:1}}}}},isActive:!0,createdAt:new Date,updatedAt:new Date}])this.configSchemas.set(e.id,e)}async loadConfigValues(){for(let[e,t]of Object.entries({"octi.assessment.dimensions":[{id:"org",name:"组织维度",weight:.25},{id:"culture",name:"文化维度",weight:.25},{id:"talent",name:"人才维度",weight:.25},{id:"innovation",name:"创新维度",weight:.25}],"octi.agents.questionnaire_designer":{model:process.env.MINIMAX_MODEL||"abab6.5-chat",temperature:.7,maxTokens:2e3},"octi.agents.assessment_mentor":{model:process.env.DEEPSEEK_MODEL||"deepseek-chat",temperature:.5,maxTokens:3e3},"octi.cache.ttl":parseInt(process.env.CONFIG_CACHE_TTL||"3600"),"octi.database.url":process.env.DATABASE_URL,"octi.redis.url":process.env.REDIS_URL}))this.configValues.set(e,t)}async validateConfigs(){for(let[e,t]of Array.from(this.configValues.entries()))await this.validateConfigValue(e,t)}async validateConfigValue(e,t){let s=this.getSchemaIdFromKey(e);if(!s||!this.configSchemas.get(s))return}validateSchema(e){if(!e.id||!e.name||!e.version)throw Error("配置模式缺少必要字段");if(!e.schema||"object"!=typeof e.schema)throw Error("配置模式格式无效")}getSchemaIdFromKey(e){return e.startsWith("octi.assessment")?"octi.assessment":e.startsWith("octi.agents")?"octi.agents":null}async notifyWatchers(e,t){let s=this.watchers.get(e);if(s&&0!==s.length)for(let a of s)try{a(t)}catch(t){this.logger.error(`配置观察者回调失败: ${e}`,{error:t})}}}var n=s(3686),o=s(4100),c=s(2259),l=s(335),h=s(3320);class g{constructor(){this.agents=new Map,this.logger=new a.Yd("AgentManager")}async initialize(){try{this.logger.info("开始初始化智能体管理器");let e=new c.e,t=new l.Z,s=new h.e;for(let[a,i]of(this.registerAgent(new n.o(e,t,s)),this.registerAgent(new o.S(e,t,s)),this.agents.entries()))await i.initialize(),this.logger.info(`智能体 ${a} 初始化完成`);this.logger.info("智能体管理器初始化完成")}catch(e){throw this.logger.error("智能体管理器初始化失败",{error:e}),e}}registerAgent(e){this.agents.set(e.name,e),this.logger.info(`注册智能体: ${e.name}`)}getAgent(e){return this.agents.get(e)}async executeAgent(e,t){let s=this.getAgent(e);if(!s)throw Error(`智能体不存在: ${e}`);if("question_designer"===e)return await s.designQuestionnaire(t);if("organization_tutor"===e)return await s.generateAssessment(t.assessmentData,t.options);throw Error(`不支持的智能体操作: ${e}`)}getAllStatus(){let e={};for(let[t,s]of Array.from(this.agents.entries()))e[t]=s.getStatus();return e}getAvailableAgents(){return Array.from(this.agents.keys())}}class u{constructor(){this.isInitialized=!1,this.logger=new a.Yd("ApiService"),this.configService=r.getInstance(),this.agentManager=g.getInstance()}static getInstance(){return u.instance||(u.instance=new u),u.instance}async initialize(){if(this.isInitialized){this.logger.warn("API服务已经初始化");return}try{this.logger.info("开始初始化API服务"),await this.agentManager.initialize(),this.isInitialized=!0,this.logger.info("API服务初始化完成")}catch(e){throw this.logger.error("API服务初始化失败",{error:e}),e}}async handleConfigRequest(e){try{let{action:t,key:s,value:a,batch:i}=e;switch(t){case"get":if(!s)return{success:!0,data:{}};{let e=await this.configService.get(s);return{success:!0,data:e}}case"set":if(!s||void 0===a)return{success:!1,error:"缺少必要参数: key 或 value"};return await this.configService.set(s,a),{success:!0,message:"配置设置成功"};case"batch_get":if(!i||!Array.isArray(i))return{success:!1,error:"缺少必要参数: batch"};let r={};for(let e of i)r[e]=await this.configService.get(e);return{success:!0,data:r};case"batch_set":if(!i||"object"!=typeof i)return{success:!1,error:"缺少必要参数: batch"};for(let[e,t]of Object.entries(i))await this.configService.set(e,t);return{success:!0,message:"批量配置设置成功"};case"reload":return await this.configService.reload(),{success:!0,message:"配置重新加载成功"};default:return{success:!1,error:`不支持的操作: ${t}`}}}catch(t){return this.logger.error("处理配置API请求失败",{error:t,request:e}),{success:!1,error:`配置操作失败: ${t}`}}}async handleAgentRequest(e){try{let{action:t,agentName:s,input:a}=e;switch(t){case"execute":if(!s||!a)return{success:!1,error:"缺少必要参数: agentName 或 input"};let i=await this.agentManager.executeAgent(s,a);return{success:!0,data:i};case"status":if(s){let e=this.agentManager.getAgent(s);if(!e)return{success:!1,error:`智能体不存在: ${s}`};let t=e.getStatus();return{success:!0,data:t}}{let e=this.agentManager.getAllStatus();return{success:!0,data:e}}case"list":let r=this.agentManager.getAvailableAgents();return{success:!0,data:r};default:return{success:!1,error:`不支持的操作: ${t}`}}}catch(t){return this.logger.error("处理智能体API请求失败",{error:t,request:e}),{success:!1,error:`智能体操作失败: ${t}`}}}async handleAssessmentRequest(e){try{let{action:t,assessmentId:s,data:a}=e;switch(t){case"create":if(!a)return{success:!1,error:"缺少必要参数: data"};let i=await this.createAssessment(a);return{success:!0,data:i};case"get":if(!s)return{success:!1,error:"缺少必要参数: assessmentId"};let r=await this.getAssessment(s);return{success:!0,data:r};case"submit":if(!s||!a)return{success:!1,error:"缺少必要参数: assessmentId 或 data"};let n=await this.submitAssessment(s,a);return{success:!0,data:n};case"analyze":if(!s)return{success:!1,error:"缺少必要参数: assessmentId"};let o=await this.analyzeAssessment(s);return{success:!0,data:o};default:return{success:!1,error:`不支持的操作: ${t}`}}}catch(t){return this.logger.error("处理评估API请求失败",{error:t,request:e}),{success:!1,error:`评估操作失败: ${t}`}}}async handleSystemRequest(e){try{let{action:t}=e;switch(t){case"health":let s=await this.getSystemHealth();return{success:!0,data:s};case"status":let a=await this.getSystemStatus();return{success:!0,data:a};case"metrics":let i=await this.getSystemMetrics();return{success:!0,data:i};default:return{success:!1,error:`不支持的操作: ${t}`}}}catch(t){return this.logger.error("处理系统API请求失败",{error:t,request:e}),{success:!1,error:`系统操作失败: ${t}`}}}async createAssessment(e){let t=await this.agentManager.executeAgent("questionnaire_designer",{assessmentType:e.type||"综合能力评估",dimensions:e.dimensions||["团队协作","沟通能力","领导力","创新思维"],requirements:e.requirements});if(!t.success)throw Error("问卷生成失败");let s={id:`assessment_${Date.now()}`,title:e.title||"智能评估",description:e.description||"AI生成的智能评估问卷",questionnaire:t.data,status:"active",createdAt:new Date().toISOString(),metadata:{type:e.type,dimensions:e.dimensions,estimatedTime:t.data?.metadata?.estimatedTime||15}};return this.logger.info("创建评估成功",{assessmentId:s.id}),s}async getAssessment(e){return this.logger.info("获取评估",{assessmentId:e}),{id:e,title:"智能评估",description:"AI生成的智能评估问卷",status:"active",questionnaire:{title:"OCTI智能评估问卷",sections:[{id:"section_1",title:"基础信息",questions:[{id:"q1",type:"single_choice",title:"您的年龄段是？",options:["18-25岁","26-35岁","36-45岁","46岁以上"],required:!0}]}]},createdAt:new Date().toISOString()}}async submitAssessment(e,t){return this.logger.info("提交评估",{assessmentId:e,data:t}),{id:`submission_${Date.now()}`,assessmentId:e,answers:t.answers,submittedAt:new Date().toISOString(),status:"completed"}}async analyzeAssessment(e){this.logger.info("分析评估",{assessmentId:e});let t=await this.agentManager.executeAgent("assessment_mentor",{assessmentResults:{assessmentId:e,answers:{},submittedAt:new Date().toISOString()},userProfile:{age:"26-35岁",experience:"3-5年"}});if(!t.success)throw Error("评估分析失败");return{assessmentId:e,analysis:t.data,analyzedAt:new Date().toISOString()}}async getSystemHealth(){return{status:"healthy",timestamp:new Date().toISOString(),services:{config:this.configService?"healthy":"unhealthy",agents:this.agentManager?"healthy":"unhealthy",api:this.isInitialized?"healthy":"unhealthy"}}}async getSystemStatus(){return{initialized:this.isInitialized,uptime:process.uptime(),memory:process.memoryUsage(),version:"1.0.0",environment:"production",agents:this.agentManager.getAllStatus()}}async getSystemMetrics(){return{requests:{total:Math.floor(1e3*Math.random()),success:Math.floor(900*Math.random()),error:Math.floor(100*Math.random())},performance:{avgResponseTime:Math.floor(500*Math.random())+100,p95ResponseTime:Math.floor(1e3*Math.random())+200,p99ResponseTime:Math.floor(2e3*Math.random())+500},resources:{cpuUsage:100*Math.random(),memoryUsage:100*Math.random(),diskUsage:100*Math.random()}}}}}};