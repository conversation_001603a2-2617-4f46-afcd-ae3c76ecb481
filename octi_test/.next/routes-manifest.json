{"version": 3, "pages404": true, "caseSensitive": false, "basePath": "", "redirects": [{"source": "/:path+/", "destination": "/:path+", "internal": true, "statusCode": 308, "regex": "^(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))/$"}], "headers": [], "dynamicRoutes": [{"page": "/api/assessment/[assessmentId]/dataSource/scrape", "regex": "^/api/assessment/([^/]+?)/dataSource/scrape(?:/)?$", "routeKeys": {"nxtPassessmentId": "nxtPassessmentId"}, "namedRegex": "^/api/assessment/(?<nxtPassessmentId>[^/]+?)/dataSource/scrape(?:/)?$"}, {"page": "/api/assessment/[assessmentId]/dataSource/upload", "regex": "^/api/assessment/([^/]+?)/dataSource/upload(?:/)?$", "routeKeys": {"nxtPassessmentId": "nxtPassessmentId"}, "namedRegex": "^/api/assessment/(?<nxtPassessmentId>[^/]+?)/dataSource/upload(?:/)?$"}, {"page": "/api/assessments/[id]/analyze", "regex": "^/api/assessments/([^/]+?)/analyze(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/assessments/(?<nxtPid>[^/]+?)/analyze(?:/)?$"}, {"page": "/api/reports/[id]", "regex": "^/api/reports/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/reports/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/api/reports/[id]/export", "regex": "^/api/reports/([^/]+?)/export(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/reports/(?<nxtPid>[^/]+?)/export(?:/)?$"}], "staticRoutes": [{"page": "/", "regex": "^/(?:/)?$", "routeKeys": {}, "namedRegex": "^/(?:/)?$"}, {"page": "/_not-found", "regex": "^/_not\\-found(?:/)?$", "routeKeys": {}, "namedRegex": "^/_not\\-found(?:/)?$"}, {"page": "/questionnaire", "regex": "^/questionnaire(?:/)?$", "routeKeys": {}, "namedRegex": "^/questionnaire(?:/)?$"}, {"page": "/report", "regex": "^/report(?:/)?$", "routeKeys": {}, "namedRegex": "^/report(?:/)?$"}, {"page": "/test", "regex": "^/test(?:/)?$", "routeKeys": {}, "namedRegex": "^/test(?:/)?$"}, {"page": "/test/api", "regex": "^/test/api(?:/)?$", "routeKeys": {}, "namedRegex": "^/test/api(?:/)?$"}], "dataRoutes": [], "rsc": {"header": "RSC", "varyHeader": "RSC, Next-Router-State-Tree, Next-Router-Prefetch, Next-Url", "prefetchHeader": "Next-Router-Prefetch", "didPostponeHeader": "x-nextjs-postponed", "contentTypeHeader": "text/x-component", "suffix": ".rsc", "prefetchSuffix": ".prefetch.rsc"}, "rewrites": [{"source": "/api/v1/:path*", "destination": "/api/:path*", "regex": "^/api/v1(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))?(?:/)?$"}]}