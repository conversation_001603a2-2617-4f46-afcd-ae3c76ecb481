(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[931],{5573:function(e,s,t){Promise.resolve().then(t.bind(t,9276))},9276:function(e,s,t){"use strict";t.r(s),t.d(s,{default:function(){return C}});var l=t(7437),r=t(2265),n=t(7418),i=t(6651),a=t(8517),c=t(2169);let d=(0,r.createContext)(void 0),o=()=>{let e=(0,r.useContext)(d);if(!e)throw Error("useDialog must be used within a Dialog");return e},u=e=>{let{children:s,open:t,onOpenChange:n,defaultOpen:i=!1}=e,[a,c]=(0,r.useState)(i);return(0,l.jsx)(d.Provider,{value:{open:void 0!==t?t:a,setOpen:e=>{void 0===t&&c(e),null==n||n(e)}},children:s})},x=e=>{let{children:s,asChild:t=!1}=e,{setOpen:n}=o();return t&&r.isValidElement(s)?r.cloneElement(s,{onClick:e=>{let t=s.props.onClick;null==t||t(e),n(!0)}}):(0,l.jsx)(i.z,{onClick:()=>n(!0),children:s})},m={sm:"max-w-sm",md:"max-w-md",lg:"max-w-lg",xl:"max-w-xl",full:"max-w-full mx-4"},h=e=>{let{children:s,className:t,size:n="md",showClose:i=!0,onEscapeKeyDown:a,onPointerDownOutside:d}=e,{open:u,setOpen:x}=o();return((0,r.useEffect)(()=>{let e=e=>{"Escape"!==e.key||(null==a||a(e),e.defaultPrevented||x(!1))};return u&&(document.addEventListener("keydown",e),document.body.style.overflow="hidden"),()=>{document.removeEventListener("keydown",e),document.body.style.overflow="unset"}},[u,a,x]),u)?(0,l.jsxs)("div",{className:"fixed inset-0 z-50",children:[(0,l.jsx)("div",{className:"fixed inset-0 bg-black/50 backdrop-blur-sm",onClick:e=>{if(e.target===e.currentTarget){let e=new PointerEvent("pointerdown",{bubbles:!0,cancelable:!0});null==d||d(e),e.defaultPrevented||x(!1)}}}),(0,l.jsx)("div",{className:"fixed inset-0 flex items-center justify-center p-4",children:(0,l.jsxs)("div",{className:(0,c.cn)("relative w-full bg-background rounded-lg shadow-lg border",m[n],t),onClick:e=>e.stopPropagation(),children:[i&&(0,l.jsxs)("button",{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none",onClick:()=>x(!1),children:[(0,l.jsx)("svg",{className:"h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,l.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})}),(0,l.jsx)("span",{className:"sr-only",children:"关闭"})]}),s]})})]}):null},f=e=>{let{children:s,className:t}=e;return(0,l.jsx)("div",{className:(0,c.cn)("flex flex-col space-y-1.5 text-center sm:text-left p-6 pb-0",t),children:s})},j=e=>{let{children:s,className:t}=e;return(0,l.jsx)("h2",{className:(0,c.cn)("text-lg font-semibold leading-none tracking-tight",t),children:s})};var p=t(2278);let g=(0,r.createContext)(void 0),v=()=>{let e=(0,r.useContext)(g);if(!e)throw Error("useForm must be used within a Form");return e},b=function(e){let s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];for(let t of s){if(t.required&&(!e||"string"==typeof e&&""===e.trim()))return t.message||"此字段为必填项";if(!e&&!t.required)continue;let s=String(e);if(t.minLength&&s.length<t.minLength)return t.message||"最少需要".concat(t.minLength,"个字符");if(t.maxLength&&s.length>t.maxLength)return t.message||"最多允许".concat(t.maxLength,"个字符");if(t.pattern&&!t.pattern.test(s))return t.message||"格式不正确";if(t.custom){let s=t.custom(e);if(s)return s}}return null},y=e=>{let{children:s,initialData:t={},onSubmit:n,className:i,fields:a=[]}=e,[d,o]=(0,r.useState)(t),[u,x]=(0,r.useState)({}),[m,h]=(0,r.useState)(!1),f=(e,s)=>{x(t=>({...t,[e]:s}))},j=e=>{x(s=>{let t={...s};return delete t[e],t})},p=e=>{let s=!0,t={};for(let l of e){let e=b(d[l.name],l.rules);e&&(t[l.name]=e,s=!1)}return x(t),s},v=async e=>{if(e.preventDefault(),p(a)){h(!0);try{await n(d)}catch(e){console.error("表单提交失败:",e)}finally{h(!1)}}};return(0,l.jsx)(g.Provider,{value:{data:d,errors:u,isSubmitting:m,setValue:(e,s)=>{o(t=>({...t,[e]:s})),u[e]&&x(s=>{let t={...s};return delete t[e],t})},setError:f,clearError:j,validateField:(e,s)=>{var t;let l=s||(null===(t=a.find(s=>s.name===e))||void 0===t?void 0:t.rules)||[],r=b(d[e],l);return r?(f(e,r),!1):(j(e),!0)},validateForm:p},children:(0,l.jsx)("form",{onSubmit:v,className:(0,c.cn)("space-y-4",i),children:s})})},N=r.memo(e=>{let{field:s,className:t}=e,{data:n,errors:i,setValue:a,validateField:d}=v(),o=n[s.name]||"",u=i[s.name],x=(0,r.useCallback)(e=>{let t="checkbox"===e.target.type?e.target.checked:e.target.value;a(s.name,t)},[s.name,a]),m=(0,r.useCallback)(()=>{d(s.name,s.rules)},[s.name,s.rules,d]);return"select"===s.type&&s.options?(0,l.jsxs)("div",{className:t,children:[s.label&&(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:s.label}),(0,l.jsxs)("select",{value:String(o),onChange:x,onBlur:m,disabled:s.disabled,className:(0,c.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",u&&"border-red-500 focus-visible:ring-red-500"),children:[(0,l.jsx)("option",{value:"",children:s.placeholder||"请选择"}),s.options.map(e=>(0,l.jsx)("option",{value:e.value,children:e.label},e.value))]}),u&&(0,l.jsx)("p",{className:"mt-1 text-sm text-red-600",children:u}),s.helperText&&!u&&(0,l.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:s.helperText})]}):"checkbox"===s.type?(0,l.jsxs)("div",{className:(0,c.cn)("flex items-center space-x-2",t),children:[(0,l.jsx)("input",{type:"checkbox",id:s.name,checked:!!o,onChange:x,onBlur:m,disabled:s.disabled,className:"h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"}),s.label&&(0,l.jsx)("label",{htmlFor:s.name,className:"text-sm font-medium text-gray-700",children:s.label}),u&&(0,l.jsx)("p",{className:"text-sm text-red-600",children:u})]}):(0,l.jsx)("div",{className:t,children:(0,l.jsx)(p.I,{type:s.type||"text",value:String(o),onChange:x,onBlur:m,placeholder:s.placeholder,disabled:s.disabled,label:s.label,error:u,helperText:s.helperText})})});N.displayName="FormField";let w=e=>{let{children:s,className:t,variant:r="default",disabled:n=!1}=e,{isSubmitting:a}=v();return(0,l.jsx)(i.z,{type:"submit",variant:r,disabled:n||a,loading:a,className:t,children:s})},k=e=>{let{fields:s,onSubmit:t,submitText:r="提交",className:n,initialData:i}=e;return(0,l.jsxs)(y,{fields:s,onSubmit:t,initialData:i,className:n,children:[s.map(e=>(0,l.jsx)(N,{field:e},e.name)),(0,l.jsx)(w,{children:r})]})};function C(){var e,s,t,c,d,o,m,p,g,v,b;let[y,N]=(0,r.useState)(null),[w,C]=(0,r.useState)([]),[z,S]=(0,r.useState)([]),[E,L]=(0,r.useState)(!0),[O,P]=(0,r.useState)(null);(0,r.useEffect)(()=>{T()},[]);let T=async()=>{try{L(!0),P(null);let[e,s,t]=await Promise.all([fetch("/api/system?action=status"),fetch("/api/agents"),fetch("/api/assessments")]);if(!e.ok||!s.ok||!t.ok)throw Error("加载系统数据失败");let[l,r,n]=await Promise.all([e.json(),s.json(),t.json()]);N(l.data),C(r.data||[]),S(n.data||[])}catch(e){P(e instanceof Error?e.message:"未知错误")}finally{L(!1)}},Z=[{name:"title",label:"评估标题",type:"text",placeholder:"请输入评估标题",rules:[{required:!0,message:"评估标题不能为空"},{minLength:2,message:"标题至少需要2个字符"},{maxLength:100,message:"标题不能超过100个字符"}]},{name:"description",label:"评估描述",type:"text",placeholder:"请输入评估描述",rules:[{maxLength:500,message:"描述不能超过500个字符"}]},{name:"type",label:"评估类型",type:"select",options:[{label:"能力评估",value:"capability"},{label:"知识测试",value:"knowledge"},{label:"技能验证",value:"skill"},{label:"综合评估",value:"comprehensive"}],rules:[{required:!0,message:"请选择评估类型"}]}],F=async e=>{try{if(!(await fetch("/api/assessments",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)})).ok)throw Error("创建评估失败");await T()}catch(e){throw console.error("创建评估失败:",e),e}},M=e=>{switch(e){case"healthy":case"active":return"text-green-600";case"warning":case"idle":return"text-yellow-600";case"error":return"text-red-600";default:return"text-gray-600"}},R=e=>{switch(e){case"healthy":return"健康";case"warning":return"警告";case"error":return"错误";case"active":return"活跃";case"idle":return"空闲";case"draft":return"草稿";case"completed":return"已完成";default:return e}};return E?(0,l.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,l.jsx)(a.gb,{size:"lg",text:"加载系统数据中..."})}):O?(0,l.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,l.jsxs)(n.Zb,{className:"max-w-md",children:[(0,l.jsxs)(n.Ol,{children:[(0,l.jsx)(n.ll,{className:"text-red-600",children:"加载失败"}),(0,l.jsx)(n.SZ,{children:O})]}),(0,l.jsx)(n.aY,{children:(0,l.jsx)(i.z,{onClick:T,className:"w-full",children:"重新加载"})})]})}):(0,l.jsx)("div",{className:"min-h-screen bg-gray-50 p-6",children:(0,l.jsxs)("div",{className:"max-w-7xl mx-auto",children:[(0,l.jsxs)("div",{className:"mb-8",children:[(0,l.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"OCTI 智能评估系统"}),(0,l.jsx)("p",{className:"text-gray-600",children:"基于AI的在线能力测试与智能评估平台"}),(0,l.jsxs)("div",{className:"mt-4 flex space-x-4",children:[(0,l.jsx)(i.z,{onClick:()=>window.location.href="/questionnaire",size:"lg",className:"bg-blue-600 hover:bg-blue-700",children:"开始OCTI评估"}),(0,l.jsx)(i.z,{onClick:()=>window.location.href="/report",size:"lg",variant:"outline",children:"查看报告"})]})]}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8",children:[(0,l.jsxs)(n.Zb,{children:[(0,l.jsx)(n.Ol,{children:(0,l.jsx)(n.ll,{className:"text-lg",children:"系统状态"})}),(0,l.jsx)(n.aY,{children:y&&(0,l.jsxs)("div",{className:"space-y-3",children:[(0,l.jsxs)("div",{className:"flex justify-between items-center",children:[(0,l.jsx)("span",{children:"整体健康度"}),(0,l.jsx)("span",{className:M(y.health),children:R(y.health)})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,l.jsx)("span",{children:"配置服务"}),(0,l.jsx)("span",{className:(null===(e=y.services)||void 0===e?void 0:e.config)?"text-green-600":"text-red-600",children:(null===(s=y.services)||void 0===s?void 0:s.config)?"正常":"异常"})]}),(0,l.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,l.jsx)("span",{children:"智能体服务"}),(0,l.jsx)("span",{className:(null===(t=y.services)||void 0===t?void 0:t.agents)?"text-green-600":"text-red-600",children:(null===(c=y.services)||void 0===c?void 0:c.agents)?"正常":"异常"})]}),(0,l.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,l.jsx)("span",{children:"API服务"}),(0,l.jsx)("span",{className:(null===(d=y.services)||void 0===d?void 0:d.api)?"text-green-600":"text-red-600",children:(null===(o=y.services)||void 0===o?void 0:o.api)?"正常":"异常"})]})]})]})})]}),(0,l.jsxs)(n.Zb,{children:[(0,l.jsx)(n.Ol,{children:(0,l.jsx)(n.ll,{className:"text-lg",children:"系统指标"})}),(0,l.jsx)(n.aY,{children:y&&(0,l.jsxs)("div",{className:"space-y-3",children:[(0,l.jsxs)("div",{className:"flex justify-between items-center",children:[(0,l.jsx)("span",{children:"运行时间"}),(0,l.jsx)("span",{className:"text-sm text-gray-600",children:null===(m=y.metrics)||void 0===m?void 0:m.uptime})]}),(0,l.jsxs)("div",{children:[(0,l.jsxs)("div",{className:"flex justify-between text-sm mb-1",children:[(0,l.jsx)("span",{children:"内存使用"}),(0,l.jsxs)("span",{children:[null===(p=y.metrics)||void 0===p?void 0:p.memory,"%"]})]}),(0,l.jsx)(a.Ex,{value:(null===(g=y.metrics)||void 0===g?void 0:g.memory)||0,size:"sm"})]}),(0,l.jsxs)("div",{children:[(0,l.jsxs)("div",{className:"flex justify-between text-sm mb-1",children:[(0,l.jsx)("span",{children:"CPU使用"}),(0,l.jsxs)("span",{children:[null===(v=y.metrics)||void 0===v?void 0:v.cpu,"%"]})]}),(0,l.jsx)(a.Ex,{value:(null===(b=y.metrics)||void 0===b?void 0:b.cpu)||0,size:"sm"})]})]})})]}),(0,l.jsxs)(n.Zb,{children:[(0,l.jsx)(n.Ol,{children:(0,l.jsx)(n.ll,{className:"text-lg",children:"智能体状态"})}),(0,l.jsx)(n.aY,{children:(0,l.jsxs)("div",{className:"space-y-2",children:[w.map(e=>(0,l.jsxs)("div",{className:"flex justify-between items-center text-sm",children:[(0,l.jsx)("span",{children:e.name}),(0,l.jsx)("span",{className:M(e.status),children:R(e.status)})]},e.id)),0===w.length&&(0,l.jsx)("p",{className:"text-sm text-gray-500",children:"暂无智能体"})]})})]})]}),(0,l.jsxs)(n.Zb,{children:[(0,l.jsx)(n.Ol,{children:(0,l.jsxs)("div",{className:"flex justify-between items-center",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)(n.ll,{className:"text-xl",children:"评估管理"}),(0,l.jsx)(n.SZ,{children:"管理和创建在线评估"})]}),(0,l.jsxs)(u,{children:[(0,l.jsx)(x,{asChild:!0,children:(0,l.jsx)(i.z,{children:"创建评估"})}),(0,l.jsxs)(h,{children:[(0,l.jsx)(f,{children:(0,l.jsx)(j,{children:"创建新评估"})}),(0,l.jsx)(k,{fields:Z,onSubmit:F,submitText:"创建评估"})]})]})]})}),(0,l.jsx)(n.aY,{children:z.length>0?(0,l.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:z.map(e=>(0,l.jsxs)(n.Zb,{variant:"outlined",children:[(0,l.jsxs)(n.Ol,{children:[(0,l.jsx)(n.ll,{className:"text-lg",children:e.title}),(0,l.jsx)(n.SZ,{children:e.description})]}),(0,l.jsxs)(n.aY,{children:[(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,l.jsx)("span",{children:"状态"}),(0,l.jsx)("span",{className:M(e.status),children:R(e.status)})]}),(0,l.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,l.jsx)("span",{children:"完成数"}),(0,l.jsx)("span",{children:e.completedCount})]}),(0,l.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,l.jsx)("span",{children:"题目数"}),(0,l.jsx)("span",{children:e.totalQuestions})]}),(0,l.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,l.jsx)("span",{children:"创建时间"}),(0,l.jsx)("span",{children:new Date(e.createdAt).toLocaleDateString()})]})]}),(0,l.jsxs)("div",{className:"mt-4 flex space-x-2",children:[(0,l.jsx)(i.z,{size:"sm",variant:"outline",className:"flex-1",children:"编辑"}),(0,l.jsx)(i.z,{size:"sm",className:"flex-1",children:"查看"})]})]})]},e.id))}):(0,l.jsxs)("div",{className:"text-center py-8",children:[(0,l.jsx)("p",{className:"text-gray-500 mb-4",children:"暂无评估项目"}),(0,l.jsxs)(u,{children:[(0,l.jsx)(x,{asChild:!0,children:(0,l.jsx)(i.z,{variant:"outline",children:"创建第一个评估"})}),(0,l.jsxs)(h,{children:[(0,l.jsx)(f,{children:(0,l.jsx)(j,{children:"创建新评估"})}),(0,l.jsx)(k,{fields:Z,onSubmit:F,submitText:"创建评估"})]})]})]})})]})]})})}},6651:function(e,s,t){"use strict";t.d(s,{z:function(){return c}});var l=t(7437),r=t(2265),n=t(2169);let i={default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},a={default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"},c=r.forwardRef((e,s)=>{let{className:t,variant:r="default",size:c="default",loading:d=!1,disabled:o,children:u,...x}=e;return(0,l.jsxs)("button",{className:(0,n.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",i[r],a[c],t),ref:s,disabled:o||d,...x,children:[d&&(0,l.jsxs)("svg",{className:"mr-2 h-4 w-4 animate-spin",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,l.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,l.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),u]})});c.displayName="Button"},7418:function(e,s,t){"use strict";t.d(s,{Ol:function(){return c},SZ:function(){return o},Zb:function(){return a},aY:function(){return u},ll:function(){return d}});var l=t(7437),r=t(2265),n=t(2169);let i={default:"bg-card text-card-foreground",outlined:"bg-card text-card-foreground border border-border",elevated:"bg-card text-card-foreground shadow-lg"},a=r.forwardRef((e,s)=>{let{className:t,variant:r="default",...a}=e;return(0,l.jsx)("div",{ref:s,className:(0,n.cn)("rounded-lg border shadow-sm",i[r],t),...a})});a.displayName="Card";let c=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,l.jsx)("div",{ref:s,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",t),...r})});c.displayName="CardHeader";let d=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,l.jsx)("h3",{ref:s,className:(0,n.cn)("text-2xl font-semibold leading-none tracking-tight",t),...r})});d.displayName="CardTitle";let o=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,l.jsx)("p",{ref:s,className:(0,n.cn)("text-sm text-muted-foreground",t),...r})});o.displayName="CardDescription";let u=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,l.jsx)("div",{ref:s,className:(0,n.cn)("p-6 pt-0",t),...r})});u.displayName="CardContent",r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,l.jsx)("div",{ref:s,className:(0,n.cn)("flex items-center p-6 pt-0",t),...r})}).displayName="CardFooter"},2278:function(e,s,t){"use strict";t.d(s,{I:function(){return i}});var l=t(7437),r=t(2265),n=t(2169);let i=r.forwardRef((e,s)=>{let{className:t,type:r="text",error:i,label:a,helperText:c,id:d,...o}=e,u=d||"input-".concat(Math.random().toString(36).substring(2,9));return(0,l.jsxs)("div",{className:"w-full",children:[a&&(0,l.jsx)("label",{htmlFor:u,className:"block text-sm font-medium text-gray-700 mb-1",children:a}),(0,l.jsx)("input",{type:r,id:u,className:(0,n.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",i&&"border-red-500 focus-visible:ring-red-500",t),ref:s,...o}),i&&(0,l.jsx)("p",{className:"mt-1 text-sm text-red-600",children:i}),c&&!i&&(0,l.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:c})]})});i.displayName="Input"},8517:function(e,s,t){"use strict";t.d(s,{Ex:function(){return c},gb:function(){return a}});var l=t(7437);t(2265);var r=t(2169);let n={sm:"h-4 w-4",md:"h-6 w-6",lg:"h-8 w-8"},i=e=>{let{size:s,className:t}=e;return(0,l.jsxs)("svg",{className:(0,r.cn)("animate-spin",n[s],t),xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,l.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,l.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]})},a=e=>{let{size:s="md",className:t,text:n,overlay:a=!1}=e,c=(0,l.jsxs)("div",{className:(0,r.cn)("flex items-center justify-center",n&&"flex-col space-y-2",!n&&"space-x-2",t),children:[(0,l.jsx)(i,{size:s,className:"text-primary"}),n&&(0,l.jsx)("span",{className:"text-sm text-muted-foreground",children:n})]});return a?(0,l.jsx)("div",{className:"fixed inset-0 z-50 flex items-center justify-center bg-background/80 backdrop-blur-sm",children:c}):c},c=e=>{let{value:s,max:t=100,className:n,showLabel:i=!1,size:a="md"}=e,c=Math.min(Math.max(s/t*100,0),100);return(0,l.jsxs)("div",{className:(0,r.cn)("w-full",n),children:[i&&(0,l.jsxs)("div",{className:"flex justify-between text-sm text-muted-foreground mb-1",children:[(0,l.jsx)("span",{children:"进度"}),(0,l.jsxs)("span",{children:[Math.round(c),"%"]})]}),(0,l.jsx)("div",{className:(0,r.cn)("w-full bg-gray-200 rounded-full overflow-hidden",{sm:"h-1",md:"h-2",lg:"h-3"}[a]),children:(0,l.jsx)("div",{className:"h-full bg-primary transition-all duration-300 ease-out",style:{width:"".concat(c,"%")}})})]})}},2169:function(e,s,t){"use strict";t.d(s,{cn:function(){return n}});var l=t(7683),r=t(188);function n(){for(var e=arguments.length,s=Array(e),t=0;t<e;t++)s[t]=arguments[t];return(0,r.m6)((0,l.W)(s))}}},function(e){e.O(0,[631,971,938,744],function(){return e(e.s=5573)}),_N_E=e.O()}]);