(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[928],{6627:function(e,r,s){Promise.resolve().then(s.bind(s,8283))},8283:function(e,r,s){"use strict";s.r(r),s.d(r,{default:function(){return l}});var n=s(7437),i=s(2265),t=s(6651),o=s(7418),a=s(2278);function l(){var e;let[r,s]=(0,i.useState)("questionnaire"),[l,d]=(0,i.useState)(!1),[c,u]=(0,i.useState)(null),[m,f]=(0,i.useState)({organizationType:"startup",industryContext:"technology",focusDimensions:["leadership","innovation"],questionCount:20,version:"standard"}),[x,p]=(0,i.useState)({organizationName:"测试公司",organizationType:"startup",industryContext:"technology",responses:[{questionId:"q1",score:4,dimension:"leadership",subdimension:"vision"},{questionId:"q2",score:3,dimension:"innovation",subdimension:"creativity"},{questionId:"q3",score:5,dimension:"execution",subdimension:"efficiency"}]}),g=async()=>{d(!0),u(null);try{let e=await fetch("/api/questionnaire/generate",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(m)}),r=await e.json();u(r)}catch(e){u({success:!1,error:{message:e instanceof Error?e.message:"未知错误"}})}finally{d(!1)}},b=async()=>{d(!0),u(null);try{let e=await fetch("/api/assessment/evaluate",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({...x,evaluationOptions:{version:"standard",includeRecommendations:!0,includeBenchmarking:!1,customFocus:[],dataFusion:{enabled:!0,fusionStrategy:"weighted",maxDataLength:1e4,dataSources:[]}}})}),r=await e.json();u(r)}catch(e){u({success:!1,error:{message:e instanceof Error?e.message:"未知错误"}})}finally{d(!1)}},h=async e=>{d(!0),u(null);try{let r=await fetch(e,{method:"OPTIONS"}),s=await r.json();u(s)}catch(e){u({success:!1,error:{message:e instanceof Error?e.message:"未知错误"}})}finally{d(!1)}};return(0,n.jsxs)("div",{className:"container mx-auto p-6 max-w-6xl",children:[(0,n.jsxs)("div",{className:"mb-8",children:[(0,n.jsx)("h1",{className:"text-3xl font-bold mb-2",children:"OCTI API 测试页面"}),(0,n.jsx)("p",{className:"text-muted-foreground",children:"测试问卷生成和组织评估API的功能"})]}),(0,n.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,n.jsx)("div",{className:"space-y-6",children:(0,n.jsxs)("div",{className:"w-full",children:[(0,n.jsxs)("div",{className:"grid w-full grid-cols-2 mb-4",children:[(0,n.jsx)("button",{className:"px-4 py-2 text-sm font-medium rounded-l-md border ".concat("questionnaire"===r?"bg-primary text-primary-foreground":"bg-background text-foreground border-input"),onClick:()=>s("questionnaire"),children:"问卷生成"}),(0,n.jsx)("button",{className:"px-4 py-2 text-sm font-medium rounded-r-md border ".concat("assessment"===r?"bg-primary text-primary-foreground":"bg-background text-foreground border-input"),onClick:()=>s("assessment"),children:"组织评估"})]}),"questionnaire"===r&&(0,n.jsx)("div",{className:"space-y-4",children:(0,n.jsxs)(o.Zb,{children:[(0,n.jsxs)(o.Ol,{children:[(0,n.jsx)(o.ll,{children:"问卷生成 API"}),(0,n.jsx)(o.SZ,{children:"测试 /api/questionnaire/generate 端点"})]}),(0,n.jsxs)(o.aY,{className:"space-y-4",children:[(0,n.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{htmlFor:"orgType",className:"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",children:"组织类型"}),(0,n.jsxs)("select",{value:m.organizationType,onChange:e=>f(r=>({...r,organizationType:e.target.value})),className:"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",children:[(0,n.jsx)("option",{value:"startup",children:"初创公司"}),(0,n.jsx)("option",{value:"sme",children:"中小企业"}),(0,n.jsx)("option",{value:"enterprise",children:"大型企业"}),(0,n.jsx)("option",{value:"nonprofit",children:"非营利组织"})]})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{htmlFor:"industry",className:"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",children:"行业背景"}),(0,n.jsxs)("select",{value:m.industryContext,onChange:e=>f(r=>({...r,industryContext:e.target.value})),className:"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",children:[(0,n.jsx)("option",{value:"technology",children:"科技"}),(0,n.jsx)("option",{value:"finance",children:"金融"}),(0,n.jsx)("option",{value:"healthcare",children:"医疗"}),(0,n.jsx)("option",{value:"education",children:"教育"}),(0,n.jsx)("option",{value:"manufacturing",children:"制造业"})]})]})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{htmlFor:"questionCount",className:"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",children:"问题数量"}),(0,n.jsx)(a.I,{id:"questionCount",type:"number",min:"10",max:"50",value:m.questionCount,onChange:e=>f(r=>({...r,questionCount:parseInt(e.target.value)||20}))})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",children:"关注维度"}),(0,n.jsx)("div",{className:"flex flex-wrap gap-2 mt-2",children:["leadership","innovation","execution","culture","strategy"].map(e=>(0,n.jsx)("span",{className:"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors cursor-pointer ".concat(m.focusDimensions.includes(e)?"border-transparent bg-primary text-primary-foreground hover:bg-primary/80":"text-foreground border-input bg-background hover:bg-accent hover:text-accent-foreground"),onClick:()=>{f(r=>({...r,focusDimensions:r.focusDimensions.includes(e)?r.focusDimensions.filter(r=>r!==e):[...r.focusDimensions,e]}))},children:e},e))})]}),(0,n.jsxs)("div",{className:"flex gap-2",children:[(0,n.jsxs)(t.z,{onClick:g,disabled:l,className:"flex-1",children:[l&&(0,n.jsx)("div",{className:"mr-2 h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent"}),"生成问卷"]}),(0,n.jsx)(t.z,{variant:"outline",onClick:()=>h("/api/questionnaire/generate"),disabled:l,children:"获取统计"})]})]})]})}),"assessment"===r&&(0,n.jsx)("div",{className:"space-y-4",children:(0,n.jsxs)(o.Zb,{children:[(0,n.jsxs)(o.Ol,{children:[(0,n.jsx)(o.ll,{children:"组织评估 API"}),(0,n.jsx)(o.SZ,{children:"测试 /api/assessment/evaluate 端点"})]}),(0,n.jsxs)(o.aY,{className:"space-y-4",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{htmlFor:"orgName",className:"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",children:"组织名称"}),(0,n.jsx)(a.I,{id:"orgName",value:x.organizationName,onChange:e=>p(r=>({...r,organizationName:e.target.value}))})]}),(0,n.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",children:"组织类型"}),(0,n.jsxs)("select",{value:x.organizationType,onChange:e=>p(r=>({...r,organizationType:e.target.value})),className:"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",children:[(0,n.jsx)("option",{value:"startup",children:"初创公司"}),(0,n.jsx)("option",{value:"sme",children:"中小企业"}),(0,n.jsx)("option",{value:"enterprise",children:"大型企业"})]})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",children:"行业背景"}),(0,n.jsxs)("select",{value:x.industryContext,onChange:e=>p(r=>({...r,industryContext:e.target.value})),className:"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",children:[(0,n.jsx)("option",{value:"technology",children:"科技"}),(0,n.jsx)("option",{value:"finance",children:"金融"}),(0,n.jsx)("option",{value:"healthcare",children:"医疗"})]})]})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",children:"评估响应数据 (JSON)"}),(0,n.jsx)("textarea",{value:JSON.stringify(x.responses,null,2),onChange:e=>{try{let r=JSON.parse(e.target.value);p(e=>({...e,responses:r}))}catch(e){}},rows:6,className:"flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 font-mono"})]}),(0,n.jsxs)("div",{className:"flex gap-2",children:[(0,n.jsxs)(t.z,{onClick:b,disabled:l,className:"flex-1",children:[l&&(0,n.jsx)("div",{className:"mr-2 h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent"}),"生成评估"]}),(0,n.jsx)(t.z,{variant:"outline",onClick:()=>h("/api/assessment/evaluate"),disabled:l,children:"获取统计"})]})]})]})})]})}),(0,n.jsx)("div",{children:(0,n.jsxs)(o.Zb,{className:"h-fit",children:[(0,n.jsx)(o.Ol,{children:(0,n.jsxs)(o.ll,{className:"flex items-center gap-2",children:["API 响应结果",c&&(c.success?(0,n.jsx)("svg",{className:"h-5 w-5 text-green-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})}):(0,n.jsx)("svg",{className:"h-5 w-5 text-red-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"})}))]})}),(0,n.jsx)(o.aY,{children:l?(0,n.jsxs)("div",{className:"flex items-center justify-center py-8",children:[(0,n.jsx)("div",{className:"h-8 w-8 animate-spin rounded-full border-2 border-current border-t-transparent"}),(0,n.jsx)("span",{className:"ml-2",children:"处理中..."})]}):c?(0,n.jsxs)("div",{className:"space-y-4",children:[c.success?(0,n.jsx)("div",{className:"rounded-lg border border-green-200 bg-green-50 p-4",children:(0,n.jsxs)("div",{className:"flex items-center",children:[(0,n.jsx)("svg",{className:"h-4 w-4 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})}),(0,n.jsx)("span",{className:"ml-2 text-sm text-green-800",children:"API 调用成功！"})]})}):(0,n.jsx)("div",{className:"rounded-lg border border-red-200 bg-red-50 p-4",children:(0,n.jsxs)("div",{className:"flex items-center",children:[(0,n.jsx)("svg",{className:"h-4 w-4 text-red-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"})}),(0,n.jsxs)("span",{className:"ml-2 text-sm text-red-800",children:["API 调用失败：",(null===(e=c.error)||void 0===e?void 0:e.message)||"未知错误"]})]})}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",children:"响应数据"}),(0,n.jsx)("pre",{className:"mt-2 p-4 bg-muted rounded-lg text-sm overflow-auto max-h-96",children:JSON.stringify(c,null,2)})]})]}):(0,n.jsx)("div",{className:"text-center py-8 text-muted-foreground",children:"选择一个API端点并点击按钮来测试"})})]})})]})]})}},6651:function(e,r,s){"use strict";s.d(r,{z:function(){return l}});var n=s(7437),i=s(2265),t=s(2169);let o={default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},a={default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"},l=i.forwardRef((e,r)=>{let{className:s,variant:i="default",size:l="default",loading:d=!1,disabled:c,children:u,...m}=e;return(0,n.jsxs)("button",{className:(0,t.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",o[i],a[l],s),ref:r,disabled:c||d,...m,children:[d&&(0,n.jsxs)("svg",{className:"mr-2 h-4 w-4 animate-spin",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,n.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,n.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),u]})});l.displayName="Button"},7418:function(e,r,s){"use strict";s.d(r,{Ol:function(){return l},SZ:function(){return c},Zb:function(){return a},aY:function(){return u},ll:function(){return d}});var n=s(7437),i=s(2265),t=s(2169);let o={default:"bg-card text-card-foreground",outlined:"bg-card text-card-foreground border border-border",elevated:"bg-card text-card-foreground shadow-lg"},a=i.forwardRef((e,r)=>{let{className:s,variant:i="default",...a}=e;return(0,n.jsx)("div",{ref:r,className:(0,t.cn)("rounded-lg border shadow-sm",o[i],s),...a})});a.displayName="Card";let l=i.forwardRef((e,r)=>{let{className:s,...i}=e;return(0,n.jsx)("div",{ref:r,className:(0,t.cn)("flex flex-col space-y-1.5 p-6",s),...i})});l.displayName="CardHeader";let d=i.forwardRef((e,r)=>{let{className:s,...i}=e;return(0,n.jsx)("h3",{ref:r,className:(0,t.cn)("text-2xl font-semibold leading-none tracking-tight",s),...i})});d.displayName="CardTitle";let c=i.forwardRef((e,r)=>{let{className:s,...i}=e;return(0,n.jsx)("p",{ref:r,className:(0,t.cn)("text-sm text-muted-foreground",s),...i})});c.displayName="CardDescription";let u=i.forwardRef((e,r)=>{let{className:s,...i}=e;return(0,n.jsx)("div",{ref:r,className:(0,t.cn)("p-6 pt-0",s),...i})});u.displayName="CardContent",i.forwardRef((e,r)=>{let{className:s,...i}=e;return(0,n.jsx)("div",{ref:r,className:(0,t.cn)("flex items-center p-6 pt-0",s),...i})}).displayName="CardFooter"},2278:function(e,r,s){"use strict";s.d(r,{I:function(){return o}});var n=s(7437),i=s(2265),t=s(2169);let o=i.forwardRef((e,r)=>{let{className:s,type:i="text",error:o,label:a,helperText:l,id:d,...c}=e,u=d||"input-".concat(Math.random().toString(36).substring(2,9));return(0,n.jsxs)("div",{className:"w-full",children:[a&&(0,n.jsx)("label",{htmlFor:u,className:"block text-sm font-medium text-gray-700 mb-1",children:a}),(0,n.jsx)("input",{type:i,id:u,className:(0,t.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",o&&"border-red-500 focus-visible:ring-red-500",s),ref:r,...c}),o&&(0,n.jsx)("p",{className:"mt-1 text-sm text-red-600",children:o}),l&&!o&&(0,n.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:l})]})});o.displayName="Input"},2169:function(e,r,s){"use strict";s.d(r,{cn:function(){return t}});var n=s(7683),i=s(188);function t(){for(var e=arguments.length,r=Array(e),s=0;s<e;s++)r[s]=arguments[s];return(0,i.m6)((0,n.W)(r))}}},function(e){e.O(0,[631,971,938,744],function(){return e(e.s=6627)}),_N_E=e.O()}]);