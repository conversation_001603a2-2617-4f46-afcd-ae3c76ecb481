(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[786],{3620:function(e,s,r){Promise.resolve().then(r.bind(r,3240))},3240:function(e,s,r){"use strict";r.r(s),r.d(s,{default:function(){return i}});var t=r(7437),n=r(2265),l=r(6651),a=r(7418);function i(){let[e,s]=(0,n.useState)(null),[r,i]=(0,n.useState)(null),[c,d]=(0,n.useState)(!1),[o,x]=(0,n.useState)(null),u=async()=>{d(!0),x(null);try{let e=await fetch("/api/test/llm",{method:"POST",headers:{"Content-Type":"application/json"}});if(!e.ok)throw Error("HTTP ".concat(e.status,": ").concat(e.statusText));let r=await e.json();s(r)}catch(e){x(e instanceof Error?e.message:"未知错误")}finally{d(!1)}},m=async()=>{d(!0),x(null);try{let e=await fetch("/api/test/llm",{method:"GET",headers:{"Content-Type":"application/json"}});if(!e.ok)throw Error("HTTP ".concat(e.status,": ").concat(e.statusText));let s=await e.json();i(s)}catch(e){x(e instanceof Error?e.message:"未知错误")}finally{d(!1)}},h=e=>{switch(e){case"success":return"✅ 成功";case"error":return"❌ 失败";default:return"⏳ 未知"}};return(0,t.jsxs)("div",{className:"container mx-auto p-6 space-y-6",children:[(0,t.jsxs)("div",{className:"text-center space-y-2",children:[(0,t.jsx)("h1",{className:"text-3xl font-bold",children:"OCTI API 测试中心"}),(0,t.jsx)("p",{className:"text-muted-foreground",children:"测试LLM API密钥配置和连接状态"})]}),(0,t.jsxs)("div",{className:"flex gap-4 justify-center",children:[(0,t.jsxs)(l.z,{onClick:u,disabled:c,variant:"outline",children:[c?"\uD83D\uDD04 ":"","测试环境变量"]}),(0,t.jsxs)(l.z,{onClick:m,disabled:c,children:[c?"\uD83D\uDD04 ":"","测试LLM连接"]})]}),o&&(0,t.jsxs)(a.Zb,{className:"border-red-200 bg-red-50",children:[(0,t.jsx)(a.Ol,{children:(0,t.jsx)(a.ll,{className:"text-red-700",children:"❌ 测试失败"})}),(0,t.jsx)(a.aY,{children:(0,t.jsx)("p",{className:"text-red-600",children:o})})]}),e&&(0,t.jsxs)(a.Zb,{children:[(0,t.jsxs)(a.Ol,{children:[(0,t.jsx)(a.ll,{children:"✅ 环境变量检查结果"}),(0,t.jsxs)(a.SZ,{children:["检查时间: ",new Date(e.timestamp).toLocaleString()]})]}),(0,t.jsx)(a.aY,{className:"space-y-4",children:(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("h4",{className:"font-medium",children:"MiniMax API"}),(0,t.jsx)("div",{className:"text-sm",children:e.environment.minimax_key_exists?"✅ 已配置":"❌ 未配置"}),(0,t.jsxs)("p",{className:"text-xs text-muted-foreground",children:["预览: ",e.environment.minimax_key_preview]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("h4",{className:"font-medium",children:"DeepSeek API"}),(0,t.jsx)("div",{className:"text-sm",children:e.environment.deepseek_key_exists?"✅ 已配置":"❌ 未配置"}),(0,t.jsxs)("p",{className:"text-xs text-muted-foreground",children:["预览: ",e.environment.deepseek_key_preview]})]})]})})]}),r&&(0,t.jsxs)(a.Zb,{children:[(0,t.jsxs)(a.Ol,{children:[(0,t.jsxs)(a.ll,{children:[r.success?"✅":"❌"," LLM API 连接测试结果"]}),(0,t.jsxs)(a.SZ,{children:["测试时间: ",new Date(r.timestamp).toLocaleString()]})]}),(0,t.jsxs)(a.aY,{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4 p-4 bg-gray-50 rounded-lg",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-medium text-sm",children:"MiniMax密钥长度"}),(0,t.jsx)("p",{className:"text-lg font-mono",children:r.environment.minimax_key_length})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-medium text-sm",children:"DeepSeek密钥长度"}),(0,t.jsx)("p",{className:"text-lg font-mono",children:r.environment.deepseek_key_length})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)(a.Zb,{children:[(0,t.jsx)(a.Ol,{className:"pb-3",children:(0,t.jsxs)(a.ll,{className:"text-lg",children:["MiniMax API - ",h(r.results.minimax.status)]})}),(0,t.jsx)(a.aY,{children:r.results.minimax.error?(0,t.jsxs)("div",{className:"text-red-600 text-sm",children:[(0,t.jsx)("strong",{children:"错误:"})," ",r.results.minimax.error]}):(0,t.jsxs)("div",{className:"text-green-600 text-sm",children:[(0,t.jsx)("strong",{children:"响应:"})," ",r.results.minimax.response||"连接成功"]})})]}),(0,t.jsxs)(a.Zb,{children:[(0,t.jsx)(a.Ol,{className:"pb-3",children:(0,t.jsxs)(a.ll,{className:"text-lg",children:["DeepSeek API - ",h(r.results.deepseek.status)]})}),(0,t.jsx)(a.aY,{children:r.results.deepseek.error?(0,t.jsxs)("div",{className:"text-red-600 text-sm",children:[(0,t.jsx)("strong",{children:"错误:"})," ",r.results.deepseek.error]}):(0,t.jsxs)("div",{className:"text-green-600 text-sm",children:[(0,t.jsx)("strong",{children:"响应:"})," ",r.results.deepseek.response||"连接成功"]})})]})]})]})]}),(0,t.jsxs)(a.Zb,{children:[(0,t.jsx)(a.Ol,{children:(0,t.jsx)(a.ll,{children:"使用说明"})}),(0,t.jsxs)(a.aY,{className:"space-y-2 text-sm text-muted-foreground",children:[(0,t.jsxs)("p",{children:["• ",(0,t.jsx)("strong",{children:"测试环境变量"}),": 检查API密钥是否正确配置在环境变量中"]}),(0,t.jsxs)("p",{children:["• ",(0,t.jsx)("strong",{children:"测试LLM连接"}),": 实际调用LLM API验证连接和响应"]}),(0,t.jsx)("p",{children:"• 确保.env文件中的MINIMAX_API_KEY和DEEPSEEK_API_KEY已正确配置"}),(0,t.jsx)("p",{children:"• 测试成功后即可开始使用OCTI智能评估系统的LLM功能"})]})]})]})}},6651:function(e,s,r){"use strict";r.d(s,{z:function(){return c}});var t=r(7437),n=r(2265),l=r(2169);let a={default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},i={default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"},c=n.forwardRef((e,s)=>{let{className:r,variant:n="default",size:c="default",loading:d=!1,disabled:o,children:x,...u}=e;return(0,t.jsxs)("button",{className:(0,l.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",a[n],i[c],r),ref:s,disabled:o||d,...u,children:[d&&(0,t.jsxs)("svg",{className:"mr-2 h-4 w-4 animate-spin",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,t.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,t.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),x]})});c.displayName="Button"},7418:function(e,s,r){"use strict";r.d(s,{Ol:function(){return c},SZ:function(){return o},Zb:function(){return i},aY:function(){return x},ll:function(){return d}});var t=r(7437),n=r(2265),l=r(2169);let a={default:"bg-card text-card-foreground",outlined:"bg-card text-card-foreground border border-border",elevated:"bg-card text-card-foreground shadow-lg"},i=n.forwardRef((e,s)=>{let{className:r,variant:n="default",...i}=e;return(0,t.jsx)("div",{ref:s,className:(0,l.cn)("rounded-lg border shadow-sm",a[n],r),...i})});i.displayName="Card";let c=n.forwardRef((e,s)=>{let{className:r,...n}=e;return(0,t.jsx)("div",{ref:s,className:(0,l.cn)("flex flex-col space-y-1.5 p-6",r),...n})});c.displayName="CardHeader";let d=n.forwardRef((e,s)=>{let{className:r,...n}=e;return(0,t.jsx)("h3",{ref:s,className:(0,l.cn)("text-2xl font-semibold leading-none tracking-tight",r),...n})});d.displayName="CardTitle";let o=n.forwardRef((e,s)=>{let{className:r,...n}=e;return(0,t.jsx)("p",{ref:s,className:(0,l.cn)("text-sm text-muted-foreground",r),...n})});o.displayName="CardDescription";let x=n.forwardRef((e,s)=>{let{className:r,...n}=e;return(0,t.jsx)("div",{ref:s,className:(0,l.cn)("p-6 pt-0",r),...n})});x.displayName="CardContent",n.forwardRef((e,s)=>{let{className:r,...n}=e;return(0,t.jsx)("div",{ref:s,className:(0,l.cn)("flex items-center p-6 pt-0",r),...n})}).displayName="CardFooter"},2169:function(e,s,r){"use strict";r.d(s,{cn:function(){return l}});var t=r(7683),n=r(188);function l(){for(var e=arguments.length,s=Array(e),r=0;r<e;r++)s[r]=arguments[r];return(0,n.m6)((0,t.W)(s))}}},function(e){e.O(0,[631,971,938,744],function(){return e(e.s=3620)}),_N_E=e.O()}]);