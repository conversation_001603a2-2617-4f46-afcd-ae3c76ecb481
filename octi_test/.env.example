# 数据库配置
DATABASE_URL="postgresql://username:password@localhost:5432/octi_db"

# Redis配置
REDIS_URL="redis://localhost:6379"

# LLM API配置
MINIMAX_API_KEY="your_minimax_api_key_here"
DEEPSEEK_API_KEY="your_deepseek_api_key_here"

# Next.js配置
NEXT_PUBLIC_APP_URL="http://localhost:3000"
NEXTAUTH_SECRET="your_nextauth_secret_here"
NEXTAUTH_URL="http://localhost:3000"

# 应用配置
APP_ENV="development"
LOG_LEVEL="debug"

# 配置文件路径
CONFIG_PATH="./configs"
CONFIG_CACHE_TTL="3600"

# 安全配置
ENCRYPTION_KEY="your_encryption_key_here"
JWT_SECRET="your_jwt_secret_here"

# 监控配置
ENABLE_MONITORING="true"
MONITORING_ENDPOINT="http://localhost:9090"