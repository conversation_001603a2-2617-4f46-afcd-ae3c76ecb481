/** @type {import('next').NextConfig} */
const nextConfig = {
  experimental: {
    appDir: true,
  },
  typescript: {
    // 在生产构建时忽略类型错误
    ignoreBuildErrors: false,
  },
  eslint: {
    // 在生产构建时忽略ESLint错误
    ignoreDuringBuilds: false,
  },
  // 环境变量配置
  env: {
    CUSTOM_KEY: process.env.CUSTOM_KEY,
  },
  // 图片优化配置
  images: {
    domains: ['localhost'],
  },
  // API路由配置
  async rewrites() {
    return [
      {
        source: '/api/v1/:path*',
        destination: '/api/:path*',
      },
    ];
  },
};

module.exports = nextConfig;