#!/bin/bash
# 增强版清理重复文件脚本

echo "🧹 开始清理重复的服务文件..."

# 1. 检查并创建缺失的lib目录
if [ ! -d "src/lib" ]; then
  echo "📁 创建lib目录..."
  mkdir -p src/lib
fi

# 2. 更新所有API路由中的导入引用
echo "🔧 更新API路由导入引用..."

find src/app/api -name "*.ts" -type f | while read file; do
  echo "   处理文件: $file"
  
  # 更新相对路径导入为绝对路径导入
  sed -i.bak \
    -e "s|from '../../../../services/llm/LLMApiClient'|from '@/services/llm/llm-api-client'|g" \
    -e "s|from '../../../../services/llm/PromptBuilder'|from '@/services/llm/prompt-builder'|g" \
    -e "s|from '../../../../services/data/DataFusionEngine'|from '@/services/data/data-fusion-engine'|g" \
    -e "s|from '../../../services/llm/LLMApiClient'|from '@/services/llm/llm-api-client'|g" \
    -e "s|from '../../../services/llm/PromptBuilder'|from '@/services/llm/prompt-builder'|g" \
    -e "s|from '../../../services/data/DataFusionEngine'|from '@/services/data/data-fusion-engine'|g" \
    -e "s|from '../../services/llm/LLMApiClient'|from '@/services/llm/llm-api-client'|g" \
    -e "s|from '../../services/llm/PromptBuilder'|from '@/services/llm/prompt-builder'|g" \
    -e "s|from '../../services/data/DataFusionEngine'|from '@/services/data/data-fusion-engine'|g" \
    "$file"
  
  # 删除备份文件
  rm "${file}.bak" 2>/dev/null || true
done

# 3. 更新services目录中的导入引用
echo "🔧 更新services目录导入引用..."

find src/services -name "*.ts" -type f | while read file; do
  echo "   处理文件: $file"
  
  sed -i.bak \
    -e "s|from '../data/DataFusionEngine'|from '../data/data-fusion-engine'|g" \
    -e "s|from '../llm/LLMApiClient'|from '../llm/llm-api-client'|g" \
    -e "s|from '../llm/PromptBuilder'|from '../llm/prompt-builder'|g" \
    -e "s|from './DataFusionEngine'|from './data-fusion-engine'|g" \
    -e "s|from './LLMApiClient'|from './llm-api-client'|g" \
    -e "s|from './PromptBuilder'|from './prompt-builder'|g" \
    "$file"
  
  rm "${file}.bak" 2>/dev/null || true
done

echo "✅ 导入引用更新完成!"
echo "📊 运行 'npm run build' 验证修复结果"
