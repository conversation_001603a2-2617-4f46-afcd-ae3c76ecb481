#!/bin/bash

# OCTI智能评估系统 - 健康检查脚本
# 用于监控系统各组件的运行状态

set -euo pipefail

# 配置变量
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
LOG_FILE="${LOG_DIR:-$PROJECT_ROOT/logs}/health_check_${TIMESTAMP}.log"

# 服务配置
APP_URL="${APP_URL:-http://localhost:3000}"
API_URL="${API_URL:-http://localhost:3000/api}"
DB_HOST="${DB_HOST:-localhost}"
DB_PORT="${DB_PORT:-5432}"
DB_NAME="${DB_NAME:-octi_db}"
DB_USER="${DB_USER:-monitoring_user}"
DB_PASSWORD="${DB_PASSWORD:-monitoring_password}"
REDIS_HOST="${REDIS_HOST:-localhost}"
REDIS_PORT="${REDIS_PORT:-6379}"
REDIS_PASSWORD="${REDIS_PASSWORD:-}"

# 检查配置
CHECK_APP=true
CHECK_API=true
CHECK_DATABASE=true
CHECK_REDIS=true
CHECK_DOCKER=true
CHECK_DISK=true
CHECK_MEMORY=true
CHECK_CPU=true
VERBOSE=false
OUTPUT_FORMAT="text" # text, json, prometheus

# 阈值配置
DISK_THRESHOLD=80
MEMORY_THRESHOLD=80
CPU_THRESHOLD=80
RESPONSE_TIME_THRESHOLD=5000 # 毫秒

# 通知配置
SLACK_WEBHOOK="${SLACK_WEBHOOK:-}"
EMAIL_TO="${EMAIL_TO:-}"
ALERT_ON_FAILURE=true

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 全局状态
OVERALL_STATUS="healthy"
FAILED_CHECKS=()
WARNING_CHECKS=()
CHECK_RESULTS=()

# 日志函数
log() {
    local level=$1
    shift
    local message="$*"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    if [[ "$VERBOSE" == "true" ]] || [[ "$level" != "DEBUG" ]]; then
        echo -e "[${timestamp}] [${level}] ${message}" | tee -a "$LOG_FILE"
    fi
}

log_info() {
    log "INFO" "${BLUE}$*${NC}"
}

log_warn() {
    log "WARN" "${YELLOW}$*${NC}"
}

log_error() {
    log "ERROR" "${RED}$*${NC}"
}

log_success() {
    log "SUCCESS" "${GREEN}$*${NC}"
}

log_debug() {
    log "DEBUG" "$*"
}

# 发送告警通知
send_alert() {
    local title="$1"
    local message="$2"
    
    if [[ "$ALERT_ON_FAILURE" != "true" ]]; then
        return 0
    fi
    
    # Slack通知
    if [[ -n "$SLACK_WEBHOOK" ]]; then
        curl -X POST -H 'Content-type: application/json' \
            --data "{\"text\":\"${title}\\n${message}\"}" \
            "$SLACK_WEBHOOK" 2>/dev/null || log_warn "Slack告警发送失败"
    fi
    
    # 邮件通知
    if [[ -n "$EMAIL_TO" ]] && command -v mail >/dev/null 2>&1; then
        echo "$message" | mail -s "$title" "$EMAIL_TO" 2>/dev/null || log_warn "邮件告警发送失败"
    fi
}

# 记录检查结果
record_result() {
    local component="$1"
    local status="$2"
    local message="$3"
    local response_time="${4:-0}"
    local details="${5:-}"
    
    local result="{"
    result+="\"component\":\"$component\","
    result+="\"status\":\"$status\","
    result+="\"message\":\"$message\","
    result+="\"response_time\":$response_time,"
    result+="\"timestamp\":\"$(date -Iseconds)\","
    result+="\"details\":\"$details\""
    result+="}"
    
    CHECK_RESULTS+=("$result")
    
    case "$status" in
        "failed")
            FAILED_CHECKS+=("$component")
            OVERALL_STATUS="unhealthy"
            ;;
        "warning")
            WARNING_CHECKS+=("$component")
            if [[ "$OVERALL_STATUS" == "healthy" ]]; then
                OVERALL_STATUS="degraded"
            fi
            ;;
    esac
}

# 检查HTTP服务
check_http_service() {
    local name="$1"
    local url="$2"
    local expected_status="${3:-200}"
    
    log_debug "检查HTTP服务: $name ($url)"
    
    local start_time=$(date +%s%3N)
    local response_code
    local response_time
    
    if response_code=$(curl -s -o /dev/null -w "%{http_code}" --max-time 10 "$url" 2>/dev/null); then
        local end_time=$(date +%s%3N)
        response_time=$((end_time - start_time))
        
        if [[ "$response_code" == "$expected_status" ]]; then
            if [[ $response_time -gt $RESPONSE_TIME_THRESHOLD ]]; then
                log_warn "$name 响应缓慢: ${response_time}ms"
                record_result "$name" "warning" "响应缓慢" "$response_time" "HTTP $response_code"
            else
                log_success "$name 正常: ${response_time}ms"
                record_result "$name" "healthy" "服务正常" "$response_time" "HTTP $response_code"
            fi
        else
            log_error "$name HTTP状态异常: $response_code"
            record_result "$name" "failed" "HTTP状态异常: $response_code" "$response_time"
        fi
    else
        log_error "$name 连接失败"
        record_result "$name" "failed" "连接失败" "0"
    fi
}

# 检查数据库连接
check_database() {
    if [[ "$CHECK_DATABASE" != "true" ]]; then
        return 0
    fi
    
    log_debug "检查数据库连接..."
    
    local start_time=$(date +%s%3N)
    
    export PGPASSWORD="$DB_PASSWORD"
    
    if psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" \
        --no-password -c "SELECT 1;" >/dev/null 2>&1; then
        
        local end_time=$(date +%s%3N)
        local response_time=$((end_time - start_time))
        
        # 检查数据库性能
        local connections=$(psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" \
            --no-password -t -c "SELECT count(*) FROM pg_stat_activity WHERE datname='$DB_NAME';" 2>/dev/null | xargs)
        
        local db_size=$(psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" \
            --no-password -t -c "SELECT pg_size_pretty(pg_database_size('$DB_NAME'));" 2>/dev/null | xargs)
        
        if [[ $response_time -gt 1000 ]]; then
            log_warn "数据库响应缓慢: ${response_time}ms (连接数: $connections)"
            record_result "database" "warning" "响应缓慢" "$response_time" "连接数: $connections, 大小: $db_size"
        else
            log_success "数据库正常: ${response_time}ms (连接数: $connections, 大小: $db_size)"
            record_result "database" "healthy" "数据库正常" "$response_time" "连接数: $connections, 大小: $db_size"
        fi
    else
        log_error "数据库连接失败"
        record_result "database" "failed" "连接失败" "0"
    fi
    
    unset PGPASSWORD
}

# 检查Redis连接
check_redis() {
    if [[ "$CHECK_REDIS" != "true" ]]; then
        return 0
    fi
    
    log_debug "检查Redis连接..."
    
    local start_time=$(date +%s%3N)
    local redis_cmd="redis-cli -h $REDIS_HOST -p $REDIS_PORT"
    
    if [[ -n "$REDIS_PASSWORD" ]]; then
        redis_cmd+" -a $REDIS_PASSWORD"
    fi
    
    if $redis_cmd ping >/dev/null 2>&1; then
        local end_time=$(date +%s%3N)
        local response_time=$((end_time - start_time))
        
        # 检查Redis性能
        local memory_usage=$($redis_cmd info memory 2>/dev/null | grep "used_memory_human:" | cut -d: -f2 | tr -d '\r' || echo "未知")
        local connected_clients=$($redis_cmd info clients 2>/dev/null | grep "connected_clients:" | cut -d: -f2 | tr -d '\r' || echo "未知")
        
        if [[ $response_time -gt 500 ]]; then
            log_warn "Redis响应缓慢: ${response_time}ms"
            record_result "redis" "warning" "响应缓慢" "$response_time" "内存: $memory_usage, 客户端: $connected_clients"
        else
            log_success "Redis正常: ${response_time}ms (内存: $memory_usage, 客户端: $connected_clients)"
            record_result "redis" "healthy" "Redis正常" "$response_time" "内存: $memory_usage, 客户端: $connected_clients"
        fi
    else
        log_error "Redis连接失败"
        record_result "redis" "failed" "连接失败" "0"
    fi
}

# 检查Docker服务
check_docker() {
    if [[ "$CHECK_DOCKER" != "true" ]]; then
        return 0
    fi
    
    log_debug "检查Docker服务..."
    
    if ! command -v docker >/dev/null 2>&1; then
        log_warn "Docker未安装"
        record_result "docker" "warning" "Docker未安装" "0"
        return 0
    fi
    
    if docker info >/dev/null 2>&1; then
        # 检查容器状态
        local running_containers=$(docker ps -q | wc -l)
        local total_containers=$(docker ps -a -q | wc -l)
        local unhealthy_containers=$(docker ps --filter "health=unhealthy" -q | wc -l)
        
        if [[ $unhealthy_containers -gt 0 ]]; then
            log_warn "发现 $unhealthy_containers 个不健康的容器"
            record_result "docker" "warning" "存在不健康容器" "0" "运行: $running_containers, 总计: $total_containers, 不健康: $unhealthy_containers"
        else
            log_success "Docker正常 (运行: $running_containers, 总计: $total_containers)"
            record_result "docker" "healthy" "Docker正常" "0" "运行: $running_containers, 总计: $total_containers"
        fi
    else
        log_error "Docker服务不可用"
        record_result "docker" "failed" "Docker服务不可用" "0"
    fi
}

# 检查磁盘使用率
check_disk_usage() {
    if [[ "$CHECK_DISK" != "true" ]]; then
        return 0
    fi
    
    log_debug "检查磁盘使用率..."
    
    local disk_usage=$(df / | awk 'NR==2 {print $5}' | sed 's/%//')
    local disk_available=$(df -h / | awk 'NR==2 {print $4}')
    
    if [[ $disk_usage -gt $DISK_THRESHOLD ]]; then
        log_error "磁盘使用率过高: ${disk_usage}% (可用: $disk_available)"
        record_result "disk" "failed" "磁盘使用率过高: ${disk_usage}%" "0" "可用空间: $disk_available"
    elif [[ $disk_usage -gt $((DISK_THRESHOLD - 10)) ]]; then
        log_warn "磁盘使用率较高: ${disk_usage}% (可用: $disk_available)"
        record_result "disk" "warning" "磁盘使用率较高: ${disk_usage}%" "0" "可用空间: $disk_available"
    else
        log_success "磁盘使用率正常: ${disk_usage}% (可用: $disk_available)"
        record_result "disk" "healthy" "磁盘使用率正常: ${disk_usage}%" "0" "可用空间: $disk_available"
    fi
}

# 检查内存使用率
check_memory_usage() {
    if [[ "$CHECK_MEMORY" != "true" ]]; then
        return 0
    fi
    
    log_debug "检查内存使用率..."
    
    local memory_info=$(free | grep Mem:)
    local total_mem=$(echo $memory_info | awk '{print $2}')
    local used_mem=$(echo $memory_info | awk '{print $3}')
    local memory_usage=$((used_mem * 100 / total_mem))
    local available_mem=$(echo $memory_info | awk '{print $7}')
    
    local total_mem_human=$(numfmt --to=iec-i --suffix=B $((total_mem * 1024)))
    local available_mem_human=$(numfmt --to=iec-i --suffix=B $((available_mem * 1024)))
    
    if [[ $memory_usage -gt $MEMORY_THRESHOLD ]]; then
        log_error "内存使用率过高: ${memory_usage}% (可用: $available_mem_human)"
        record_result "memory" "failed" "内存使用率过高: ${memory_usage}%" "0" "总计: $total_mem_human, 可用: $available_mem_human"
    elif [[ $memory_usage -gt $((MEMORY_THRESHOLD - 10)) ]]; then
        log_warn "内存使用率较高: ${memory_usage}% (可用: $available_mem_human)"
        record_result "memory" "warning" "内存使用率较高: ${memory_usage}%" "0" "总计: $total_mem_human, 可用: $available_mem_human"
    else
        log_success "内存使用率正常: ${memory_usage}% (可用: $available_mem_human)"
        record_result "memory" "healthy" "内存使用率正常: ${memory_usage}%" "0" "总计: $total_mem_human, 可用: $available_mem_human"
    fi
}

# 检查CPU使用率
check_cpu_usage() {
    if [[ "$CHECK_CPU" != "true" ]]; then
        return 0
    fi
    
    log_debug "检查CPU使用率..."
    
    # 获取1分钟平均负载
    local load_avg=$(uptime | awk -F'load average:' '{print $2}' | awk '{print $1}' | sed 's/,//')
    local cpu_cores=$(nproc)
    local cpu_usage_percent=$(echo "scale=2; $load_avg * 100 / $cpu_cores" | bc -l 2>/dev/null || echo "0")
    local cpu_usage_int=${cpu_usage_percent%.*}
    
    if [[ $cpu_usage_int -gt $CPU_THRESHOLD ]]; then
        log_error "CPU使用率过高: ${cpu_usage_percent}% (负载: $load_avg, 核心: $cpu_cores)"
        record_result "cpu" "failed" "CPU使用率过高: ${cpu_usage_percent}%" "0" "负载: $load_avg, 核心: $cpu_cores"
    elif [[ $cpu_usage_int -gt $((CPU_THRESHOLD - 10)) ]]; then
        log_warn "CPU使用率较高: ${cpu_usage_percent}% (负载: $load_avg, 核心: $cpu_cores)"
        record_result "cpu" "warning" "CPU使用率较高: ${cpu_usage_percent}%" "0" "负载: $load_avg, 核心: $cpu_cores"
    else
        log_success "CPU使用率正常: ${cpu_usage_percent}% (负载: $load_avg, 核心: $cpu_cores)"
        record_result "cpu" "healthy" "CPU使用率正常: ${cpu_usage_percent}%" "0" "负载: $load_avg, 核心: $cpu_cores"
    fi
}

# 输出结果
output_results() {
    case "$OUTPUT_FORMAT" in
        "json")
            output_json
            ;;
        "prometheus")
            output_prometheus
            ;;
        *)
            output_text
            ;;
    esac
}

# 文本格式输出
output_text() {
    echo
    echo "==========================================="
    echo "OCTI系统健康检查报告"
    echo "==========================================="
    echo "检查时间: $(date '+%Y-%m-%d %H:%M:%S')"
    echo "整体状态: $OVERALL_STATUS"
    echo
    
    if [[ ${#FAILED_CHECKS[@]} -gt 0 ]]; then
        echo "❌ 失败组件: ${FAILED_CHECKS[*]}"
    fi
    
    if [[ ${#WARNING_CHECKS[@]} -gt 0 ]]; then
        echo "⚠️  警告组件: ${WARNING_CHECKS[*]}"
    fi
    
    if [[ ${#FAILED_CHECKS[@]} -eq 0 && ${#WARNING_CHECKS[@]} -eq 0 ]]; then
        echo "✅ 所有组件运行正常"
    fi
    
    echo
    echo "详细检查结果:"
    echo "-------------------------------------------"
    
    for result in "${CHECK_RESULTS[@]}"; do
        local component=$(echo "$result" | jq -r '.component' 2>/dev/null || echo "未知")
        local status=$(echo "$result" | jq -r '.status' 2>/dev/null || echo "未知")
        local message=$(echo "$result" | jq -r '.message' 2>/dev/null || echo "未知")
        local response_time=$(echo "$result" | jq -r '.response_time' 2>/dev/null || echo "0")
        local details=$(echo "$result" | jq -r '.details' 2>/dev/null || echo "")
        
        local status_icon
        case "$status" in
            "healthy") status_icon="✅" ;;
            "warning") status_icon="⚠️" ;;
            "failed") status_icon="❌" ;;
            *) status_icon="❓" ;;
        esac
        
        printf "%-15s %s %s" "$component" "$status_icon" "$message"
        
        if [[ $response_time -gt 0 ]]; then
            printf " (${response_time}ms)"
        fi
        
        if [[ -n "$details" ]]; then
            printf " - %s" "$details"
        fi
        
        echo
    done
    
    echo "==========================================="
}

# JSON格式输出
output_json() {
    local json_output="{"
    json_output+="\"timestamp\":\"$(date -Iseconds)\","
    json_output+="\"overall_status\":\"$OVERALL_STATUS\","
    json_output+="\"failed_checks\":[$(printf '\"%s\",' "${FAILED_CHECKS[@]}" | sed 's/,$//')]，"
    json_output+="\"warning_checks\":[$(printf '\"%s\",' "${WARNING_CHECKS[@]}" | sed 's/,$//')]，"
    json_output+="\"results\":[$(printf '%s,' "${CHECK_RESULTS[@]}" | sed 's/,$//')]}"
    
    echo "$json_output" | jq . 2>/dev/null || echo "$json_output"
}

# Prometheus格式输出
output_prometheus() {
    echo "# HELP octi_health_check_status Health check status (0=failed, 1=warning, 2=healthy)"
    echo "# TYPE octi_health_check_status gauge"
    
    for result in "${CHECK_RESULTS[@]}"; do
        local component=$(echo "$result" | jq -r '.component' 2>/dev/null || echo "unknown")
        local status=$(echo "$result" | jq -r '.status' 2>/dev/null || echo "unknown")
        local response_time=$(echo "$result" | jq -r '.response_time' 2>/dev/null || echo "0")
        
        local status_value
        case "$status" in
            "healthy") status_value=2 ;;
            "warning") status_value=1 ;;
            "failed") status_value=0 ;;
            *) status_value=0 ;;
        esac
        
        echo "octi_health_check_status{component=\"$component\"} $status_value"
        
        if [[ $response_time -gt 0 ]]; then
            echo "octi_health_check_response_time_ms{component=\"$component\"} $response_time"
        fi
    done
}

# 主函数
main() {
    # 创建日志目录
    mkdir -p "$(dirname "$LOG_FILE")"
    
    log_info "开始OCTI系统健康检查..."
    
    # 执行各项检查
    if [[ "$CHECK_APP" == "true" ]]; then
        check_http_service "app" "$APP_URL/health"
    fi
    
    if [[ "$CHECK_API" == "true" ]]; then
        check_http_service "api" "$API_URL/health"
    fi
    
    check_database
    check_redis
    check_docker
    check_disk_usage
    check_memory_usage
    check_cpu_usage
    
    # 输出结果
    output_results
    
    # 发送告警
    if [[ ${#FAILED_CHECKS[@]} -gt 0 ]]; then
        send_alert "🚨 OCTI系统健康检查失败" "失败组件: ${FAILED_CHECKS[*]}\n检查时间: $(date '+%Y-%m-%d %H:%M:%S')"
    elif [[ ${#WARNING_CHECKS[@]} -gt 0 ]]; then
        send_alert "⚠️ OCTI系统健康检查警告" "警告组件: ${WARNING_CHECKS[*]}\n检查时间: $(date '+%Y-%m-%d %H:%M:%S')"
    fi
    
    # 设置退出码
    case "$OVERALL_STATUS" in
        "healthy") exit 0 ;;
        "degraded") exit 1 ;;
        "unhealthy") exit 2 ;;
        *) exit 3 ;;
    esac
}

# 显示帮助信息
show_help() {
    cat << EOF
OCTI智能评估系统健康检查脚本

用法: $0 [选项]

选项:
  -h, --help              显示此帮助信息
  -v, --verbose           详细输出
  -f, --format FORMAT     输出格式 (text|json|prometheus)
  --no-app                跳过应用检查
  --no-api                跳过API检查
  --no-database           跳过数据库检查
  --no-redis              跳过Redis检查
  --no-docker             跳过Docker检查
  --no-disk               跳过磁盘检查
  --no-memory             跳过内存检查
  --no-cpu                跳过CPU检查
  --no-alerts             禁用告警通知
  --app-url URL           应用URL (默认: http://localhost:3000)
  --api-url URL           API URL (默认: http://localhost:3000/api)
  --db-host HOST          数据库主机 (默认: localhost)
  --db-port PORT          数据库端口 (默认: 5432)
  --redis-host HOST       Redis主机 (默认: localhost)
  --redis-port PORT       Redis端口 (默认: 6379)
  --disk-threshold PCT    磁盘使用率阈值 (默认: 80)
  --memory-threshold PCT  内存使用率阈值 (默认: 80)
  --cpu-threshold PCT     CPU使用率阈值 (默认: 80)
  --response-threshold MS 响应时间阈值毫秒 (默认: 5000)
  --slack-webhook URL     Slack通知webhook URL
  --email-to EMAIL        邮件通知地址

环境变量:
  APP_URL, API_URL        服务URL
  DB_HOST, DB_PORT        数据库连接
  DB_NAME, DB_USER        数据库信息
  DB_PASSWORD             数据库密码
  REDIS_HOST, REDIS_PORT  Redis连接
  REDIS_PASSWORD          Redis密码
  SLACK_WEBHOOK           Slack通知
  EMAIL_TO                邮件通知
  LOG_DIR                 日志目录

退出码:
  0 - 健康 (healthy)
  1 - 降级 (degraded)
  2 - 不健康 (unhealthy)
  3 - 检查失败

示例:
  $0                                    # 基本健康检查
  $0 -v -f json                         # 详细输出，JSON格式
  $0 --no-docker --no-redis             # 跳过Docker和Redis检查
  $0 --disk-threshold 90                # 自定义磁盘阈值

EOF
}

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -v|--verbose)
            VERBOSE=true
            shift
            ;;
        -f|--format)
            OUTPUT_FORMAT="$2"
            shift 2
            ;;
        --no-app)
            CHECK_APP=false
            shift
            ;;
        --no-api)
            CHECK_API=false
            shift
            ;;
        --no-database)
            CHECK_DATABASE=false
            shift
            ;;
        --no-redis)
            CHECK_REDIS=false
            shift
            ;;
        --no-docker)
            CHECK_DOCKER=false
            shift
            ;;
        --no-disk)
            CHECK_DISK=false
            shift
            ;;
        --no-memory)
            CHECK_MEMORY=false
            shift
            ;;
        --no-cpu)
            CHECK_CPU=false
            shift
            ;;
        --no-alerts)
            ALERT_ON_FAILURE=false
            shift
            ;;
        --app-url)
            APP_URL="$2"
            shift 2
            ;;
        --api-url)
            API_URL="$2"
            shift 2
            ;;
        --db-host)
            DB_HOST="$2"
            shift 2
            ;;
        --db-port)
            DB_PORT="$2"
            shift 2
            ;;
        --redis-host)
            REDIS_HOST="$2"
            shift 2
            ;;
        --redis-port)
            REDIS_PORT="$2"
            shift 2
            ;;
        --disk-threshold)
            DISK_THRESHOLD="$2"
            shift 2
            ;;
        --memory-threshold)
            MEMORY_THRESHOLD="$2"
            shift 2
            ;;
        --cpu-threshold)
            CPU_THRESHOLD="$2"
            shift 2
            ;;
        --response-threshold)
            RESPONSE_TIME_THRESHOLD="$2"
            shift 2
            ;;
        --slack-webhook)
            SLACK_WEBHOOK="$2"
            shift 2
            ;;
        --email-to)
            EMAIL_TO="$2"
            shift 2
            ;;
        *)
            log_error "未知参数: $1"
            show_help
            exit 1
            ;;
    esac
done

# 执行主函数
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi