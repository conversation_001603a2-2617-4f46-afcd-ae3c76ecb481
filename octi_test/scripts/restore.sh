#!/bin/bash

# OCTI智能评估系统 - 数据恢复脚本
# 用于从备份中恢复数据库和应用数据

set -euo pipefail

# 配置变量
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
BACKUP_DIR="${BACKUP_DIR:-/var/backups/octi}"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
LOG_FILE="${BACKUP_DIR}/logs/restore_${TIMESTAMP}.log"

# 数据库配置
DB_HOST="${DB_HOST:-localhost}"
DB_PORT="${DB_PORT:-5432}"
DB_NAME="${DB_NAME:-octi_db}"
DB_USER="${DB_USER:-octi_user}"
DB_PASSWORD="${DB_PASSWORD:-}"

# 恢复配置
BACKUP_NAME=""
FORCE_RESTORE=false
SKIP_CONFIRMATION=false
RESTORE_DATABASE=true
RESTORE_FILES=true
RESTORE_DOCKER=false

# 通知配置
SLACK_WEBHOOK="${SLACK_WEBHOOK:-}"
EMAIL_TO="${EMAIL_TO:-}"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log() {
    local level=$1
    shift
    local message="$*"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo -e "[${timestamp}] [${level}] ${message}" | tee -a "$LOG_FILE"
}

log_info() {
    log "INFO" "${BLUE}$*${NC}"
}

log_warn() {
    log "WARN" "${YELLOW}$*${NC}"
}

log_error() {
    log "ERROR" "${RED}$*${NC}"
}

log_success() {
    log "SUCCESS" "${GREEN}$*${NC}"
}

# 错误处理
error_exit() {
    log_error "$1"
    send_notification "❌ OCTI恢复失败" "恢复过程中发生错误: $1"
    exit 1
}

# 发送通知
send_notification() {
    local title="$1"
    local message="$2"
    
    # Slack通知
    if [[ -n "$SLACK_WEBHOOK" ]]; then
        curl -X POST -H 'Content-type: application/json' \
            --data "{\"text\":\"${title}\\n${message}\"}" \
            "$SLACK_WEBHOOK" 2>/dev/null || log_warn "Slack通知发送失败"
    fi
    
    # 邮件通知
    if [[ -n "$EMAIL_TO" ]] && command -v mail >/dev/null 2>&1; then
        echo "$message" | mail -s "$title" "$EMAIL_TO" 2>/dev/null || log_warn "邮件通知发送失败"
    fi
}

# 检查依赖
check_dependencies() {
    log_info "检查恢复依赖..."
    
    local deps=("pg_restore" "psql" "docker" "gzip" "tar")
    for dep in "${deps[@]}"; do
        if ! command -v "$dep" >/dev/null 2>&1; then
            error_exit "缺少依赖: $dep"
        fi
    done
    
    log_success "依赖检查完成"
}

# 列出可用备份
list_backups() {
    log_info "可用备份列表:"
    
    if [[ ! -d "$BACKUP_DIR" ]]; then
        log_error "备份目录不存在: $BACKUP_DIR"
        return 1
    fi
    
    local backups=()
    while IFS= read -r -d '' manifest; do
        local backup_name=$(basename "$manifest" _manifest.txt)
        backups+=("$backup_name")
    done < <(find "$BACKUP_DIR" -name "*_manifest.txt" -print0 | sort -z)
    
    if [[ ${#backups[@]} -eq 0 ]]; then
        log_warn "未找到任何备份"
        return 1
    fi
    
    echo
    printf "%-5s %-25s %-20s %-15s\n" "序号" "备份名称" "创建时间" "大小"
    printf "%-5s %-25s %-20s %-15s\n" "----" "--------" "--------" "----"
    
    local i=1
    for backup in "${backups[@]}"; do
        local manifest_file="${BACKUP_DIR}/${backup}_manifest.txt"
        local create_time="未知"
        local total_size="未知"
        
        if [[ -f "$manifest_file" ]]; then
            create_time=$(grep "备份时间:" "$manifest_file" 2>/dev/null | cut -d: -f2- | xargs || echo "未知")
            total_size=$(grep "总备份大小:" "$manifest_file" 2>/dev/null | cut -d: -f2 | xargs || echo "未知")
        fi
        
        printf "%-5s %-25s %-20s %-15s\n" "$i" "$backup" "$create_time" "$total_size"
        ((i++))
    done
    echo
    
    return 0
}

# 验证备份完整性
verify_backup() {
    local backup_name="$1"
    
    log_info "验证备份完整性: $backup_name"
    
    local db_backup="${BACKUP_DIR}/database/${backup_name}_database.sql.gz"
    local files_backup="${BACKUP_DIR}/files/${backup_name}_files.tar.gz"
    local manifest="${BACKUP_DIR}/${backup_name}_manifest.txt"
    
    # 检查清单文件
    if [[ ! -f "$manifest" ]]; then
        error_exit "备份清单文件不存在: $manifest"
    fi
    
    # 验证数据库备份
    if [[ "$RESTORE_DATABASE" == "true" ]]; then
        if [[ ! -f "$db_backup" ]]; then
            error_exit "数据库备份文件不存在: $db_backup"
        fi
        
        if ! gzip -t "$db_backup" 2>/dev/null; then
            error_exit "数据库备份文件损坏: $db_backup"
        fi
        
        log_success "数据库备份文件验证通过"
    fi
    
    # 验证文件备份
    if [[ "$RESTORE_FILES" == "true" ]]; then
        if [[ ! -f "$files_backup" ]]; then
            error_exit "文件备份不存在: $files_backup"
        fi
        
        if ! tar -tzf "$files_backup" >/dev/null 2>&1; then
            error_exit "文件备份损坏: $files_backup"
        fi
        
        log_success "文件备份验证通过"
    fi
}

# 确认恢复操作
confirm_restore() {
    if [[ "$SKIP_CONFIRMATION" == "true" ]]; then
        return 0
    fi
    
    echo
    log_warn "⚠️  警告: 恢复操作将覆盖现有数据！"
    echo
    echo "恢复配置:"
    echo "  备份名称: $BACKUP_NAME"
    echo "  数据库恢复: $([ "$RESTORE_DATABASE" == "true" ] && echo "是" || echo "否")"
    echo "  文件恢复: $([ "$RESTORE_FILES" == "true" ] && echo "是" || echo "否")"
    echo "  Docker镜像恢复: $([ "$RESTORE_DOCKER" == "true" ] && echo "是" || echo "否")"
    echo "  数据库: $DB_HOST:$DB_PORT/$DB_NAME"
    echo
    
    if [[ "$FORCE_RESTORE" != "true" ]]; then
        read -p "确认继续恢复操作? (输入 'yes' 确认): " -r
        if [[ ! $REPLY =~ ^[Yy][Ee][Ss]$ ]]; then
            log_info "恢复操作已取消"
            exit 0
        fi
    fi
}

# 停止应用服务
stop_services() {
    log_info "停止应用服务..."
    
    # 停止Docker Compose服务
    if [[ -f "$PROJECT_ROOT/docker-compose.yml" ]]; then
        cd "$PROJECT_ROOT"
        if docker-compose ps -q | grep -q .; then
            docker-compose stop 2>>"$LOG_FILE" || log_warn "停止Docker服务失败"
            log_success "Docker服务已停止"
        else
            log_info "Docker服务未运行"
        fi
    fi
}

# 启动应用服务
start_services() {
    log_info "启动应用服务..."
    
    if [[ -f "$PROJECT_ROOT/docker-compose.yml" ]]; then
        cd "$PROJECT_ROOT"
        if docker-compose up -d 2>>"$LOG_FILE"; then
            log_success "Docker服务已启动"
            
            # 等待服务就绪
            log_info "等待服务就绪..."
            sleep 30
            
            # 检查服务状态
            if docker-compose ps | grep -q "Up"; then
                log_success "服务启动成功"
            else
                log_warn "部分服务可能未正常启动"
            fi
        else
            error_exit "Docker服务启动失败"
        fi
    fi
}

# 恢复数据库
restore_database() {
    if [[ "$RESTORE_DATABASE" != "true" ]]; then
        log_info "跳过数据库恢复"
        return 0
    fi
    
    log_info "开始数据库恢复..."
    
    local db_backup="${BACKUP_DIR}/database/${BACKUP_NAME}_database.sql.gz"
    local temp_file="${BACKUP_DIR}/temp/restore_${TIMESTAMP}.dump"
    
    # 解压备份文件
    if ! gzip -dc "$db_backup" > "$temp_file" 2>>"$LOG_FILE"; then
        error_exit "解压数据库备份失败"
    fi
    
    # 设置数据库密码
    export PGPASSWORD="$DB_PASSWORD"
    
    # 创建数据库备份（恢复前）
    local pre_restore_backup="${BACKUP_DIR}/temp/pre_restore_${TIMESTAMP}.sql"
    log_info "创建恢复前数据库备份..."
    pg_dump -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" \
        --no-password --format=plain > "$pre_restore_backup" 2>>"$LOG_FILE" || log_warn "恢复前备份失败"
    
    # 删除现有数据库内容
    log_info "清理现有数据库内容..."
    psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" \
        --no-password -c "DROP SCHEMA public CASCADE; CREATE SCHEMA public;" 2>>"$LOG_FILE" || error_exit "清理数据库失败"
    
    # 恢复数据库
    log_info "恢复数据库内容..."
    if pg_restore -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" \
        --no-password --verbose --clean --if-exists "$temp_file" 2>>"$LOG_FILE"; then
        log_success "数据库恢复完成"
    else
        log_error "数据库恢复失败，尝试从恢复前备份回滚..."
        
        # 回滚到恢复前状态
        if [[ -f "$pre_restore_backup" ]]; then
            psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" \
                --no-password < "$pre_restore_backup" 2>>"$LOG_FILE" || log_error "回滚失败"
        fi
        
        error_exit "数据库恢复失败"
    fi
    
    # 清理临时文件
    rm -f "$temp_file" "$pre_restore_backup"
    
    unset PGPASSWORD
}

# 恢复文件
restore_files() {
    if [[ "$RESTORE_FILES" != "true" ]]; then
        log_info "跳过文件恢复"
        return 0
    fi
    
    log_info "开始文件恢复..."
    
    local files_backup="${BACKUP_DIR}/files/${BACKUP_NAME}_files.tar.gz"
    
    # 创建文件备份（恢复前）
    local pre_restore_dir="${BACKUP_DIR}/temp/pre_restore_files_${TIMESTAMP}"
    mkdir -p "$pre_restore_dir"
    
    log_info "备份现有文件..."
    
    # 备份重要文件
    local backup_paths=(
        "$PROJECT_ROOT/.env.production"
        "$PROJECT_ROOT/docker-compose.yml"
        "$PROJECT_ROOT/uploads"
        "$PROJECT_ROOT/logs"
    )
    
    for path in "${backup_paths[@]}"; do
        if [[ -e "$path" ]]; then
            local rel_path=$(realpath --relative-to="$PROJECT_ROOT" "$path" 2>/dev/null || basename "$path")
            local backup_path="$pre_restore_dir/$rel_path"
            mkdir -p "$(dirname "$backup_path")"
            cp -r "$path" "$backup_path" 2>/dev/null || log_warn "备份文件失败: $path"
        fi
    done
    
    # 恢复文件
    log_info "恢复文件内容..."
    if tar -xzf "$files_backup" -C / 2>>"$LOG_FILE"; then
        log_success "文件恢复完成"
    else
        log_error "文件恢复失败，尝试回滚..."
        
        # 回滚文件
        if [[ -d "$pre_restore_dir" ]]; then
            cp -r "$pre_restore_dir"/* "$PROJECT_ROOT/" 2>/dev/null || log_error "文件回滚失败"
        fi
        
        error_exit "文件恢复失败"
    fi
    
    # 设置正确的文件权限
    chmod +x "$PROJECT_ROOT/scripts/"*.sh 2>/dev/null || true
    chown -R "$(whoami):$(id -gn)" "$PROJECT_ROOT/uploads" 2>/dev/null || true
}

# 恢复Docker镜像
restore_docker_images() {
    if [[ "$RESTORE_DOCKER" != "true" ]]; then
        log_info "跳过Docker镜像恢复"
        return 0
    fi
    
    log_info "开始Docker镜像恢复..."
    
    local images_backup="${BACKUP_DIR}/files/${BACKUP_NAME}_images.tar.gz"
    
    if [[ ! -f "$images_backup" ]]; then
        log_warn "Docker镜像备份不存在，跳过恢复"
        return 0
    fi
    
    # 加载Docker镜像
    if gzip -dc "$images_backup" | docker load 2>>"$LOG_FILE"; then
        log_success "Docker镜像恢复完成"
    else
        log_warn "Docker镜像恢复失败"
    fi
}

# 验证恢复结果
verify_restore() {
    log_info "验证恢复结果..."
    
    # 验证数据库连接
    if [[ "$RESTORE_DATABASE" == "true" ]]; then
        export PGPASSWORD="$DB_PASSWORD"
        if psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" \
            --no-password -c "SELECT 1;" >/dev/null 2>&1; then
            log_success "数据库连接验证通过"
        else
            log_error "数据库连接验证失败"
        fi
        unset PGPASSWORD
    fi
    
    # 验证关键文件
    if [[ "$RESTORE_FILES" == "true" ]]; then
        local key_files=(
            "$PROJECT_ROOT/.env.production"
            "$PROJECT_ROOT/docker-compose.yml"
        )
        
        for file in "${key_files[@]}"; do
            if [[ -f "$file" ]]; then
                log_success "文件验证通过: $(basename "$file")"
            else
                log_warn "文件缺失: $(basename "$file")"
            fi
        done
    fi
}

# 主函数
main() {
    log_info "开始OCTI系统恢复..."
    
    # 创建日志目录
    mkdir -p "$(dirname "$LOG_FILE")"
    
    # 检查备份名称
    if [[ -z "$BACKUP_NAME" ]]; then
        list_backups
        echo
        read -p "请输入要恢复的备份名称: " -r BACKUP_NAME
        
        if [[ -z "$BACKUP_NAME" ]]; then
            error_exit "未指定备份名称"
        fi
    fi
    
    # 执行恢复流程
    check_dependencies
    verify_backup "$BACKUP_NAME"
    confirm_restore
    stop_services
    restore_database
    restore_files
    restore_docker_images
    start_services
    verify_restore
    
    # 计算恢复时间
    local end_time=$(date +%s)
    local start_time=$(stat -c %Y "$LOG_FILE" 2>/dev/null || echo $end_time)
    local duration=$((end_time - start_time))
    
    log_success "OCTI系统恢复完成！耗时: ${duration}秒"
    
    # 发送成功通知
    send_notification "✅ OCTI恢复成功" "恢复完成\n备份名称: $BACKUP_NAME\n耗时: ${duration}秒"
}

# 显示帮助信息
show_help() {
    cat << EOF
OCTI智能评估系统恢复脚本

用法: $0 [选项] [备份名称]

选项:
  -h, --help              显示此帮助信息
  -l, --list              列出可用备份
  -b, --backup NAME       指定要恢复的备份名称
  -d, --backup-dir DIR    指定备份目录 (默认: /var/backups/octi)
  -f, --force             强制恢复，跳过确认
  -y, --yes               跳过所有确认提示
  --no-database           跳过数据库恢复
  --no-files              跳过文件恢复
  --with-docker           包含Docker镜像恢复
  --db-host HOST          数据库主机 (默认: localhost)
  --db-port PORT          数据库端口 (默认: 5432)
  --db-name NAME          数据库名称 (默认: octi_db)
  --db-user USER          数据库用户 (默认: octi_user)
  --slack-webhook URL     Slack通知webhook URL
  --email-to EMAIL        邮件通知地址

环境变量:
  BACKUP_DIR              备份目录
  DB_HOST, DB_PORT        数据库连接
  DB_NAME, DB_USER        数据库信息
  DB_PASSWORD             数据库密码
  SLACK_WEBHOOK           Slack通知
  EMAIL_TO                邮件通知

示例:
  $0 -l                                 # 列出可用备份
  $0 octi_backup_20231201_120000        # 恢复指定备份
  $0 -f -y --no-files backup_name       # 强制恢复，仅恢复数据库
  $0 --with-docker backup_name          # 包含Docker镜像恢复

EOF
}

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -l|--list)
            list_backups
            exit 0
            ;;
        -b|--backup)
            BACKUP_NAME="$2"
            shift 2
            ;;
        -d|--backup-dir)
            BACKUP_DIR="$2"
            shift 2
            ;;
        -f|--force)
            FORCE_RESTORE=true
            shift
            ;;
        -y|--yes)
            SKIP_CONFIRMATION=true
            shift
            ;;
        --no-database)
            RESTORE_DATABASE=false
            shift
            ;;
        --no-files)
            RESTORE_FILES=false
            shift
            ;;
        --with-docker)
            RESTORE_DOCKER=true
            shift
            ;;
        --db-host)
            DB_HOST="$2"
            shift 2
            ;;
        --db-port)
            DB_PORT="$2"
            shift 2
            ;;
        --db-name)
            DB_NAME="$2"
            shift 2
            ;;
        --db-user)
            DB_USER="$2"
            shift 2
            ;;
        --slack-webhook)
            SLACK_WEBHOOK="$2"
            shift 2
            ;;
        --email-to)
            EMAIL_TO="$2"
            shift 2
            ;;
        -*)
            log_error "未知参数: $1"
            show_help
            exit 1
            ;;
        *)
            if [[ -z "$BACKUP_NAME" ]]; then
                BACKUP_NAME="$1"
            else
                log_error "多余的参数: $1"
                show_help
                exit 1
            fi
            shift
            ;;
    esac
done

# 执行主函数
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi