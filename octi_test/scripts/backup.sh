#!/bin/bash

# OCTI智能评估系统 - 自动备份脚本
# 用于数据库和应用数据的定期备份

set -euo pipefail

# 配置变量
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
BACKUP_DIR="${BACKUP_DIR:-/var/backups/octi}"
RETENTION_DAYS="${RETENTION_DAYS:-30}"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
BACKUP_NAME="octi_backup_${TIMESTAMP}"
LOG_FILE="${BACKUP_DIR}/logs/backup_${TIMESTAMP}.log"

# 数据库配置
DB_HOST="${DB_HOST:-localhost}"
DB_PORT="${DB_PORT:-5432}"
DB_NAME="${DB_NAME:-octi_db}"
DB_USER="${DB_USER:-backup_user}"
DB_PASSWORD="${DB_PASSWORD:-backup_password}"

# 通知配置
SLACK_WEBHOOK="${SLACK_WEBHOOK:-}"
EMAIL_TO="${EMAIL_TO:-}"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log() {
    local level=$1
    shift
    local message="$*"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo -e "[${timestamp}] [${level}] ${message}" | tee -a "$LOG_FILE"
}

log_info() {
    log "INFO" "${BLUE}$*${NC}"
}

log_warn() {
    log "WARN" "${YELLOW}$*${NC}"
}

log_error() {
    log "ERROR" "${RED}$*${NC}"
}

log_success() {
    log "SUCCESS" "${GREEN}$*${NC}"
}

# 错误处理
error_exit() {
    log_error "$1"
    send_notification "❌ OCTI备份失败" "备份过程中发生错误: $1"
    exit 1
}

# 发送通知
send_notification() {
    local title="$1"
    local message="$2"
    
    # Slack通知
    if [[ -n "$SLACK_WEBHOOK" ]]; then
        curl -X POST -H 'Content-type: application/json' \
            --data "{\"text\":\"${title}\\n${message}\"}" \
            "$SLACK_WEBHOOK" 2>/dev/null || log_warn "Slack通知发送失败"
    fi
    
    # 邮件通知
    if [[ -n "$EMAIL_TO" ]] && command -v mail >/dev/null 2>&1; then
        echo "$message" | mail -s "$title" "$EMAIL_TO" 2>/dev/null || log_warn "邮件通知发送失败"
    fi
}

# 检查依赖
check_dependencies() {
    log_info "检查备份依赖..."
    
    local deps=("pg_dump" "docker" "gzip" "tar")
    for dep in "${deps[@]}"; do
        if ! command -v "$dep" >/dev/null 2>&1; then
            error_exit "缺少依赖: $dep"
        fi
    done
    
    log_success "依赖检查完成"
}

# 创建备份目录
setup_backup_dirs() {
    log_info "创建备份目录..."
    
    mkdir -p "$BACKUP_DIR"/{database,files,logs,temp}
    chmod 750 "$BACKUP_DIR"
    
    log_success "备份目录创建完成: $BACKUP_DIR"
}

# 备份数据库
backup_database() {
    log_info "开始数据库备份..."
    
    local db_backup_file="${BACKUP_DIR}/database/${BACKUP_NAME}_database.sql.gz"
    
    # 设置数据库密码
    export PGPASSWORD="$DB_PASSWORD"
    
    # 执行数据库备份
    if pg_dump -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" \
        --verbose --no-password --format=custom --compress=9 \
        --file="${BACKUP_DIR}/temp/${BACKUP_NAME}_database.dump" 2>>"$LOG_FILE"; then
        
        # 压缩备份文件
        gzip -9 "${BACKUP_DIR}/temp/${BACKUP_NAME}_database.dump"
        mv "${BACKUP_DIR}/temp/${BACKUP_NAME}_database.dump.gz" "$db_backup_file"
        
        local file_size=$(du -h "$db_backup_file" | cut -f1)
        log_success "数据库备份完成: $db_backup_file ($file_size)"
    else
        error_exit "数据库备份失败"
    fi
    
    unset PGPASSWORD
}

# 备份应用文件
backup_files() {
    log_info "开始文件备份..."
    
    local files_backup="${BACKUP_DIR}/files/${BACKUP_NAME}_files.tar.gz"
    
    # 备份重要文件和目录
    local backup_paths=(
        "$PROJECT_ROOT/.env.production"
        "$PROJECT_ROOT/docker-compose.yml"
        "$PROJECT_ROOT/docker"
        "$PROJECT_ROOT/scripts"
        "$PROJECT_ROOT/uploads"
        "$PROJECT_ROOT/logs"
    )
    
    # 创建文件列表
    local file_list="${BACKUP_DIR}/temp/file_list.txt"
    > "$file_list"
    
    for path in "${backup_paths[@]}"; do
        if [[ -e "$path" ]]; then
            echo "$path" >> "$file_list"
        else
            log_warn "文件不存在，跳过: $path"
        fi
    done
    
    # 创建tar备份
    if tar -czf "$files_backup" -T "$file_list" 2>>"$LOG_FILE"; then
        local file_size=$(du -h "$files_backup" | cut -f1)
        log_success "文件备份完成: $files_backup ($file_size)"
    else
        error_exit "文件备份失败"
    fi
    
    rm -f "$file_list"
}

# 备份Docker镜像
backup_docker_images() {
    log_info "开始Docker镜像备份..."
    
    local images_backup="${BACKUP_DIR}/files/${BACKUP_NAME}_images.tar.gz"
    
    # 获取OCTI相关镜像
    local images=$(docker images --format "{{.Repository}}:{{.Tag}}" | grep -E "octi|postgres|redis|nginx" | head -10)
    
    if [[ -n "$images" ]]; then
        # 保存镜像
        if echo "$images" | xargs docker save | gzip > "$images_backup" 2>>"$LOG_FILE"; then
            local file_size=$(du -h "$images_backup" | cut -f1)
            log_success "Docker镜像备份完成: $images_backup ($file_size)"
        else
            log_warn "Docker镜像备份失败"
        fi
    else
        log_warn "未找到OCTI相关Docker镜像"
    fi
}

# 创建备份清单
create_manifest() {
    log_info "创建备份清单..."
    
    local manifest_file="${BACKUP_DIR}/${BACKUP_NAME}_manifest.txt"
    
    cat > "$manifest_file" << EOF
OCTI智能评估系统备份清单
========================

备份时间: $(date '+%Y-%m-%d %H:%M:%S')
备份名称: $BACKUP_NAME
备份目录: $BACKUP_DIR

数据库信息:
- 主机: $DB_HOST:$DB_PORT
- 数据库: $DB_NAME
- 用户: $DB_USER

备份文件:
EOF
    
    # 列出所有备份文件
    find "$BACKUP_DIR" -name "${BACKUP_NAME}*" -type f -exec ls -lh {} \; >> "$manifest_file"
    
    # 计算总大小
    local total_size=$(find "$BACKUP_DIR" -name "${BACKUP_NAME}*" -type f -exec du -b {} \; | awk '{sum+=$1} END {print sum}')
    local total_size_human=$(numfmt --to=iec-i --suffix=B "$total_size")
    
    echo "" >> "$manifest_file"
    echo "总备份大小: $total_size_human" >> "$manifest_file"
    
    log_success "备份清单创建完成: $manifest_file"
}

# 清理旧备份
cleanup_old_backups() {
    log_info "清理旧备份文件..."
    
    local deleted_count=0
    
    # 删除超过保留期的备份
    while IFS= read -r -d '' file; do
        rm -f "$file"
        ((deleted_count++))
    done < <(find "$BACKUP_DIR" -name "octi_backup_*" -type f -mtime +"$RETENTION_DAYS" -print0)
    
    if [[ $deleted_count -gt 0 ]]; then
        log_success "清理完成，删除了 $deleted_count 个旧备份文件"
    else
        log_info "没有需要清理的旧备份文件"
    fi
}

# 验证备份
verify_backup() {
    log_info "验证备份完整性..."
    
    local db_backup="${BACKUP_DIR}/database/${BACKUP_NAME}_database.sql.gz"
    local files_backup="${BACKUP_DIR}/files/${BACKUP_NAME}_files.tar.gz"
    
    # 验证数据库备份
    if [[ -f "$db_backup" ]]; then
        if gzip -t "$db_backup" 2>/dev/null; then
            log_success "数据库备份文件完整性验证通过"
        else
            error_exit "数据库备份文件损坏"
        fi
    fi
    
    # 验证文件备份
    if [[ -f "$files_backup" ]]; then
        if tar -tzf "$files_backup" >/dev/null 2>&1; then
            log_success "文件备份完整性验证通过"
        else
            error_exit "文件备份损坏"
        fi
    fi
}

# 主函数
main() {
    log_info "开始OCTI系统备份..."
    
    # 检查是否以root权限运行
    if [[ $EUID -eq 0 ]]; then
        log_warn "建议不要以root权限运行备份脚本"
    fi
    
    # 执行备份流程
    check_dependencies
    setup_backup_dirs
    backup_database
    backup_files
    backup_docker_images
    create_manifest
    verify_backup
    cleanup_old_backups
    
    # 计算备份时间
    local end_time=$(date +%s)
    local start_time=$(stat -c %Y "$LOG_FILE" 2>/dev/null || echo $end_time)
    local duration=$((end_time - start_time))
    
    log_success "OCTI系统备份完成！耗时: ${duration}秒"
    
    # 发送成功通知
    local total_size=$(find "$BACKUP_DIR" -name "${BACKUP_NAME}*" -type f -exec du -b {} \; | awk '{sum+=$1} END {print sum}')
    local total_size_human=$(numfmt --to=iec-i --suffix=B "$total_size")
    
    send_notification "✅ OCTI备份成功" "备份完成\n备份名称: $BACKUP_NAME\n备份大小: $total_size_human\n耗时: ${duration}秒"
}

# 显示帮助信息
show_help() {
    cat << EOF
OCTI智能评估系统备份脚本

用法: $0 [选项]

选项:
  -h, --help              显示此帮助信息
  -d, --backup-dir DIR    指定备份目录 (默认: /var/backups/octi)
  -r, --retention DAYS    备份保留天数 (默认: 30)
  --db-host HOST          数据库主机 (默认: localhost)
  --db-port PORT          数据库端口 (默认: 5432)
  --db-name NAME          数据库名称 (默认: octi_db)
  --db-user USER          数据库用户 (默认: backup_user)
  --slack-webhook URL     Slack通知webhook URL
  --email-to EMAIL        邮件通知地址

环境变量:
  BACKUP_DIR              备份目录
  RETENTION_DAYS          保留天数
  DB_HOST, DB_PORT        数据库连接
  DB_NAME, DB_USER        数据库信息
  DB_PASSWORD             数据库密码
  SLACK_WEBHOOK           Slack通知
  EMAIL_TO                邮件通知

示例:
  $0                                    # 使用默认配置
  $0 -d /backup -r 7                   # 自定义备份目录和保留期
  $0 --slack-webhook https://...        # 启用Slack通知

EOF
}

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -d|--backup-dir)
            BACKUP_DIR="$2"
            shift 2
            ;;
        -r|--retention)
            RETENTION_DAYS="$2"
            shift 2
            ;;
        --db-host)
            DB_HOST="$2"
            shift 2
            ;;
        --db-port)
            DB_PORT="$2"
            shift 2
            ;;
        --db-name)
            DB_NAME="$2"
            shift 2
            ;;
        --db-user)
            DB_USER="$2"
            shift 2
            ;;
        --slack-webhook)
            SLACK_WEBHOOK="$2"
            shift 2
            ;;
        --email-to)
            EMAIL_TO="$2"
            shift 2
            ;;
        *)
            log_error "未知参数: $1"
            show_help
            exit 1
            ;;
    esac
done

# 执行主函数
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi