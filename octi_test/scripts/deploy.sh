#!/bin/bash

# OCTI智能评估系统 - 部署脚本
# 用于自动化部署到生产环境

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 配置变量
APP_NAME="octi-assessment-system"
DOCKER_COMPOSE_FILE="docker-compose.yml"
BACKUP_DIR="/opt/backups"
DEPLOY_DIR="/opt/octi-production"
GIT_REPO="https://github.com/your-org/octi-assessment-system.git"
BRANCH="main"

# 检查必要的工具
check_dependencies() {
    log_info "检查部署依赖..."
    
    local deps=("docker" "docker-compose" "git" "curl")
    for dep in "${deps[@]}"; do
        if ! command -v "$dep" &> /dev/null; then
            log_error "缺少依赖: $dep"
            exit 1
        fi
    done
    
    log_success "所有依赖检查通过"
}

# 创建备份
create_backup() {
    log_info "创建数据备份..."
    
    local timestamp=$(date +"%Y%m%d_%H%M%S")
    local backup_file="${BACKUP_DIR}/octi_backup_${timestamp}.sql"
    
    # 创建备份目录
    mkdir -p "$BACKUP_DIR"
    
    # 备份数据库
    docker-compose exec -T postgres pg_dump -U octi_user octi_db > "$backup_file"
    
    if [ $? -eq 0 ]; then
        log_success "数据库备份完成: $backup_file"
        
        # 压缩备份文件
        gzip "$backup_file"
        log_success "备份文件已压缩: ${backup_file}.gz"
    else
        log_error "数据库备份失败"
        exit 1
    fi
}

# 更新代码
update_code() {
    log_info "更新应用代码..."
    
    cd "$DEPLOY_DIR"
    
    # 拉取最新代码
    git fetch origin
    git reset --hard "origin/$BRANCH"
    
    log_success "代码更新完成"
}

# 构建镜像
build_images() {
    log_info "构建Docker镜像..."
    
    cd "$DEPLOY_DIR"
    
    # 构建应用镜像
    docker-compose build --no-cache app
    
    if [ $? -eq 0 ]; then
        log_success "镜像构建完成"
    else
        log_error "镜像构建失败"
        exit 1
    fi
}

# 运行数据库迁移
run_migrations() {
    log_info "运行数据库迁移..."
    
    cd "$DEPLOY_DIR"
    
    # 运行Prisma迁移
    docker-compose exec -T app npx prisma migrate deploy
    
    if [ $? -eq 0 ]; then
        log_success "数据库迁移完成"
    else
        log_error "数据库迁移失败"
        exit 1
    fi
}

# 部署应用
deploy_app() {
    log_info "部署应用..."
    
    cd "$DEPLOY_DIR"
    
    # 停止旧容器
    docker-compose down
    
    # 启动新容器
    docker-compose up -d
    
    if [ $? -eq 0 ]; then
        log_success "应用部署完成"
    else
        log_error "应用部署失败"
        exit 1
    fi
}

# 健康检查
health_check() {
    log_info "执行健康检查..."
    
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if curl -f http://localhost:3000/api/health &> /dev/null; then
            log_success "应用健康检查通过"
            return 0
        fi
        
        log_warning "健康检查失败，重试中... ($attempt/$max_attempts)"
        sleep 10
        ((attempt++))
    done
    
    log_error "应用健康检查失败"
    return 1
}

# 清理旧镜像
cleanup() {
    log_info "清理旧Docker镜像..."
    
    # 删除未使用的镜像
    docker image prune -f
    
    # 删除未使用的容器
    docker container prune -f
    
    # 删除未使用的网络
    docker network prune -f
    
    # 删除未使用的卷（谨慎使用）
    # docker volume prune -f
    
    log_success "清理完成"
}

# 发送通知
send_notification() {
    local status="$1"
    local message="$2"
    
    if [ -n "$SLACK_WEBHOOK_URL" ]; then
        local emoji="✅"
        if [ "$status" = "error" ]; then
            emoji="❌"
        fi
        
        curl -X POST -H 'Content-type: application/json' \
            --data "{\"text\":\"$emoji OCTI部署通知: $message\"}" \
            "$SLACK_WEBHOOK_URL"
    fi
}

# 回滚函数
rollback() {
    log_warning "开始回滚..."
    
    cd "$DEPLOY_DIR"
    
    # 回滚到上一个版本
    git reset --hard HEAD~1
    
    # 重新构建和部署
    build_images
    deploy_app
    
    if health_check; then
        log_success "回滚完成"
        send_notification "success" "应用已成功回滚到上一个版本"
    else
        log_error "回滚失败"
        send_notification "error" "应用回滚失败，需要手动干预"
        exit 1
    fi
}

# 主部署流程
main() {
    log_info "开始部署 $APP_NAME..."
    
    # 检查是否为root用户
    if [ "$EUID" -ne 0 ]; then
        log_error "请使用root权限运行此脚本"
        exit 1
    fi
    
    # 检查部署目录
    if [ ! -d "$DEPLOY_DIR" ]; then
        log_error "部署目录不存在: $DEPLOY_DIR"
        exit 1
    fi
    
    # 执行部署步骤
    check_dependencies
    create_backup
    update_code
    build_images
    run_migrations
    deploy_app
    
    # 健康检查
    if health_check; then
        cleanup
        log_success "🎉 部署成功完成！"
        send_notification "success" "应用已成功部署到生产环境"
    else
        log_error "部署失败，开始回滚..."
        rollback
    fi
}

# 处理命令行参数
case "${1:-}" in
    "rollback")
        rollback
        ;;
    "health")
        health_check
        ;;
    "backup")
        create_backup
        ;;
    "cleanup")
        cleanup
        ;;
    "")
        main
        ;;
    *)
        echo "用法: $0 [rollback|health|backup|cleanup]"
        echo "  rollback  - 回滚到上一个版本"
        echo "  health    - 执行健康检查"
        echo "  backup    - 创建数据备份"
        echo "  cleanup   - 清理Docker资源"
        exit 1
        ;;
esac