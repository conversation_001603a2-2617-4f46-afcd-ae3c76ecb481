#!/bin/bash
# 验证修复脚本

echo "🔍 验证修复结果..."

# 1. 检查是否还有旧的导入引用
echo "📋 检查剩余的旧导入引用..."
OLD_IMPORTS=$(grep -r "DataFusionEngine\|LLMApiClient\|PromptBuilder" src/ --include="*.ts" | grep -v "kebab-case" | grep -v "data-fusion-engine\|llm-api-client\|prompt-builder")

if [ -n "$OLD_IMPORTS" ]; then
  echo "⚠️  发现未更新的导入引用:"
  echo "$OLD_IMPORTS"
else
  echo "✅ 所有导入引用已更新"
fi

# 2. 检查是否还有重复文件
echo "📋 检查剩余的重复文件..."
DUPLICATE_FILES=$(find src -name "*DataFusionEngine*" -o -name "*LLMApiClient*" -o -name "*PromptBuilder*")

if [ -n "$DUPLICATE_FILES" ]; then
  echo "⚠️  发现剩余的重复文件:"
  echo "$DUPLICATE_FILES"
else
  echo "✅ 所有重复文件已清理"
fi

# 3. 检查必需的文件是否存在
echo "📋 检查必需文件..."
REQUIRED_FILES=(
  "src/lib/api-response.ts"
  "src/services/data/data-fusion-engine.ts"
  "src/services/llm/llm-api-client.ts"
  "src/services/llm/prompt-builder.ts"
)

for file in "${REQUIRED_FILES[@]}"; do
  if [ -f "$file" ]; then
    echo "✅ $file 存在"
  else
    echo "❌ $file 缺失"
  fi
done

# 4. 运行类型检查
echo "🔧 运行类型检查..."
npm run type-check

echo "📊 修复验证完成!"