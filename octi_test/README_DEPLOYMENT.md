# OCTI智能评估系统 - 部署配置完整指南

本文档是OCTI智能评估系统阶段六部署工作的完整总结，包含所有Docker、CI/CD、监控、备份等配置的详细说明。

## 📋 部署配置概览

### 已完成的配置文件

#### 🐳 Docker配置
- `Dockerfile` - 多阶段构建配置（开发/生产环境）
- `docker-compose.yml` - 生产环境容器编排
- `docker-compose.dev.yml` - 开发环境容器编排

#### 🔧 服务配置
- `docker/nginx/nginx.conf` - Nginx主配置
- `docker/nginx/conf.d/default.conf` - 生产环境站点配置
- `docker/nginx/dev.conf` - 开发环境站点配置
- `docker/redis/redis.conf` - Redis配置
- `docker/postgres/init/01-init-db.sql` - 数据库初始化脚本

#### 📊 监控配置
- `docker/prometheus/prometheus.yml` - 生产环境监控配置
- `docker/prometheus/dev.yml` - 开发环境监控配置
- `docker/prometheus/alert_rules.yml` - 告警规则
- `docker/grafana/provisioning/dashboards/dashboard.yml` - 仪表板配置
- `docker/grafana/provisioning/datasources/prometheus.yml` - 数据源配置

#### 🚀 CI/CD配置
- `.github/workflows/ci-cd.yml` - GitHub Actions工作流

#### 🔐 环境配置
- `.env.template` - 环境变量模板
- `.env.production` - 生产环境配置
- `.env.development` - 开发环境配置

#### 📜 运维脚本
- `scripts/deploy.sh` - 自动化部署脚本
- `scripts/backup.sh` - 数据备份脚本
- `scripts/restore.sh` - 数据恢复脚本
- `scripts/health-check.sh` - 健康检查脚本

## 🚀 快速开始

### 开发环境部署

```bash
# 1. 克隆项目
git clone <repository-url> octi-system
cd octi-system

# 2. 复制环境配置
cp .env.template .env.development

# 3. 启动开发环境
docker-compose -f docker-compose.dev.yml up --build -d

# 4. 查看服务状态
docker-compose -f docker-compose.dev.yml ps
```

**开发环境服务地址：**
- 主应用: http://localhost:3000
- Nginx: http://localhost:80
- Grafana: http://localhost:3001 (admin/admin123)
- Prometheus: http://localhost:9090
- pgAdmin: http://localhost:8080 (<EMAIL>/admin123)
- Redis Commander: http://localhost:8081 (admin/admin123)
- Mailhog: http://localhost:8025
- File Browser: http://localhost:8082

### 生产环境部署

```bash
# 1. 配置生产环境变量
cp .env.template .env.production
vim .env.production  # 修改关键配置

# 2. 配置SSL证书
mkdir -p docker/nginx/ssl
# 将SSL证书放入该目录

# 3. 使用自动化部署脚本
./scripts/deploy.sh

# 4. 验证部署
./scripts/health-check.sh
```

## 🏗️ 架构说明

### Docker多阶段构建

```mermaid
graph TD
    A[base: Node.js 18 Alpine] --> B[development: 开发环境]
    A --> C[deps: 依赖安装]
    A --> D[builder: 构建阶段]
    C --> E[production: 生产环境]
    D --> E
```

**构建目标：**
- `development`: 开发环境，包含热重载和调试工具
- `deps`: 生产依赖安装和Prisma客户端生成
- `builder`: 应用构建阶段
- `production`: 最终生产镜像，优化大小和安全性

### 服务架构

```mermaid
graph TB
    subgraph "负载均衡层"
        N[Nginx]
    end
    
    subgraph "应用层"
        A1[OCTI App 1]
        A2[OCTI App 2]
        A3[OCTI App N]
    end
    
    subgraph "数据层"
        P[PostgreSQL]
        R[Redis]
    end
    
    subgraph "监控层"
        PR[Prometheus]
        G[Grafana]
    end
    
    N --> A1
    N --> A2
    N --> A3
    A1 --> P
    A1 --> R
    A2 --> P
    A2 --> R
    A3 --> P
    A3 --> R
    PR --> A1
    PR --> A2
    PR --> A3
    PR --> P
    PR --> R
    G --> PR
```

## 🔧 配置详解

### 关键环境变量

```bash
# 应用配置
NODE_ENV=production
APP_URL=https://your-domain.com
APP_PORT=3000

# 数据库配置
DB_HOST=postgres
DB_PORT=5432
DB_NAME=octi_prod_db
DB_USER=octi_prod_user
DB_PASSWORD=your_secure_password

# Redis配置
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=your_redis_password

# JWT配置
JWT_SECRET=your_jwt_secret_key
JWT_EXPIRES_IN=24h

# LLM API配置
OPENAI_API_KEY=your_openai_api_key
OPENAI_MODEL=gpt-4

# 腾讯云配置
TENCENT_SECRET_ID=your_secret_id
TENCENT_SECRET_KEY=your_secret_key
```

### Nginx配置特性

- **SSL/TLS**: 支持HTTPS和HTTP/2
- **安全头**: HSTS、CSP、XSS保护
- **限流**: API、认证端点分级限流
- **缓存**: 静态资源缓存优化
- **压缩**: Gzip压缩减少传输大小
- **健康检查**: 自动故障转移

### 监控配置

**Prometheus监控指标：**
- 应用性能指标（响应时间、错误率、吞吐量）
- 数据库性能指标（连接数、查询时间、慢查询）
- Redis性能指标（内存使用、命中率、连接数）
- 系统资源指标（CPU、内存、磁盘、网络）
- 业务指标（评估完成率、LLM调用成功率）

**Grafana仪表板：**
- OCTI系统概览
- 应用性能监控
- 数据库性能监控
- 基础设施监控
- 业务指标监控

## 🔄 CI/CD流程

### GitHub Actions工作流

```mermaid
graph TD
    A[代码推送] --> B[代码检查]
    B --> C[TypeScript类型检查]
    C --> D[单元测试]
    D --> E[覆盖率报告]
    E --> F[安全扫描]
    F --> G[Docker构建]
    G --> H[集成测试]
    H --> I{分支检查}
    I -->|develop| J[部署到测试环境]
    I -->|main| K[部署到生产环境]
    J --> L[健康检查]
    K --> L
    L --> M[Slack通知]
```

**触发条件：**
- `develop`分支：自动部署到测试环境
- `main`分支：自动部署到生产环境
- Pull Request：运行测试和检查

## 🛡️ 安全配置

### 容器安全
- 非root用户运行应用
- 最小化镜像（Alpine Linux）
- 多阶段构建减少攻击面
- 健康检查确保服务可用性

### 网络安全
- SSL/TLS加密传输
- 安全头防护
- API限流防止滥用
- 内部网络隔离

### 数据安全
- 数据库连接加密
- 敏感信息环境变量管理
- 定期自动备份
- 访问权限控制

## 📊 性能优化

### 应用层优化
- Next.js生产构建优化
- 静态资源CDN加速
- 数据库连接池
- Redis缓存策略

### 基础设施优化
- Nginx反向代理和负载均衡
- 数据库索引优化
- Redis内存管理
- 容器资源限制

## 🔧 运维操作

### 日常维护

```bash
# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f app

# 重启服务
docker-compose restart app

# 更新部署
./scripts/deploy.sh --update
```

### 备份恢复

```bash
# 手动备份
./scripts/backup.sh

# 从备份恢复
./scripts/restore.sh --backup-file backups/octi_backup_20231201_020000.tar.gz

# 设置定时备份
crontab -e
# 添加: 0 2 * * * /path/to/scripts/backup.sh --auto
```

### 健康检查

```bash
# 完整健康检查
./scripts/health-check.sh

# JSON格式输出
./scripts/health-check.sh --format json

# 检查特定服务
./scripts/health-check.sh --service app
```

## 🚨 故障排除

### 常见问题

#### 应用无法启动
```bash
# 检查日志
docker-compose logs app

# 检查环境变量
docker-compose exec app env | grep -E "DB_|REDIS_"

# 检查端口占用
netstat -tlnp | grep :3000
```

#### 数据库连接失败
```bash
# 检查数据库状态
docker-compose exec postgres pg_isready

# 测试连接
docker-compose exec app npm run db:test
```

#### Redis连接失败
```bash
# 检查Redis状态
docker-compose exec redis redis-cli ping

# 查看Redis日志
docker-compose logs redis
```

### 性能问题

```bash
# 查看容器资源使用
docker stats

# 查看应用性能指标
curl http://localhost/metrics

# 查看数据库性能
docker-compose exec postgres psql -U octi_prod_user -d octi_prod_db -c "SELECT * FROM pg_stat_activity;"
```

## 📈 扩容指南

### 水平扩容

```bash
# 扩容应用实例
docker-compose up -d --scale app=3

# 更新Nginx负载均衡配置
vim docker/nginx/conf.d/default.conf
```

### 垂直扩容

```yaml
# 修改docker-compose.yml中的资源限制
services:
  app:
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 4G
        reservations:
          cpus: '1.0'
          memory: 2G
```

## 📚 相关文档

- [详细部署指南](docs/deployment_guide.md)
- [API文档](docs/API_documentation.md)
- [项目架构](docs/Project_architecture.md)
- [安全考虑](docs/security_considerations.md)
- [测试策略](docs/testing_strategy.md)

## 🆘 技术支持

如果在部署过程中遇到问题：

1. 查看本文档的故障排除部分
2. 检查项目的GitHub Issues
3. 查看详细的部署指南文档
4. 联系技术支持团队

---

**版本信息：**
- OCTI系统版本：v4.0
- Docker版本：20.10+
- Docker Compose版本：2.0+
- Node.js版本：18.0+
- PostgreSQL版本：15+
- Redis版本：7+

**最后更新：** 2024年12月

---

🎉 **恭喜！** OCTI智能评估系统的部署配置已经完成。现在您可以轻松地在开发和生产环境中部署和运行系统了。