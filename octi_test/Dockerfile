# OCTI智能评估系统 Dockerfile
# 多阶段构建，支持开发和生产环境

# 基础镜像阶段
FROM node:18-alpine AS base

# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN apk add --no-cache \
    curl \
    bash \
    git \
    python3 \
    make \
    g++ \
    libc6-compat \
    && rm -rf /var/cache/apk/*

# 复制package文件
COPY package*.json ./

# 开发环境阶段
FROM base AS development

# 安装所有依赖（包括开发依赖）
RUN npm ci --include=dev

# 复制源代码
COPY . .

# 暴露端口
EXPOSE 3000 9229

# 设置环境变量
ENV NODE_ENV=development
ENV LOG_LEVEL=debug

# 启动命令（开发模式）
CMD ["npm", "run", "dev"]

# 依赖安装阶段
FROM base AS deps

# 复制Prisma配置
COPY prisma ./prisma/

# 安装生产依赖
RUN npm ci --only=production && npm cache clean --force

# 生成Prisma客户端
RUN npx prisma generate

# 构建阶段
FROM base AS builder

# 安装所有依赖
RUN npm ci --include=dev

# 复制源代码
COPY . .

# 设置环境变量
ENV NEXT_TELEMETRY_DISABLED=1
ENV NODE_ENV=production

# 生成Prisma客户端
RUN npx prisma generate

# 构建应用
RUN npm run build

# 生产环境运行阶段
FROM node:18-alpine AS production

# 安装运行时依赖
RUN apk add --no-cache \
    curl \
    bash \
    libc6-compat \
    && rm -rf /var/cache/apk/*

# 设置工作目录
WORKDIR /app

# 设置环境变量
ENV NODE_ENV=production
ENV NEXT_TELEMETRY_DISABLED=1
ENV PORT=3000
ENV HOSTNAME="0.0.0.0"

# 创建非root用户
RUN addgroup --system --gid 1001 nodejs && \
    adduser --system --uid 1001 nextjs

# 复制生产依赖
COPY --from=deps --chown=nextjs:nodejs /app/node_modules ./node_modules
COPY --from=deps --chown=nextjs:nodejs /app/node_modules/.prisma ./node_modules/.prisma

# 复制构建产物
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static
COPY --from=builder --chown=nextjs:nodejs /app/public ./public

# 复制Prisma相关文件
COPY --from=builder --chown=nextjs:nodejs /app/prisma ./prisma

# 复制配置文件
COPY --from=builder --chown=nextjs:nodejs /app/configs ./configs

# 创建必要目录
RUN mkdir -p /app/logs /app/uploads && \
    chown -R nextjs:nodejs /app/logs /app/uploads

# 切换到非root用户
USER nextjs

# 暴露端口
EXPOSE 3000

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=40s --retries=3 \
    CMD curl -f http://localhost:3000/api/health || exit 1

# 启动应用
CMD ["node", "server.js"]

# 构建说明
# ==========================================
# 构建开发环境:
# docker build --target development -t octi:dev .
# 
# 构建生产环境:
# docker build --target production -t octi:prod .
# 
# 运行开发环境:
# docker run -p 3000:3000 -v $(pwd):/app octi:dev
# 
# 运行生产环境:
# docker run -p 3000:3000 octi:prod
# ==========================================