# OCTI智能评估系统 - 开发环境Docker Compose配置
# 专为本地开发优化的容器编排配置

version: '3.8'

services:
  # 主应用服务 (开发模式)
  app:
    build:
      context: .
      dockerfile: Dockerfile
      target: development
    container_name: octi_app_dev
    ports:
      - "3000:3000"
      - "9229:9229" # Node.js调试端口
    environment:
      - NODE_ENV=development
      - DB_HOST=postgres
      - REDIS_HOST=redis
      - LOG_LEVEL=debug
    volumes:
      - .:/app
      - /app/node_modules
      - ./logs:/app/logs
      - ./uploads:/app/uploads
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - octi_dev_network
    restart: unless-stopped
    command: npm run dev
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # PostgreSQL数据库 (开发配置)
  postgres:
    image: postgres:15-alpine
    container_name: octi_postgres_dev
    ports:
      - "5432:5432"
    environment:
      POSTGRES_DB: octi_dev_db
      POSTGRES_USER: octi_dev_user
      POSTGRES_PASSWORD: dev_password_123
      POSTGRES_INITDB_ARGS: "--encoding=UTF8 --locale=C"
    volumes:
      - postgres_dev_data:/var/lib/postgresql/data
      - ./docker/postgres/init:/docker-entrypoint-initdb.d
      - ./logs/postgres:/var/log/postgresql
    networks:
      - octi_dev_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U octi_dev_user -d octi_dev_db"]
      interval: 10s
      timeout: 5s
      retries: 5
    command: >
      postgres
      -c log_statement=all
      -c log_destination=stderr
      -c log_min_duration_statement=0
      -c log_line_prefix='%t [%p]: [%l-1] user=%u,db=%d,app=%a,client=%h '
      -c shared_preload_libraries=pg_stat_statements

  # Redis缓存 (开发配置)
  redis:
    image: redis:7-alpine
    container_name: octi_redis_dev
    ports:
      - "6379:6379"
    volumes:
      - redis_dev_data:/data
      - ./logs/redis:/var/log/redis
    networks:
      - octi_dev_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 3s
      retries: 3
    command: >
      redis-server
      --appendonly yes
      --appendfsync everysec
      --save 60 1000
      --loglevel verbose
      --logfile /var/log/redis/redis.log

  # Nginx反向代理 (开发配置)
  nginx:
    image: nginx:alpine
    container_name: octi_nginx_dev
    ports:
      - "80:80"
    volumes:
      - ./docker/nginx/dev.conf:/etc/nginx/conf.d/default.conf
      - ./public:/usr/share/nginx/html
      - ./logs/nginx:/var/log/nginx
    depends_on:
      - app
    networks:
      - octi_dev_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Prometheus监控 (开发环境)
  prometheus:
    image: prom/prometheus:latest
    container_name: octi_prometheus_dev
    ports:
      - "9090:9090"
    volumes:
      - ./docker/prometheus/dev.yml:/etc/prometheus/prometheus.yml
      - ./docker/prometheus/alert_rules.yml:/etc/prometheus/alert_rules.yml
      - prometheus_dev_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=7d'
      - '--web.enable-lifecycle'
      - '--log.level=debug'
    networks:
      - octi_dev_network
    restart: unless-stopped

  # Grafana监控面板 (开发环境)
  grafana:
    image: grafana/grafana:latest
    container_name: octi_grafana_dev
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin123
      - GF_USERS_ALLOW_SIGN_UP=false
      - GF_INSTALL_PLUGINS=grafana-clock-panel,grafana-simple-json-datasource
      - GF_LOG_LEVEL=debug
    volumes:
      - grafana_dev_data:/var/lib/grafana
      - ./docker/grafana/provisioning:/etc/grafana/provisioning
    depends_on:
      - prometheus
    networks:
      - octi_dev_network
    restart: unless-stopped

  # Redis Commander (Redis管理界面)
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: octi_redis_commander_dev
    ports:
      - "8081:8081"
    environment:
      - REDIS_HOSTS=local:redis:6379
      - HTTP_USER=admin
      - HTTP_PASSWORD=admin123
    depends_on:
      - redis
    networks:
      - octi_dev_network
    restart: unless-stopped

  # pgAdmin (PostgreSQL管理界面)
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: octi_pgadmin_dev
    ports:
      - "8080:80"
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin123
      PGADMIN_CONFIG_SERVER_MODE: 'False'
    volumes:
      - pgadmin_dev_data:/var/lib/pgadmin
      - ./docker/pgadmin/servers.json:/pgadmin4/servers.json
    depends_on:
      - postgres
    networks:
      - octi_dev_network
    restart: unless-stopped

  # Mailhog (邮件测试服务)
  mailhog:
    image: mailhog/mailhog:latest
    container_name: octi_mailhog_dev
    ports:
      - "1025:1025" # SMTP端口
      - "8025:8025" # Web界面端口
    networks:
      - octi_dev_network
    restart: unless-stopped

  # 文件浏览器 (开发环境文件管理)
  filebrowser:
    image: filebrowser/filebrowser:latest
    container_name: octi_filebrowser_dev
    ports:
      - "8082:80"
    volumes:
      - .:/srv
      - ./docker/filebrowser/database.db:/database.db
      - ./docker/filebrowser/config.json:/.filebrowser.json
    networks:
      - octi_dev_network
    restart: unless-stopped

# 数据卷定义
volumes:
  postgres_dev_data:
    driver: local
  redis_dev_data:
    driver: local
  prometheus_dev_data:
    driver: local
  grafana_dev_data:
    driver: local
  pgadmin_dev_data:
    driver: local

# 网络定义
networks:
  octi_dev_network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# 开发环境说明
# ==========================================
# 服务访问地址:
# - 主应用: http://localhost:3000
# - Nginx: http://localhost:80
# - Grafana: http://localhost:3001 (admin/admin123)
# - Prometheus: http://localhost:9090
# - pgAdmin: http://localhost:8080 (<EMAIL>/admin123)
# - Redis Commander: http://localhost:8081 (admin/admin123)
# - Mailhog: http://localhost:8025
# - File Browser: http://localhost:8082
# 
# 数据库连接:
# - Host: localhost
# - Port: 5432
# - Database: octi_dev_db
# - User: octi_dev_user
# - Password: dev_password_123
# 
# Redis连接:
# - Host: localhost
# - Port: 6379
# - No password
# 
# 开发命令:
# - 启动: docker-compose -f docker-compose.dev.yml up -d
# - 停止: docker-compose -f docker-compose.dev.yml down
# - 查看日志: docker-compose -f docker-compose.dev.yml logs -f
# - 重建: docker-compose -f docker-compose.dev.yml up --build -d
# ==========================================