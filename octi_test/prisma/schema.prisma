// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// 用户模型
model User {
  id        String   @id @default(cuid())
  email     String   @unique
  name      String?
  password  String
  role      UserRole @default(USER)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // 关联关系
  organizations OrganizationMember[]
  assessments   Assessment[]
  apiKeys       ApiKey[]

  @@map("users")
}

// 用户角色枚举
enum UserRole {
  USER
  ADMIN
  SUPER_ADMIN
}

// 组织模型
model Organization {
  id          String             @id @default(cuid())
  name        String
  description String?
  type        OrganizationType
  website     String?
  logoUrl     String?
  settings    Json?              // 组织配置信息
  createdAt   DateTime           @default(now())
  updatedAt   DateTime           @updatedAt

  // 关联关系
  members         OrganizationMember[]
  assessments     Assessment[]
  externalSources ExternalDataSource[]

  @@map("organizations")
}

// 组织类型枚举
enum OrganizationType {
  NGO
  CHARITY
  FOUNDATION
  SOCIAL_ENTERPRISE
  COMMUNITY_GROUP
  OTHER
}

// 组织成员关联表
model OrganizationMember {
  id             String   @id @default(cuid())
  userId         String
  organizationId String
  role           MemberRole
  joinedAt       DateTime @default(now())

  // 关联关系
  user         User         @relation(fields: [userId], references: [id], onDelete: Cascade)
  organization Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)

  @@unique([userId, organizationId])
  @@map("organization_members")
}

// 成员角色枚举
enum MemberRole {
  OWNER
  ADMIN
  MEMBER
  VIEWER
}

// 评估模型
model Assessment {
  id             String           @id @default(cuid())
  organizationId String
  userId         String
  version        AssessmentVersion @default(STANDARD)
  status         AssessmentStatus @default(DRAFT)
  questionnaire  Json?            // 问卷数据
  responses      Json?            // 用户回答
  scores         Json?            // 评分结果
  report         Json?            // 分析报告
  metadata       Json?            // 元数据
  startedAt      DateTime?        // 开始时间
  completedAt    DateTime?        // 完成时间
  createdAt      DateTime         @default(now())
  updatedAt      DateTime         @updatedAt

  // 关联关系
  organization Organization    @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  user         User            @relation(fields: [userId], references: [id], onDelete: Cascade)
  processedData ProcessedData[]

  @@map("assessments")
}

// 评估版本枚举
enum AssessmentVersion {
  STANDARD
  PROFESSIONAL
}

// 评估状态枚举
enum AssessmentStatus {
  DRAFT
  IN_PROGRESS
  COMPLETED
  ARCHIVED
}

// 外部数据源模型（专业版功能）
model ExternalDataSource {
  id             String         @id @default(cuid())
  organizationId String
  type           DataSourceType
  name           String
  url            String?
  filePath       String?
  fileSize       Int?
  mimeType       String?
  status         DataSourceStatus @default(PENDING)
  metadata       Json?          // 数据源元信息
  createdAt      DateTime       @default(now())
  updatedAt      DateTime       @updatedAt

  // 关联关系
  organization  Organization    @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  processedData ProcessedData[]

  @@map("external_data_sources")
}

// 数据源类型枚举
enum DataSourceType {
  UPLOADED_FILE
  WEB_URL
  API_ENDPOINT
}

// 数据源状态枚举
enum DataSourceStatus {
  PENDING
  PROCESSING
  COMPLETED
  FAILED
  ARCHIVED
}

// 处理后数据模型
model ProcessedData {
  id               String    @id @default(cuid())
  assessmentId     String?
  dataSourceId     String
  content          String    // 提取的文本内容
  summary          String?   // 内容摘要
  keywords         String[]  // 关键词
  sentiment        Float?    // 情感分析得分
  confidence       Float?    // 置信度
  processingMethod String    // 处理方法
  metadata         Json?     // 处理元数据
  createdAt        DateTime  @default(now())
  updatedAt        DateTime  @updatedAt

  // 关联关系
  assessment Assessment?        @relation(fields: [assessmentId], references: [id], onDelete: Cascade)
  dataSource ExternalDataSource @relation(fields: [dataSourceId], references: [id], onDelete: Cascade)

  @@map("processed_data")
}

// API密钥模型
model ApiKey {
  id          String       @id @default(cuid())
  userId      String
  name        String
  keyHash     String       @unique // 存储哈希值，不存储明文
  permissions String[]     // 权限列表
  status      ApiKeyStatus @default(ACTIVE)
  expiresAt   DateTime?
  lastUsedAt  DateTime?
  createdAt   DateTime     @default(now())
  updatedAt   DateTime     @updatedAt

  // 关联关系
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("api_keys")
}

// API密钥状态枚举
enum ApiKeyStatus {
  ACTIVE
  INACTIVE
  REVOKED
}

// 安全事件模型
model SecurityEvent {
  id          String            @id @default(cuid())
  type        SecurityEventType
  severity    EventSeverity
  description String
  metadata    Json?             // 事件详细信息
  ipAddress   String?
  userAgent   String?
  userId      String?
  createdAt   DateTime          @default(now())

  @@map("security_events")
}

// 安全事件类型枚举
enum SecurityEventType {
  LOGIN_ATTEMPT
  LOGIN_SUCCESS
  LOGIN_FAILURE
  PASSWORD_CHANGE
  API_ACCESS
  DATA_EXPORT
  SUSPICIOUS_ACTIVITY
  SYSTEM_ERROR
}

// 事件严重程度枚举
enum EventSeverity {
  LOW
  MEDIUM
  HIGH
  CRITICAL
}