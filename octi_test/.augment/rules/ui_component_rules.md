---
type: "agent_requested"
description: "Example description"
---
# OCTI UI组件开发规则

## 适用场景
当开发前端组件、页面、表单等UI相关代码时自动应用此规则。

## 组件开发规范

### 函数式组件模板
```typescript
interface ComponentProps {
  // 明确定义props类型
}

export function ComponentName({ prop1, prop2 }: ComponentProps) {
  // 使用自定义Hooks处理业务逻辑
  const { data, loading, error } = useCustomHook();
  
  // 使用useCallback缓存函数
  const handleAction = useCallback(() => {
    // 处理逻辑
  }, [dependencies]);
  
  return (
    <div className="component-wrapper">
      {/* JSX内容 */}
    </div>
  );
}
```

## Tailwind CSS规范
- 使用语义化的class组合
- 优先使用Shadcn/ui组件
- 响应式设计必须考虑移动端

## 表单处理
```typescript
// 使用React Hook Form + Zod
const formSchema = z.object({
  organizationName: z.string().min(1).max(100),
  assessmentType: z.enum(['standard', 'professional']),
});

const form = useForm<z.infer<typeof formSchema>>({
  resolver: zodResolver(formSchema),
});
```

## 性能优化
- 使用React.memo优化重渲染
- 大型组件使用动态导入
- 图表组件设置ssr: false