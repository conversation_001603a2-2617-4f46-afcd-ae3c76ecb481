---
type: "agent_requested"
description: "Example description"
---
# OCTI 测试规则

## 适用场景
当编写测试代码或需要测试指导时自动应用此规则。

## 测试策略
- **单元测试**: Jest + React Testing Library
- **集成测试**: API路由测试
- **E2E测试**: 关键用户流程
- **覆盖率要求**: >80%

## 组件测试模板
```typescript
describe('ComponentName', () => {
  it('should render with initial props', () => {
    const mockProps = createMockProps();
    render(<ComponentName {...mockProps} />);
    
    expect(screen.getByText('Expected Text')).toBeInTheDocument();
  });
  
  it('should handle user interactions', async () => {
    const mockOnClick = jest.fn();
    render(<ComponentName onClick={mockOnClick} />);
    
    await user.click(screen.getByRole('button'));
    expect(mockOnClick).toHaveBeenCalled();
  });
});
```

## API测试模板
```typescript
describe('API Route', () => {
  it('should return valid response', async () => {
    const response = await request(app)
      .post('/api/v1/assessments')
      .send(validPayload)
      .expect(200);
    
    expect(response.body.success).toBe(true);
    expect(response.body.data).toBeDefined();
  });
});
```